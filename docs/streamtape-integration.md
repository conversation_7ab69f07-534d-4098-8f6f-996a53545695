# StreamTape集成文档

## 📋 概述

本文档描述了StreamTape视频上传服务的集成实现，支持与现有DoodStream服务的双重上传功能。

## 🎯 功能特性

### ✅ 已实现功能

1. **StreamTape客户端包** (`pkg/streamtape/`)
   - 完整的StreamTape API封装
   - 支持文件上传、连接测试
   - 错误处理和重试机制
   - 速率限制和性能优化

2. **双重上传支持**
   - 同时上传到DoodStream和StreamTape
   - 并行上传提高效率
   - 容错机制：至少一个成功即可
   - 智能结果处理

3. **配置系统更新**
   - 支持多种上传提供商：`imgbb`、`doodstream`、`streamtape`、`dual`
   - StreamTape API配置验证
   - 环境变量支持

4. **服务集成**
   - 文件处理服务支持双重上传
   - 自动上传服务支持多提供商
   - 数据库模型扩展存储多个播放链接

## 🔧 配置说明

### 基础配置

```yaml
file_processing:
  # 上传服务提供商选择
  upload_provider: "dual"  # imgbb/doodstream/streamtape/dual
  
  # DoodStream配置
  doodstream:
    api_key: "your_doodstream_api_key"
    base_url: "https://doodapi.co"
    timeout: 300
    max_retries: 3
  
  # StreamTape配置
  streamtape:
    api_login: "1a5ca4d7baa9d3e38863"
    api_key: "ll4YJX64JKtB7A"
    base_url: "https://api.streamtape.com"
    timeout: 300
    max_retries: 3
```

### 上传提供商说明

- **`imgbb`**: 传统分片上传模式
- **`doodstream`**: 仅使用DoodStream直接上传
- **`streamtape`**: 仅使用StreamTape直接上传
- **`dual`**: 同时上传到DoodStream和StreamTape（推荐）

## 🏗️ 架构设计

### 核心组件

1. **StreamTape客户端** (`pkg/streamtape/`)
   ```
   ├── client.go          # 主客户端实现
   ├── config.go          # 配置结构
   ├── upload.go          # 上传功能
   └── errors.go          # 错误处理
   ```

2. **文件处理服务扩展**
   ```
   ├── file_processing_service.go           # 主服务
   ├── file_processing_service_streamtape.go # StreamTape上传
   ├── file_processing_service_dual.go      # 双重上传逻辑
   └── file_processing_service_dual_core.go # 双重上传核心
   ```

3. **自动上传服务扩展**
   ```
   ├── auto_upload_service.go      # 主服务
   └── auto_upload_providers.go    # 多提供商支持
   ```

### 数据库模型

扩展了`DownloadTask`模型以支持多个播放链接：

```go
type DownloadTask struct {
    // 原有字段...
    PlayURL       string `json:"play_url"`       // 主要播放链接
    DoodStreamURL string `json:"doodstream_url"` // DoodStream播放链接
    StreamTapeURL string `json:"streamtape_url"` // StreamTape播放链接
}
```

## 🚀 使用方法

### 1. 配置设置

更新`config.yaml`文件：

```yaml
file_processing:
  upload_provider: "dual"
  streamtape:
    api_login: "your_api_login"
    api_key: "your_api_key"
    base_url: "https://api.streamtape.com"
    timeout: 300
    max_retries: 3
```

### 2. 启动服务

```bash
# 启动主服务
go run cmd/server/main.go

# 或使用Docker
docker-compose up -d
```

### 3. 测试功能

```bash
# 测试配置
go run cmd/test_config.go

# 测试StreamTape连接
go run cmd/test_streamtape.go

# 测试双重上传配置
go run cmd/test_dual_upload.go
```

## 📊 性能特性

### 并行上传

- 双重上传采用并行处理
- 两个服务商同时上传，提高效率
- 独立的错误处理，互不影响

### 容错机制

- 至少一个服务商成功即视为上传成功
- 智能结果选择：优先DoodStream，备选StreamTape
- 详细的错误日志和状态跟踪

### 性能优化

- 速率限制避免API限制
- 连接池复用提高效率
- 智能重试机制

## 🔍 API接口

### StreamTape客户端

```go
// 创建客户端
client := streamtape.NewClient(config)

// 测试连接
err := client.TestConnection()

// 上传文件
result, err := client.UploadFile("/path/to/video.mp4")
```

### 双重上传结果

```go
type DualUploadResult struct {
    DoodStreamResult *doodstream.UploadResult
    StreamTapeResult *streamtape.UploadResult
    DoodStreamError  string
    StreamTapeError  string
    SuccessCount     int
}
```

## 🐛 故障排除

### 常见问题

1. **StreamTape连接失败**
   - 检查API登录用户名和密钥
   - 验证网络连接
   - 确认API配额

2. **配置验证失败**
   - 确保`upload_provider`为有效值
   - 检查必需的配置字段
   - 验证配置文件格式

3. **双重上传部分失败**
   - 查看日志确定失败的服务商
   - 检查各服务商的API状态
   - 验证文件大小限制

### 日志分析

```bash
# 查看上传日志
grep "dual_upload" logs/app.log

# 查看StreamTape相关日志
grep "StreamTape" logs/app.log

# 查看错误日志
grep "ERROR" logs/app.log | grep -E "(streamtape|dual)"
```

## 🔮 未来扩展

### 计划功能

1. **更多视频服务商**
   - 支持更多视频托管服务
   - 可配置的服务商优先级

2. **智能上传策略**
   - 基于文件大小选择服务商
   - 动态负载均衡

3. **高级监控**
   - 上传成功率统计
   - 性能指标监控
   - 服务商健康检查

## 📝 更新日志

### v1.0.0 (2025-06-26)

- ✅ 实现StreamTape客户端包
- ✅ 添加双重上传功能
- ✅ 更新配置系统支持多提供商
- ✅ 扩展数据库模型
- ✅ 集成自动上传服务
- ✅ 添加测试工具和文档

## 🤝 贡献

如需贡献代码或报告问题，请：

1. 查看现有Issues
2. 创建详细的Bug报告或功能请求
3. 提交Pull Request时包含测试用例
4. 更新相关文档

---

**注意**: 本集成已完成核心功能实现，建议在生产环境使用前进行充分测试。