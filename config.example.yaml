# 应用配置示例文件
# 复制此文件为 config.yaml 并根据实际环境修改配置

app:
  name: "magnet-downloader"
  version: "1.0.0"
  env: "development"  # development, production

server:
  host: "0.0.0.0"
  port: 8081
  mode: "debug"  # debug, release
  read_timeout: 60
  write_timeout: 60

database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "your_password_here"
  dbname: "magnet_downloader"
  sslmode: "disable"
  max_open_conns: 100
  max_idle_conns: 10

redis:
  host: "localhost"
  port: 6379
  password: "your_redis_password"
  db: 0
  pool_size: 10

aria2:
  host: "localhost"
  port: 6800
  secret: "your_aria2_secret"
  timeout: 30

log:
  level: "info"  # debug, info, warn, error
  format: "json"  # json, text
  output: "stdout"  # stdout, file

jwt:
  secret: "your-jwt-secret-key-change-this-in-production"
  expires_in: 24  # hours

scheduler:
  enabled: true
  timezone: "UTC"

file_processing:
  enabled: true
  chunk_size_mb: 10
  max_concurrent_uploads: 3
  encryption_algorithm: "AES-256-GCM"
  encryption_enabled: false
  keep_original_files: true
  work_dir: "/tmp/file_processing"
  retry_attempts: 3
  auto_start_processing: false
  cleanup_after_days: 7
  upload_provider: "streamtape"  # imgbb, streamtape, streamhg
  
  imgbb:
    api_key: "your_imgbb_api_key"
    base_url: "https://api.imgbb.com/1"
    timeout: 30
    max_retries: 3
  
  imgur:
    client_id: "your_imgur_client_id"
    client_secret: "your_imgur_client_secret"
    redirect_uri: "http://localhost:8081/api/imgur/callback"
    base_url: "https://api.imgur.com/3"
    timeout: 30
    max_retries: 3
  
  streamtape:
    api_login: "your_streamtape_login"
    api_key: "your_streamtape_api_key"
    base_url: "https://api.streamtape.com"
    timeout: 60
    max_retries: 3
  
  streamhg:
    api_key: "your_streamhg_api_key"
    base_url: "https://api.streamhg.com"
    timeout: 60
    max_retries: 3
  
  auto_upload:
    enabled: false
    delay_seconds: 30
    max_concurrent: 2
    retry_attempts: 3
    cleanup_after_upload: false
  
  playlist:
    version: 3
    target_duration: 10
    media_sequence: 0
    allow_cache: true
    playlist_type: "VOD"
    output_dir: "/tmp/playlists"

# JAV功能配置
jav:
  enabled: true  # 是否启用JAV功能
  
  # 数据采集配置
  scraping:
    enabled: true
    timeout: 30  # 请求超时时间(秒)
    rate_limit: 2  # 请求间隔(秒)
    max_retries: 3  # 最大重试次数
    auto_merge: true  # 是否自动融合多源数据
    min_confidence: 0.7  # 最小可信度
    batch_size: 10  # 批量处理大小
    concurrent_limit: 5  # 并发限制
    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    proxy_enabled: false  # 是否启用代理
    proxy_url: ""  # 代理URL
    
    # 数据源配置
    sources:
      # JavBus - 主要数据源
      javbus:
        enabled: true
        base_url: "https://www.javbus.com"
        timeout: 30
        max_retries: 3
        priority: 9  # 优先级(1-10)
        weight: 0.4  # 权重(0.0-1.0)
        rate_limit: 2
        user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        headers:
          Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
          Accept-Language: "zh-CN,zh;q=0.9,en;q=0.8"
      
      # Javinizer - 辅助数据源
      javinizer:
        enabled: true
        base_url: "https://www.r18.com"
        timeout: 30
        max_retries: 3
        priority: 8
        weight: 0.3
        rate_limit: 3
        user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        headers:
          Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
      
      # JavSP - 辅助数据源
      javsp:
        enabled: true
        base_url: "https://javdb.com"
        timeout: 30
        max_retries: 3
        priority: 7
        weight: 0.2
        rate_limit: 3
        user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        headers:
          Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
      
      # JavDB - 可选数据源
      javdb:
        enabled: false
        base_url: "https://javdb.com"
        timeout: 30
        max_retries: 3
        priority: 6
        weight: 0.1
        rate_limit: 5
        user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
      
      # JavLibrary - 可选数据源
      javlibrary:
        enabled: false
        base_url: "https://www.javlibrary.com"
        timeout: 30
        max_retries: 3
        priority: 5
        weight: 0.1
        rate_limit: 5
        user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  
  # 下载配置
  download:
    enabled: true
    default_path: "/downloads/jav"  # 默认下载路径
    create_folder: true  # 是否为每部影片创建文件夹
    folder_template: "{studio}/{code} - {title}"  # 文件夹命名模板
    file_template: "{code} - {title}.{ext}"  # 文件命名模板
    prefer_quality: "1080p"  # 首选清晰度: 720p, 1080p, 4K, auto
    prefer_subtitle: true  # 是否优先选择有字幕的
    subtitle_lang: "chinese"  # 首选字幕语言: chinese, english, japanese, auto, none
    auto_start: false  # 是否自动开始下载
    max_concurrent: 3  # 最大并发下载数
    speed_limit: 0  # 速度限制(KB/s, 0为不限制)
    auto_upload: false  # 是否自动上传
    upload_platform: "streamtape"  # 上传平台
    clean_after_upload: false  # 上传后是否清理本地文件
  
  # 存储配置
  storage:
    cover_path: "/storage/jav/covers"  # 封面存储路径
    poster_path: "/storage/jav/posters"  # 海报存储路径
    actor_avatar_path: "/storage/jav/actors"  # 演员头像存储路径
    max_cover_size: 5242880  # 最大封面文件大小(5MB)
    max_poster_size: 10485760  # 最大海报文件大小(10MB)
    image_quality: 85  # 图片质量(1-100)
    image_format: "jpg"  # 图片格式: jpg, jpeg, png, webp
    enable_webp: true  # 是否启用WebP格式
    enable_thumbnail: true  # 是否生成缩略图
    thumbnail_size: 300  # 缩略图大小(像素)
  
  # 处理配置
  processing:
    enable_metadata: true  # 是否启用元数据处理
    enable_thumbnail: true  # 是否生成缩略图
    enable_preview: false  # 是否生成预览
    thumbnail_count: 10  # 缩略图数量
    thumbnail_width: 320  # 缩略图宽度
    thumbnail_height: 180  # 缩略图高度
    preview_duration: 30  # 预览时长(秒)
    preview_quality: "medium"  # 预览质量: low, medium, high
    ffmpeg_path: "/usr/bin/ffmpeg"  # FFmpeg路径
    ffprobe_path: "/usr/bin/ffprobe"  # FFprobe路径
    temp_path: "/tmp/jav_processing"  # 临时文件路径
    clean_temp_files: true  # 是否清理临时文件
  
  # API配置
  api:
    enable_public_api: true  # 是否启用公开API
    enable_search: true  # 是否启用搜索功能
    enable_download: true  # 是否启用下载功能
    enable_scraping: false  # 是否启用采集功能(仅管理员)
    max_search_results: 100  # 最大搜索结果数
    max_page_size: 50  # 最大分页大小
    cache_enabled: true  # 是否启用缓存
    cache_ttl: 3600  # 缓存TTL(秒)
    rate_limit_enabled: true  # 是否启用速率限制
    rate_limit_rpm: 1000  # 每分钟请求限制
    allowed_origins:  # 允许的跨域来源
      - "http://localhost:3000"
      - "https://yourdomain.com"
    require_auth: false  # 是否需要认证
    admin_only: false  # 是否仅管理员可用

# 环境变量覆盖说明:
# 大部分配置项都可以通过环境变量覆盖，格式为：
# APP_NAME, SERVER_PORT, DATABASE_HOST, JAV_ENABLED 等
# 嵌套配置使用下划线分隔，如：JAV_SCRAPING_ENABLED, JAV_DOWNLOAD_DEFAULT_PATH