# JAV API 项目 Makefile

.PHONY: scraper server build clean help

# 默认目标
help:
	@echo "🎬 JAV API 项目命令"
	@echo "===================="
	@echo "make scraper    - 启动完整采集器"
	@echo "make server     - 启动主服务器"
	@echo "make build      - 编译所有程序"
	@echo "make clean      - 清理编译文件"
	@echo "make help       - 显示帮助信息"

# 启动完整采集器
scraper:
	@echo "🚀 启动JAV完整采集器..."
	@go run \
		cmd/complete_javbus_scraper/main.go \
		cmd/complete_javbus_scraper/helpers.go \
		cmd/complete_javbus_scraper/helpers_extended.go \
		cmd/complete_javbus_scraper/magnet_selector.go \
		cmd/complete_javbus_scraper/subtitle_selector.go

# 启动主服务器
server:
	@echo "🖥️  启动主服务器..."
	@go run cmd/server/main.go

# 编译完整采集器
build-scraper:
	@echo "🔨 编译完整采集器..."
	@go build -o bin/scraper \
		cmd/complete_javbus_scraper/main.go \
		cmd/complete_javbus_scraper/helpers.go \
		cmd/complete_javbus_scraper/helpers_extended.go \
		cmd/complete_javbus_scraper/magnet_selector.go \
		cmd/complete_javbus_scraper/subtitle_selector.go

# 编译主服务器
build-server:
	@echo "🔨 编译主服务器..."
	@go build -o bin/server cmd/server/main.go

# 编译所有程序
build: build-scraper build-server
	@echo "✅ 编译完成"

# 运行编译后的采集器
run-scraper: build-scraper
	@echo "🚀 运行编译后的采集器..."
	@./bin/scraper

# 运行编译后的服务器
run-server: build-server
	@echo "🖥️  运行编译后的服务器..."
	@./bin/server

# 清理编译文件
clean:
	@echo "🧹 清理编译文件..."
	@rm -rf bin/
	@echo "✅ 清理完成"

# 创建bin目录
bin:
	@mkdir -p bin