package streamtape

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"magnet-downloader/pkg/logger"
)

// UploadFile 智能上传文件到StreamTape（自动选择最佳上传方式）
func (c *Client) UploadFile(filePath string) (*UploadResult, error) {
	// 验证文件路径
	if filePath == "" {
		return nil, NewValidationError("文件路径不能为空", "filePath is empty")
	}

	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		return nil, NewFileError(ErrorTypeFileAccess, "文件不存在", err)
	}
	if err != nil {
		return nil, NewFileError(ErrorTypeFileAccess, "无法访问文件", err)
	}

	// 检查文件大小
	if fileInfo.Size() == 0 {
		return nil, NewFileError(ErrorTypeFileSize, "文件大小为0", fmt.Errorf("file size is 0"))
	}

	// StreamTape支持最大15GB文件
	maxSize := int64(15 * 1024 * 1024 * 1024) // 15GB
	if fileInfo.Size() > maxSize {
		return nil, NewFileError(ErrorTypeFileSize,
			fmt.Sprintf("文件过大，StreamTape最大支持15GB，当前文件%.2fGB",
				float64(fileInfo.Size())/(1024*1024*1024)),
			fmt.Errorf("file too large"))
	}

	originalFilename := filepath.Base(filePath)
	// 应用智能文件重命名
	filename := smartRenameVideoFile(originalFilename)
	logger.Infof("开始智能上传文件到StreamTape: %s (原名: %s, size: %d bytes)", filename, originalFilename, fileInfo.Size())

	// 智能上传策略：
	// 1. 小文件(<50MB): 缓冲上传（最佳兼容性）
	// 2. 大文件(≥50MB): 零内存流式上传（节省内存）
	bufferThreshold := int64(50 * 1024 * 1024) // 50MB

	if fileInfo.Size() < bufferThreshold {
		logger.Infof("小文件(%.2f MB)，使用缓冲上传（确保兼容性）", float64(fileInfo.Size())/(1024*1024))
		return c.performBufferedUpload(filePath, filename, "")
	} else {
		logger.Infof("大文件(%.2f MB)，使用零内存流式上传（节省内存）", float64(fileInfo.Size())/(1024*1024))
		return c.performZeroMemoryUpload(filePath, filename, "")
	}
}

// performUpload 执行上传
func (c *Client) performUpload(filePath, filename, folderID string) (*UploadResult, error) {
	var lastErr error

	// 重试上传
	for attempt := 0; attempt < c.config.MaxRetries; attempt++ {
		if attempt > 0 {
			waitTime := c.calculateBackoff(attempt, IsRateLimitError(lastErr))
			logger.Debugf("重试StreamTape上传，等待 %v (attempt %d/%d)", waitTime, attempt+1, c.config.MaxRetries)
			time.Sleep(waitTime)
		}

		// 1. 获取上传服务器URL
		serverURL, err := c.GetUploadServer(folderID)
		if err != nil {
			lastErr = err
			logger.Warnf("获取StreamTape上传服务器失败 (attempt %d/%d): %v", attempt+1, c.config.MaxRetries, err)
			continue
		}

		// 2. 执行文件上传
		result, err := c.performFileUpload(serverURL, filePath, filename)
		if err == nil {
			logger.Infof("StreamTape上传成功: %s", filename)
			return result, nil
		}

		lastErr = err
		logger.Warnf("StreamTape上传失败 (attempt %d/%d): %v", attempt+1, c.config.MaxRetries, err)
	}

	if lastErr != nil {
		return nil, NewUploadError("StreamTape上传失败", lastErr.Error(), 500)
	}
	return nil, NewUploadError("StreamTape上传失败", "unknown error", 500)
}

// performFileUpload 执行文件上传（零内存流式上传）
func (c *Client) performFileUpload(serverURL, filePath, filename string) (*UploadResult, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return nil, NewFileError(ErrorTypeFileAccess, "打开文件失败", err)
	}
	defer file.Close()

	// 获取文件信息
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, NewFileError(ErrorTypeFileAccess, "获取文件信息失败", err)
	}

	// 应用速率限制
	streamTapeRateLimit()

	logger.Infof("上传文件到StreamTape: %s (size: %.2f MB) -> %s",
		filename, float64(fileInfo.Size())/(1024*1024), serverURL)

	// 🚀 使用零内存流式上传
	return c.performStreamingUpload(serverURL, file, filename, fileInfo.Size())
}

// performStreamingUpload 执行零内存流式上传
func (c *Client) performStreamingUpload(serverURL string, file *os.File, filename string, fileSize int64) (*UploadResult, error) {
	// 🔧 修复：预先计算multipart数据的总大小
	contentType, totalSize, err := c.calculateMultipartSize(filename, fileSize)
	if err != nil {
		return nil, NewUploadError("计算multipart大小失败", err.Error(), 500)
	}

	// 创建管道用于流式传输
	pipeReader, pipeWriter := io.Pipe()

	// 在goroutine中写入multipart数据
	go func() {
		defer pipeWriter.Close()

		// 创建multipart writer
		writer := multipart.NewWriter(pipeWriter)
		defer writer.Close()

		// 创建文件字段
		part, err := writer.CreateFormFile("file1", filename)
		if err != nil {
			logger.Errorf("创建文件字段失败: %v", err)
			pipeWriter.CloseWithError(err)
			return
		}

		// 流式复制文件内容，带进度监控
		if err := c.copyWithProgress(part, file, filename, fileSize); err != nil {
			logger.Errorf("文件复制失败: %v", err)
			pipeWriter.CloseWithError(err)
			return
		}
	}()

	// 创建请求
	req, err := http.NewRequest("POST", serverURL, pipeReader)
	if err != nil {
		return nil, NewUploadError("创建上传请求失败", err.Error(), 500)
	}

	req.Header.Set("Content-Type", contentType)
	req.Header.Set("User-Agent", "magnet-downloader/1.0")
	req.ContentLength = totalSize // 🔧 修复：设置确切的内容长度

	// 🔧 强制禁用chunked encoding
	req.TransferEncoding = []string{"identity"}

	// 执行请求
	logger.Infof("执行StreamTape上传请求到: %s", serverURL)
	resp, err := c.httpClient.Do(req)
	if err != nil {
		logger.Errorf("StreamTape HTTP请求失败: %v", err)
		return nil, NewNetworkError("StreamTape HTTP请求失败", fmt.Sprintf("请求URL: %s, 错误: %v", serverURL, err))
	}
	defer resp.Body.Close()

	// 处理响应
	return c.handleUploadResponse(resp, filename)
}

// handleUploadResponse 处理上传响应
func (c *Client) handleUploadResponse(resp *http.Response, filename string) (*UploadResult, error) {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, NewNetworkError("读取上传响应失败", err.Error())
	}

	// 记录响应详情用于调试
	logger.Infof("StreamTape上传响应 (HTTP %d): %s", resp.StatusCode, string(body))
	logger.Debugf("StreamTape响应头: %+v", resp.Header)

	// 检查HTTP状态码
	if resp.StatusCode != 200 {
		logger.Errorf("StreamTape上传HTTP错误: %d, 响应: %s", resp.StatusCode, string(body))

		// 特殊处理HTTP 500错误 - 通常是StreamTape服务器临时问题
		if resp.StatusCode == 500 {
			// 尝试解析错误响应
			var errorResp map[string]interface{}
			if err := json.Unmarshal(body, &errorResp); err == nil {
				if msg, exists := errorResp["msg"]; exists {
					msgStr := fmt.Sprintf("%v", msg)
					if strings.Contains(msgStr, "read() failed: closed") {
						return nil, NewAPIError("StreamTape服务器处理文件时出错",
							"文件上传完成但服务器处理失败，这通常是临时问题，请稍后重试",
							resp.StatusCode)
					}
				}
			}
		}

		return nil, NewAPIError("上传失败",
			fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			resp.StatusCode)
	}

	// 检查响应是否为空
	if len(body) == 0 {
		logger.Errorf("StreamTape返回空响应")
		return nil, NewAPIError("上传失败", "服务器返回空响应", 200)
	}

	// 根据StreamTape API文档，上传完成后可能返回不同格式的响应
	// 1. 成功上传后的文件信息响应
	// 2. 简单的状态响应
	// 3. HTML响应（某些情况下）

	// 首先检查是否为HTML响应
	if strings.Contains(string(body), "<html") || strings.Contains(string(body), "<!DOCTYPE") {
		return c.handleHTMLResponse(string(body), filename)
	}

	// 尝试解析JSON响应
	var genericResp map[string]interface{}
	if err := json.Unmarshal(body, &genericResp); err != nil {
		// 如果不是JSON，可能是纯文本响应
		return c.handleTextResponse(string(body), filename)
	}

	logger.Debugf("StreamTape响应结构: %+v", genericResp)

	// 检查是否有status字段
	_, hasStatus := genericResp["status"]
	if hasStatus {
		// 标准API响应格式
		return c.parseAPIResponse(genericResp, filename)
	}

	// 检查是否直接包含文件信息
	if url, hasURL := genericResp["url"]; hasURL {
		return c.parseDirectFileResponse(genericResp, filename, url.(string))
	}

	// 检查是否包含link字段（旧格式）
	if link, hasLink := genericResp["link"]; hasLink {
		return c.parseDirectFileResponse(genericResp, filename, link.(string))
	}

	// 如果都不匹配，记录详细信息并返回错误
	logger.Errorf("未知的StreamTape响应格式: %+v", genericResp)
	return nil, NewAPIError("未知的响应格式", fmt.Sprintf("响应: %+v", genericResp), resp.StatusCode)
}

// convertToPlayURL 将下载链接转换为播放链接
func (c *Client) convertToPlayURL(downloadURL string) string {
	// StreamTape下载链接格式: https://streamtape.com/v/{id}/{filename}
	// 播放链接格式: https://streamtape.com/e/{id}

	if strings.Contains(downloadURL, "/v/") {
		// 提取文件ID
		parts := strings.Split(downloadURL, "/v/")
		if len(parts) > 1 {
			idPart := strings.Split(parts[1], "/")[0]
			return fmt.Sprintf("https://streamtape.com/e/%s", idPart)
		}
	}

	// 如果无法转换，返回原链接
	return downloadURL
}

// handleHTMLResponse 处理HTML响应
func (c *Client) handleHTMLResponse(htmlBody, filename string) (*UploadResult, error) {
	// StreamTape有时返回HTML页面，通常包含JavaScript重定向或文件信息
	logger.Warnf("StreamTape返回HTML响应，可能需要解析页面内容")

	// 尝试从HTML中提取文件ID或链接
	// 查找常见的模式，如 streamtape.com/v/ID 或 streamtape.com/e/ID
	if strings.Contains(htmlBody, "streamtape.com/v/") {
		// 使用正则表达式提取文件ID
		start := strings.Index(htmlBody, "streamtape.com/v/")
		if start != -1 {
			start += len("streamtape.com/v/")
			end := start
			for end < len(htmlBody) && (htmlBody[end] != '"' && htmlBody[end] != '\'' && htmlBody[end] != ' ' && htmlBody[end] != '/') {
				end++
			}
			if end > start {
				fileID := htmlBody[start:end]
				// 如果包含文件名，去掉文件名部分
				if slashIndex := strings.Index(fileID, "/"); slashIndex != -1 {
					fileID = fileID[:slashIndex]
				}

				downloadURL := fmt.Sprintf("https://streamtape.com/v/%s/%s", fileID, filename)
				playURL := fmt.Sprintf("https://streamtape.com/e/%s", fileID)

				return &UploadResult{
					Success:  true,
					URL:      downloadURL,
					PlayURL:  playURL,
					FileCode: fileID,
					Size:     0, // 无法从HTML获取大小
					Title:    filename,
					CanPlay:  true,
				}, nil
			}
		}
	}

	return nil, NewAPIError("无法解析HTML响应", "HTML响应中未找到有效的文件信息", 200)
}

// handleTextResponse 处理纯文本响应
func (c *Client) handleTextResponse(textBody, filename string) (*UploadResult, error) {
	logger.Warnf("StreamTape返回纯文本响应: %s", textBody)

	// 检查是否包含错误信息
	if strings.Contains(strings.ToLower(textBody), "error") ||
		strings.Contains(strings.ToLower(textBody), "fail") {
		return nil, NewAPIError("上传失败", textBody, 400)
	}

	// 检查是否包含成功信息或文件链接
	if strings.Contains(textBody, "streamtape.com") {
		// 尝试提取链接
		lines := strings.Split(textBody, "\n")
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if strings.Contains(line, "streamtape.com/v/") {
				return c.parseDirectFileResponse(map[string]interface{}{
					"url": line,
				}, filename, line)
			}
		}
	}

	return nil, NewAPIError("无法解析文本响应", textBody, 200)
}

// parseAPIResponse 解析标准API响应
func (c *Client) parseAPIResponse(genericResp map[string]interface{}, filename string) (*UploadResult, error) {
	// 检查状态
	status, ok := genericResp["status"]
	if !ok {
		return nil, NewAPIError("响应中缺少status字段", fmt.Sprintf("响应: %+v", genericResp), 500)
	}

	var statusCode int
	switch v := status.(type) {
	case float64:
		statusCode = int(v)
	case int:
		statusCode = v
	default:
		return nil, NewAPIError("status字段格式错误", fmt.Sprintf("status: %v", status), 500)
	}

	if statusCode != 200 {
		msg, _ := genericResp["msg"].(string)
		return nil, NewAPIError("StreamTape上传失败", msg, statusCode)
	}

	// 检查result字段
	result, ok := genericResp["result"]
	if !ok {
		return nil, NewAPIError("响应中缺少result字段", fmt.Sprintf("响应: %+v", genericResp), 500)
	}

	resultMap, ok := result.(map[string]interface{})
	if !ok {
		return nil, NewAPIError("result字段格式错误", fmt.Sprintf("result: %v", result), 500)
	}

	return c.parseResultMap(resultMap, filename)
}

// parseDirectFileResponse 解析直接文件响应
func (c *Client) parseDirectFileResponse(genericResp map[string]interface{}, filename, fileURL string) (*UploadResult, error) {
	return c.parseResultMap(genericResp, filename)
}

// parseResultMap 解析结果映射
func (c *Client) parseResultMap(resultMap map[string]interface{}, filename string) (*UploadResult, error) {
	// 提取关键信息
	var downloadURL, linkID string
	var fileSize int64

	// StreamTape可能使用不同的字段名
	// 尝试多种可能的字段名
	urlFields := []string{"url", "link", "download_url"}
	for _, field := range urlFields {
		if url, exists := resultMap[field]; exists {
			downloadURL, _ = url.(string)
			if downloadURL != "" {
				break
			}
		}
	}

	// 尝试多种可能的ID字段名
	idFields := []string{"id", "linkid", "file_id", "fileid"}
	for _, field := range idFields {
		if id, exists := resultMap[field]; exists {
			linkID, _ = id.(string)
			if linkID != "" {
				break
			}
		}
	}

	// 尝试获取文件大小
	if size, exists := resultMap["size"]; exists {
		switch v := size.(type) {
		case string:
			if parsed, err := strconv.ParseInt(v, 10, 64); err == nil {
				fileSize = parsed
			}
		case float64:
			fileSize = int64(v)
		case int64:
			fileSize = v
		case int:
			fileSize = int64(v)
		}
	}

	if downloadURL == "" {
		return nil, NewAPIError("响应中缺少下载链接", fmt.Sprintf("result: %+v", resultMap), 500)
	}

	// 构建播放URL
	playURL := c.convertToPlayURL(downloadURL)

	// 构建结果
	uploadResult := &UploadResult{
		Success:  true,
		URL:      downloadURL,
		PlayURL:  playURL,
		FileCode: linkID,
		Size:     fileSize,
		Title:    filename,
		CanPlay:  true, // 假设可以播放
	}

	logger.Infof("StreamTape上传成功: %s -> %s", filename, uploadResult.PlayURL)
	return uploadResult, nil
}

// parseGenericResponse 解析通用响应格式（保持向后兼容）
func (c *Client) parseGenericResponse(genericResp map[string]interface{}, filename string) (*UploadResult, error) {
	// 直接调用新的API响应解析方法
	return c.parseAPIResponse(genericResp, filename)
}

// copyWithProgress 带进度监控的文件复制
func (c *Client) copyWithProgress(dst io.Writer, src io.Reader, filename string, totalSize int64) error {
	const bufferSize = 32 * 1024 // 32KB缓冲区
	buffer := make([]byte, bufferSize)

	var written int64
	lastLogTime := time.Now()

	logger.Infof("零内存上传请求: Content-Length=%d bytes", totalSize)

	for {
		n, err := src.Read(buffer)
		if n > 0 {
			_, writeErr := dst.Write(buffer[:n])
			if writeErr != nil {
				return writeErr
			}
			written += int64(n)

			// 每5秒记录一次进度
			if time.Since(lastLogTime) >= 5*time.Second {
				progress := float64(written) / float64(totalSize) * 100
				writtenMB := float64(written) / (1024 * 1024)
				totalMB := float64(totalSize) / (1024 * 1024)

				logger.Infof("零内存上传进度: %s - %.1f%% (%.2f MB / %.2f MB)",
					filename, progress, writtenMB, totalMB)
				lastLogTime = time.Now()
			}
		}

		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}
	}

	logger.Infof("零内存上传完成: %s (%.2f MB)", filename, float64(written)/(1024*1024))
	return nil
}

// calculateMultipartSize 计算multipart数据的总大小
func (c *Client) calculateMultipartSize(filename string, fileSize int64) (contentType string, totalSize int64, err error) {
	// 创建临时buffer来计算multipart头部和结束边界大小
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 创建文件字段（只写头部，不写内容）
	part, err := writer.CreateFormFile("file1", filename)
	if err != nil {
		return "", 0, err
	}

	// 获取Content-Type
	contentType = writer.FormDataContentType()

	// 记录头部大小（在写入文件内容之前）
	headerSize := int64(buf.Len())

	// 写入一个字节来模拟文件内容（这样可以准确计算边界）
	part.Write([]byte("x"))

	// 关闭writer以获取完整的multipart结构
	writer.Close()

	// 计算总的multipart结构大小（包含头部、1字节内容、结束边界）
	totalMultipartSize := int64(buf.Len())

	// 实际总大小 = 头部 + 实际文件大小 + 结束边界
	// 结束边界大小 = 总multipart大小 - 头部大小 - 1字节内容
	endBoundarySize := totalMultipartSize - headerSize - 1

	totalSize = headerSize + fileSize + endBoundarySize

	logger.Debugf("Multipart大小计算: 头部=%d, 文件=%d, 结束边界=%d, 总计=%d",
		headerSize, fileSize, endBoundarySize, totalSize)

	return contentType, totalSize, nil
}

// UploadFileBuffered 使用缓冲方式上传文件（兼容性更好）
func (c *Client) UploadFileBuffered(filePath string) (*UploadResult, error) {
	// 验证文件路径
	if filePath == "" {
		return nil, NewValidationError("文件路径不能为空", "filePath is empty")
	}

	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		return nil, NewFileError(ErrorTypeFileAccess, "文件不存在", err)
	}
	if err != nil {
		return nil, NewFileError(ErrorTypeFileAccess, "无法访问文件", err)
	}

	// 检查文件大小
	if fileInfo.Size() == 0 {
		return nil, NewFileError(ErrorTypeFileSize, "文件大小为0", fmt.Errorf("file size is 0"))
	}

	// StreamTape支持最大15GB文件
	maxSize := int64(15 * 1024 * 1024 * 1024) // 15GB
	if fileInfo.Size() > maxSize {
		return nil, NewFileError(ErrorTypeFileSize,
			fmt.Sprintf("文件过大，StreamTape最大支持15GB，当前文件%.2fGB",
				float64(fileInfo.Size())/(1024*1024*1024)),
			fmt.Errorf("file too large"))
	}

	filename := filepath.Base(filePath)
	logger.Infof("开始缓冲上传文件到StreamTape: %s (size: %d bytes)", filename, fileInfo.Size())

	// 执行缓冲上传
	return c.performBufferedUpload(filePath, filename, "")
}

// performBufferedUpload 执行缓冲上传（兼容性更好）
func (c *Client) performBufferedUpload(filePath, filename, folderID string) (*UploadResult, error) {
	var lastErr error

	// 重试上传
	for attempt := 0; attempt < c.config.MaxRetries; attempt++ {
		if attempt > 0 {
			waitTime := c.calculateBackoff(attempt, IsRateLimitError(lastErr))
			logger.Debugf("重试StreamTape缓冲上传，等待 %v (attempt %d/%d)", waitTime, attempt+1, c.config.MaxRetries)
			time.Sleep(waitTime)
		}

		// 1. 获取上传服务器URL
		serverURL, err := c.GetUploadServer(folderID)
		if err != nil {
			lastErr = err
			logger.Warnf("获取StreamTape上传服务器失败 (attempt %d/%d): %v", attempt+1, c.config.MaxRetries, err)
			continue
		}

		// 2. 执行缓冲文件上传
		result, err := c.performBufferedFileUpload(serverURL, filePath, filename)
		if err == nil {
			logger.Infof("StreamTape缓冲上传成功: %s", filename)
			return result, nil
		}

		lastErr = err
		logger.Warnf("StreamTape缓冲上传失败 (attempt %d/%d): %v", attempt+1, c.config.MaxRetries, err)
	}

	return nil, NewUploadError("StreamTape上传失败",
		fmt.Sprintf("所有重试都失败了，最后错误: %v", lastErr), 500)
}

// performZeroMemoryUpload 执行零内存上传（实验性）
func (c *Client) performZeroMemoryUpload(filePath, filename, folderID string) (*UploadResult, error) {
	var lastErr error

	// 重试上传
	for attempt := 0; attempt < c.config.MaxRetries; attempt++ {
		if attempt > 0 {
			waitTime := c.calculateBackoff(attempt, IsRateLimitError(lastErr))
			logger.Debugf("重试StreamTape零内存上传，等待 %v (attempt %d/%d)", waitTime, attempt+1, c.config.MaxRetries)
			time.Sleep(waitTime)
		}

		// 1. 获取上传服务器URL
		serverURL, err := c.GetUploadServer(folderID)
		if err != nil {
			lastErr = err
			logger.Warnf("获取StreamTape上传服务器失败 (attempt %d/%d): %v", attempt+1, c.config.MaxRetries, err)
			continue
		}

		// 2. 执行零内存流式上传
		result, err := c.performZeroMemoryFileUpload(serverURL, filePath, filename)
		if err == nil {
			logger.Infof("StreamTape零内存上传成功: %s", filename)
			return result, nil
		}

		lastErr = err
		logger.Warnf("StreamTape零内存上传失败 (attempt %d/%d): %v", attempt+1, c.config.MaxRetries, err)
	}

	return nil, NewUploadError("StreamTape零内存上传失败",
		fmt.Sprintf("所有重试都失败了，最后错误: %v", lastErr), 500)
}

// performBufferedFileUpload 执行缓冲文件上传
func (c *Client) performBufferedFileUpload(serverURL, filePath, filename string) (*UploadResult, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return nil, NewFileError(ErrorTypeFileAccess, "打开文件失败", err)
	}
	defer file.Close()

	// 获取文件信息
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, NewFileError(ErrorTypeFileAccess, "获取文件信息失败", err)
	}

	// 应用速率限制
	streamTapeRateLimit()

	logger.Infof("缓冲上传文件到StreamTape: %s (size: %.2f MB) -> %s",
		filename, float64(fileInfo.Size())/(1024*1024), serverURL)

	// 使用传统的内存缓冲上传
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 创建文件字段
	part, err := writer.CreateFormFile("file1", filename)
	if err != nil {
		return nil, NewUploadError("创建文件字段失败", err.Error(), 500)
	}

	// 复制文件内容到buffer
	_, err = io.Copy(part, file)
	if err != nil {
		return nil, NewUploadError("复制文件内容失败", err.Error(), 500)
	}

	// 关闭writer
	writer.Close()

	logger.Infof("缓冲上传数据准备完成: %d bytes", buf.Len())

	// 创建请求
	req, err := http.NewRequest("POST", serverURL, &buf)
	if err != nil {
		return nil, NewUploadError("创建上传请求失败", err.Error(), 500)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("User-Agent", "magnet-downloader/1.0")

	// 执行请求
	logger.Infof("执行StreamTape缓冲上传请求到: %s", serverURL)
	resp, err := c.httpClient.Do(req)
	if err != nil {
		logger.Errorf("StreamTape HTTP请求失败: %v", err)
		return nil, NewNetworkError("StreamTape HTTP请求失败", fmt.Sprintf("请求URL: %s, 错误: %v", serverURL, err))
	}
	defer resp.Body.Close()

	// 处理响应
	return c.handleUploadResponse(resp, filename)
}

// performZeroMemoryFileUpload 执行零内存文件上传
func (c *Client) performZeroMemoryFileUpload(serverURL, filePath, filename string) (*UploadResult, error) {
	// 应用速率限制
	streamTapeRateLimit()

	logger.Infof("零内存上传文件到StreamTape: %s -> %s", filename, serverURL)

	// 创建流式上传器
	uploader := NewStreamingUploader(c.config.Timeout)

	// 执行流式上传
	return uploader.UploadFileStreaming(serverURL, filePath, filename, c)
}

// UploadFileWithFolder 上传文件到指定文件夹
func (c *Client) UploadFileWithFolder(filePath, folderID string) (*UploadResult, error) {
	originalFilename := filepath.Base(filePath)
	// 应用智能文件重命名
	filename := smartRenameVideoFile(originalFilename)
	return c.performUpload(filePath, filename, folderID)
}

// smartRenameVideoFile 智能重命名视频文件
// 将所有视频文件重命名为 getav.net@xxx.mp4/mkv 格式
func smartRenameVideoFile(originalFilename string) string {
	// 如果文件名已经以 getav.net@ 开头，保持原样
	if strings.HasPrefix(originalFilename, "getav.net@") {
		logger.Debugf("文件名已经是getav.net格式，保持原样: %s", originalFilename)
		return originalFilename
	}

	var baseFilename string

	// 检查文件名是否包含@符号
	if strings.Contains(originalFilename, "@") {
		// 分割文件名，取@后面的部分
		parts := strings.SplitN(originalFilename, "@", 2)
		if len(parts) == 2 {
			domainPart := parts[0]
			filenamePart := parts[1]

			// 检查域名部分是否包含域名格式
			domainPattern := regexp.MustCompile(`^[a-zA-Z0-9\-]+\.(com|net|org|cn|jp|tv|xxx|info|biz)$`)
			if domainPattern.MatchString(domainPart) {
				// 是域名格式，使用@后面的部分
				baseFilename = filenamePart
				logger.Debugf("检测到域名格式，提取文件名部分: %s", baseFilename)
			} else {
				// 不是域名格式，使用整个文件名
				baseFilename = originalFilename
			}
		} else {
			// 分割失败，使用整个文件名
			baseFilename = originalFilename
		}
	} else {
		// 没有@符号，直接使用原文件名
		baseFilename = originalFilename
	}

	// 生成新的文件名
	newFilename := fmt.Sprintf("getav.net@%s", baseFilename)

	logger.Infof("智能重命名视频文件: %s -> %s", originalFilename, newFilename)
	return newFilename
}
