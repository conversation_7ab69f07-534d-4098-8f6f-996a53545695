package streamtape

// ErrorType 错误类型
type ErrorType string

const (
	ErrorTypeValidation ErrorType = "validation"
	ErrorTypeAPI        ErrorType = "api"
	ErrorTypeNetwork    ErrorType = "network"
	ErrorTypeFileAccess ErrorType = "file_access"
	ErrorTypeFileSize   ErrorType = "file_size"
	ErrorTypeUpload     ErrorType = "upload"
)

// StreamTapeError StreamTape错误接口
type StreamTapeError interface {
	error
	Type() ErrorType
	Code() int
	Details() string
}

// BaseError 基础错误结构
type BaseError struct {
	ErrorType ErrorType `json:"error_type"`
	Message   string    `json:"message"`
	Detail    string    `json:"detail"`
	ErrorCode int       `json:"error_code"`
}

func (e *BaseError) Error() string {
	return e.Message
}

func (e *BaseError) Type() ErrorType {
	return e.ErrorType
}

func (e *BaseError) Code() int {
	return e.ErrorCode
}

func (e *BaseError) Details() string {
	return e.Detail
}

// NewValidationError 创建验证错误
func NewValidationError(message, detail string) StreamTapeError {
	return &BaseError{
		ErrorType: ErrorTypeValidation,
		Message:   message,
		Detail:    detail,
		ErrorCode: 400,
	}
}

// NewAPIError 创建API错误
func NewAPIError(message, detail string, code int) StreamTapeError {
	return &BaseError{
		ErrorType: ErrorTypeAPI,
		Message:   message,
		Detail:    detail,
		ErrorCode: code,
	}
}

// NewNetworkError 创建网络错误
func NewNetworkError(message, detail string) StreamTapeError {
	return &BaseError{
		ErrorType: ErrorTypeNetwork,
		Message:   message,
		Detail:    detail,
		ErrorCode: 500,
	}
}

// NewFileError 创建文件错误
func NewFileError(errorType ErrorType, message string, err error) StreamTapeError {
	detail := ""
	if err != nil {
		detail = err.Error()
	}

	code := 500
	if errorType == ErrorTypeFileAccess {
		code = 404
	} else if errorType == ErrorTypeFileSize {
		code = 413
	}

	return &BaseError{
		ErrorType: errorType,
		Message:   message,
		Detail:    detail,
		ErrorCode: code,
	}
}

// NewUploadError 创建上传错误
func NewUploadError(message, detail string, code int) StreamTapeError {
	return &BaseError{
		ErrorType: ErrorTypeUpload,
		Message:   message,
		Detail:    detail,
		ErrorCode: code,
	}
}

// IsRetryableError 判断错误是否可重试
func IsRetryableError(err error) bool {
	if streamTapeErr, ok := err.(StreamTapeError); ok {
		switch streamTapeErr.Type() {
		case ErrorTypeNetwork:
			return true
		case ErrorTypeAPI:
			// 5xx错误和429错误可重试
			code := streamTapeErr.Code()
			return code >= 500 || code == 429
		default:
			return false
		}
	}
	return false
}

// IsRateLimitError 判断是否为速率限制错误
func IsRateLimitError(err error) bool {
	if streamTapeErr, ok := err.(StreamTapeError); ok {
		return streamTapeErr.Code() == 429 || streamTapeErr.Code() == 509
	}
	return false
}
