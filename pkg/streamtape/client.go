package streamtape

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sync"
	"time"

	"magnet-downloader/pkg/logger"
)

// 全局速率限制器
var (
	lastStreamTapeRequest = time.Now()
	streamTapeMutex       sync.Mutex
)

// UploadServerResponse 获取上传服务器响应
type UploadServerResponse struct {
	Status int    `json:"status"`
	Msg    string `json:"msg"`
	Result struct {
		URL        string `json:"url"`
		ValidUntil string `json:"valid_until"`
	} `json:"result"`
}

// UploadResponse 上传响应
type UploadResponse struct {
	Status int    `json:"status"`
	Msg    string `json:"msg"`
	Result struct {
		Name     string `json:"name"`
		Size     int64  `json:"size"`
		Link     string `json:"link"`
		LinkID   string `json:"linkid"`
		Convert  string `json:"convert"`
		Downloads int   `json:"downloads"`
	} `json:"result"`
}

// UploadResult 上传结果（统一接口）
type UploadResult struct {
	Success  bool   `json:"success"`
	URL      string `json:"url"`       // 下载链接
	PlayURL  string `json:"play_url"`  // 播放链接
	FileCode string `json:"file_code"` // 文件代码
	Size     int64  `json:"size"`      // 文件大小
	Title    string `json:"title"`     // 文件标题
	CanPlay  bool   `json:"can_play"`  // 是否可播放
	Error    string `json:"error,omitempty"`
}

// Client StreamTape客户端
type Client struct {
	config     *Config
	httpClient *http.Client
	mutex      sync.RWMutex
}

// NewClient 创建新的StreamTape客户端
func NewClient(config *Config) *Client {
	if config == nil {
		config = DefaultConfig()
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		logger.Errorf("StreamTape配置验证失败: %v", err)
		return nil
	}

	// 创建优化的HTTP传输配置
	transport := &http.Transport{
		MaxIdleConns:        10,
		MaxIdleConnsPerHost: 5,
		IdleConnTimeout:     90 * time.Second,
		TLSHandshakeTimeout: 10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		WriteBufferSize: 64 * 1024, // 64KB
		ReadBufferSize:  64 * 1024, // 64KB
	}

	httpClient := &http.Client{
		Transport: transport,
		Timeout:   config.Timeout,
	}

	return &Client{
		config:     config,
		httpClient: httpClient,
	}
}

// TestConnection 测试连接
func (c *Client) TestConnection() error {
	logger.Infof("测试StreamTape连接...")
	
	// 构建账户信息请求URL
	reqURL := fmt.Sprintf("%s/account/info?login=%s&key=%s", 
		c.config.BaseURL, c.config.APILogin, c.config.APIKey)
	
	resp, err := c.httpClient.Get(reqURL)
	if err != nil {
		return NewNetworkError("连接StreamTape失败", err.Error())
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return NewNetworkError("读取响应失败", err.Error())
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return NewAPIError("解析响应失败", err.Error(), resp.StatusCode)
	}

	status, ok := result["status"].(float64)
	if !ok || status != 200 {
		msg, _ := result["msg"].(string)
		return NewAPIError("StreamTape API测试失败", msg, int(status))
	}

	logger.Infof("StreamTape连接测试成功")
	return nil
}

// GetUploadServer 获取上传服务器URL
func (c *Client) GetUploadServer(folderID string) (string, error) {
	// 应用速率限制
	streamTapeRateLimit()

	// 构建请求URL
	params := url.Values{}
	params.Set("login", c.config.APILogin)
	params.Set("key", c.config.APIKey)
	if folderID != "" {
		params.Set("folder", folderID)
	}

	reqURL := fmt.Sprintf("%s/file/ul?%s", c.config.BaseURL, params.Encode())
	
	logger.Debugf("获取StreamTape上传服务器: %s", reqURL)

	resp, err := c.httpClient.Get(reqURL)
	if err != nil {
		return "", NewNetworkError("获取上传服务器失败", err.Error())
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", NewNetworkError("读取响应失败", err.Error())
	}

	var uploadResp UploadServerResponse
	if err := json.Unmarshal(body, &uploadResp); err != nil {
		return "", NewAPIError("解析上传服务器响应失败", err.Error(), resp.StatusCode)
	}

	if uploadResp.Status != 200 {
		return "", NewAPIError("获取上传服务器失败", uploadResp.Msg, uploadResp.Status)
	}

	logger.Debugf("获取到StreamTape上传服务器: %s", uploadResp.Result.URL)
	return uploadResp.Result.URL, nil
}

// streamTapeRateLimit 应用速率限制
func streamTapeRateLimit() {
	streamTapeMutex.Lock()
	defer streamTapeMutex.Unlock()

	// StreamTape没有明确的速率限制，但我们保守一点，每100ms一个请求
	elapsed := time.Since(lastStreamTapeRequest)
	minInterval := 100 * time.Millisecond

	if elapsed < minInterval {
		time.Sleep(minInterval - elapsed)
	}

	lastStreamTapeRequest = time.Now()
}

// calculateBackoff 计算退避时间
func (c *Client) calculateBackoff(attempt int, isRateLimit bool) time.Duration {
	if isRateLimit {
		// 速率限制错误，等待更长时间
		return time.Duration(attempt*2) * time.Second
	}
	
	// 指数退避，最大30秒
	backoff := time.Duration(1<<uint(attempt)) * time.Second
	if backoff > 30*time.Second {
		backoff = 30 * time.Second
	}
	
	return backoff
}