package streamtape

import "time"

// Config StreamTape配置
type Config struct {
	APILogin   string        `json:"api_login"`   // API登录用户名
	APIKey     string        `json:"api_key"`     // API密钥/密码
	BaseURL    string        `json:"base_url"`    // API基础URL
	Timeout    time.Duration `json:"timeout"`     // 请求超时时间
	MaxRetries int           `json:"max_retries"` // 最大重试次数
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		BaseURL:    "https://api.streamtape.com",
		Timeout:    300 * time.Second, // 5分钟超时，适合大文件上传
		MaxRetries: 3,
	}
}

// Validate 验证配置
func (c *Config) Validate() error {
	if c.APILogin == "" {
		return NewValidationError("API登录用户名不能为空", "api_login is required")
	}
	if c.APIKey == "" {
		return NewValidationError("API密钥不能为空", "api_key is required")
	}
	if c.BaseURL == "" {
		c.BaseURL = "https://api.streamtape.com"
	}
	if c.Timeout == 0 {
		c.Timeout = 300 * time.Second
	}
	if c.MaxRetries == 0 {
		c.MaxRetries = 3
	}
	return nil
}