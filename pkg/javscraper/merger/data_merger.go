package merger

import (
	"fmt"
	"strings"
	"time"

	"magnet-downloader/pkg/logger"
)

// MergeStrategy 数据合并策略
type MergeStrategy struct {
	// 字段优先级（数据源名称 -> 优先级，数字越小优先级越高）
	FieldPriority map[string]map[string]int `json:"field_priority"`
	
	// 是否合并演员信息
	MergeActors bool `json:"merge_actors"`
	
	// 是否合并分类信息
	MergeGenres bool `json:"merge_genres"`
	
	// 是否合并磁力链接
	MergeMagnets bool `json:"merge_magnets"`
	
	// 最小可信度阈值
	MinConfidence float64 `json:"min_confidence"`
	
	// 数据源权重
	SourceWeights map[string]float64 `json:"source_weights"`
}

// MovieInfo 影片信息结构（本地定义避免循环导入）
type MovieInfo struct {
	Code        string    `json:"code"`
	Title       string    `json:"title"`
	TitleEn     string    `json:"title_en"`
	ReleaseDate time.Time `json:"release_date"`
	Duration    int       `json:"duration"`
	Studio      string    `json:"studio"`
	Series      string    `json:"series"`
	Director    string    `json:"director"`
	CoverURL    string    `json:"cover_url"`
	PosterURL   string    `json:"poster_url"`
	Plot        string    `json:"plot"`
	PlotEn      string    `json:"plot_en"`
	Rating      float64   `json:"rating"`
	Actors      []ActorInfo `json:"actors"`
	Genres      []GenreInfo `json:"genres"`
	Magnets     []MagnetInfo `json:"magnets"`
	Source      string    `json:"source"`
	ScrapedAt   time.Time `json:"scraped_at"`
	Confidence  float64   `json:"confidence"`
}

// ActorInfo 演员信息结构
type ActorInfo struct {
	Name      string     `json:"name"`
	NameEn    string     `json:"name_en"`
	NameJp    string     `json:"name_jp"`
	AvatarURL string     `json:"avatar_url"`
	BirthDate *time.Time `json:"birth_date"`
	Height    int        `json:"height"`
	Bust      int        `json:"bust"`
	Waist     int        `json:"waist"`
	Hip       int        `json:"hip"`
	BloodType string     `json:"blood_type"`
	Hobby     string     `json:"hobby"`
	DebutDate *time.Time `json:"debut_date"`
}

// GenreInfo 分类信息结构
type GenreInfo struct {
	Name        string `json:"name"`
	NameEn      string `json:"name_en"`
	NameJp      string `json:"name_jp"`
	Description string `json:"description"`
}

// MagnetInfo 磁力链接信息结构
type MagnetInfo struct {
	MagnetURL        string    `json:"magnet_url"`
	FileName         string    `json:"file_name"`
	FileSize         int64     `json:"file_size"`
	Quality          string    `json:"quality"`
	HasSubtitle      bool      `json:"has_subtitle"`
	SubtitleLanguage string    `json:"subtitle_language"`
	Source           string    `json:"source"`
	Uploader         string    `json:"uploader"`
	UploadDate       time.Time `json:"upload_date"`
	Seeders          int       `json:"seeders"`
	Leechers         int       `json:"leechers"`
}

// MergedMovieInfo 合并后的影片信息
type MergedMovieInfo struct {
	MovieInfo
	Sources []string `json:"sources"` // 数据来源列表
}

// DataMerger 数据融合器
type DataMerger struct {
	strategy *MergeStrategy
}

// NewDataMerger 创建新的数据融合器
func NewDataMerger(strategy *MergeStrategy) *DataMerger {
	if strategy == nil {
		strategy = DefaultMergeStrategy()
	}
	
	return &DataMerger{
		strategy: strategy,
	}
}

// DefaultMergeStrategy 默认合并策略
func DefaultMergeStrategy() *MergeStrategy {
	return &MergeStrategy{
		FieldPriority: map[string]map[string]int{
			"title": {
				"javbus":    1,
				"javinizer": 2,
				"javsp":     3,
			},
			"title_en": {
				"javinizer": 1,
				"javsp":     2,
				"javbus":    3,
			},
			"plot_en": {
				"javinizer": 1,
				"javsp":     2,
				"javbus":    3,
			},
			"actors": {
				"javbus":    1,
				"javsp":     2,
				"javinizer": 3,
			},
			"magnets": {
				"javbus":    1,
				"javsp":     2,
				"javinizer": 3,
			},
			"cover_url": {
				"javbus":    1,
				"javsp":     2,
				"javinizer": 3,
			},
			"studio": {
				"javbus":    1,
				"javinizer": 2,
				"javsp":     3,
			},
		},
		MergeActors:   true,
		MergeGenres:   true,
		MergeMagnets:  true,
		MinConfidence: 0.5,
		SourceWeights: map[string]float64{
			"javbus":    1.0,
			"javinizer": 0.8,
			"javsp":     0.7,
		},
	}
}

// MergeMovieInfos 合并多个影片信息
func (m *DataMerger) MergeMovieInfos(movieInfos []*MovieInfo) (*MergedMovieInfo, error) {
	if len(movieInfos) == 0 {
		return nil, fmt.Errorf("没有可合并的影片信息")
	}
	
	// 过滤低可信度的数据
	filteredInfos := m.filterByConfidence(movieInfos)
	if len(filteredInfos) == 0 {
		return nil, fmt.Errorf("所有数据的可信度都低于阈值 %.2f", m.strategy.MinConfidence)
	}
	
	// 按优先级排序
	sortedInfos := m.sortByPriority(filteredInfos)
	
	// 以最高优先级的数据为基础
	merged := &MergedMovieInfo{
		MovieInfo: *sortedInfos[0],
		Sources:   []string{sortedInfos[0].Source},
	}
	
	// 合并其他数据源的信息
	for _, info := range sortedInfos[1:] {
		m.mergeMovieInfo(merged, info)
		merged.Sources = append(merged.Sources, info.Source)
	}
	
	// 更新合并后的元数据
	merged.Source = "merged"
	merged.ScrapedAt = time.Now()
	merged.Confidence = m.calculateMergedConfidence(sortedInfos)
	
	logger.Infof("成功合并%d个数据源的影片信息: %s", len(sortedInfos), merged.Code)
	return merged, nil
}

// filterByConfidence 按可信度过滤数据
func (m *DataMerger) filterByConfidence(movieInfos []*MovieInfo) []*MovieInfo {
	var filtered []*MovieInfo
	for _, info := range movieInfos {
		if info.Confidence >= m.strategy.MinConfidence {
			filtered = append(filtered, info)
		} else {
			logger.Debugf("过滤低可信度数据: %s (可信度: %.2f)", info.Source, info.Confidence)
		}
	}
	return filtered
}

// sortByPriority 按优先级排序数据源
func (m *DataMerger) sortByPriority(movieInfos []*MovieInfo) []*MovieInfo {
	// 创建副本避免修改原数据
	sorted := make([]*MovieInfo, len(movieInfos))
	copy(sorted, movieInfos)
	
	// 简单的冒泡排序，按数据源权重排序
	for i := 0; i < len(sorted)-1; i++ {
		for j := 0; j < len(sorted)-i-1; j++ {
			weight1 := m.getSourceWeight(sorted[j].Source)
			weight2 := m.getSourceWeight(sorted[j+1].Source)
			if weight1 < weight2 {
				sorted[j], sorted[j+1] = sorted[j+1], sorted[j]
			}
		}
	}
	
	return sorted
}

// getSourceWeight 获取数据源权重
func (m *DataMerger) getSourceWeight(source string) float64 {
	// 提取数据源名称（去除前缀）
	sourceName := source
	if strings.Contains(source, "-") {
		parts := strings.Split(source, "-")
		sourceName = parts[0]
	}
	
	if weight, exists := m.strategy.SourceWeights[sourceName]; exists {
		return weight
	}
	return 0.5 // 默认权重
}

// mergeMovieInfo 合并单个影片信息到目标信息中
func (m *DataMerger) mergeMovieInfo(target *MergedMovieInfo, source *MovieInfo) {
	// 合并基本字段
	m.mergeStringField(&target.Title, source.Title, "title", target.Source, source.Source)
	m.mergeStringField(&target.TitleEn, source.TitleEn, "title_en", target.Source, source.Source)
	m.mergeStringField(&target.Plot, source.Plot, "plot", target.Source, source.Source)
	m.mergeStringField(&target.PlotEn, source.PlotEn, "plot_en", target.Source, source.Source)
	m.mergeStringField(&target.Studio, source.Studio, "studio", target.Source, source.Source)
	m.mergeStringField(&target.Series, source.Series, "series", target.Source, source.Source)
	m.mergeStringField(&target.Director, source.Director, "director", target.Source, source.Source)
	m.mergeStringField(&target.CoverURL, source.CoverURL, "cover_url", target.Source, source.Source)
	m.mergeStringField(&target.PosterURL, source.PosterURL, "poster_url", target.Source, source.Source)
	
	// 合并数值字段
	if target.Duration == 0 && source.Duration > 0 {
		target.Duration = source.Duration
	}
	if target.Rating == 0 && source.Rating > 0 {
		target.Rating = source.Rating
	}
	
	// 合并时间字段
	if target.ReleaseDate.IsZero() && !source.ReleaseDate.IsZero() {
		target.ReleaseDate = source.ReleaseDate
	}
	
	// 合并演员信息
	if m.strategy.MergeActors {
		target.Actors = m.mergeActors(target.Actors, source.Actors)
	}
	
	// 合并分类信息
	if m.strategy.MergeGenres {
		target.Genres = m.mergeGenres(target.Genres, source.Genres)
	}
	
	// 合并磁力链接
	if m.strategy.MergeMagnets {
		target.Magnets = m.mergeMagnets(target.Magnets, source.Magnets)
	}
}

// mergeStringField 合并字符串字段
func (m *DataMerger) mergeStringField(target *string, source, fieldName, targetSource, sourceSource string) {
	// 如果目标字段为空，直接使用源字段
	if *target == "" && source != "" {
		*target = source
		return
	}
	
	// 如果源字段为空，保持目标字段不变
	if source == "" {
		return
	}
	
	// 根据字段优先级决定是否替换
	if m.shouldReplaceField(fieldName, targetSource, sourceSource) {
		*target = source
	}
}

// shouldReplaceField 判断是否应该替换字段
func (m *DataMerger) shouldReplaceField(fieldName, targetSource, sourceSource string) bool {
	fieldPriority, exists := m.strategy.FieldPriority[fieldName]
	if !exists {
		// 如果没有定义优先级，使用默认权重比较
		targetWeight := m.getSourceWeight(targetSource)
		sourceWeight := m.getSourceWeight(sourceSource)
		return sourceWeight > targetWeight
	}
	
	targetPriority := fieldPriority[m.extractSourceName(targetSource)]
	sourcePriority := fieldPriority[m.extractSourceName(sourceSource)]
	
	// 优先级数字越小，优先级越高
	return sourcePriority < targetPriority
}

// extractSourceName 提取数据源名称
func (m *DataMerger) extractSourceName(source string) string {
	if strings.Contains(source, "-") {
		parts := strings.Split(source, "-")
		return parts[0]
	}
	return source
}

// mergeActors 合并演员信息
func (m *DataMerger) mergeActors(target []ActorInfo, source []ActorInfo) []ActorInfo {
	// 创建演员名称到索引的映射
	actorMap := make(map[string]int)
	for i, actor := range target {
		actorMap[actor.Name] = i
	}
	
	// 合并源演员信息
	for _, sourceActor := range source {
		if index, exists := actorMap[sourceActor.Name]; exists {
			// 演员已存在，合并详细信息
			m.mergeActorInfo(&target[index], &sourceActor)
		} else {
			// 新演员，直接添加
			target = append(target, sourceActor)
			actorMap[sourceActor.Name] = len(target) - 1
		}
	}
	
	return target
}

// mergeActorInfo 合并单个演员信息
func (m *DataMerger) mergeActorInfo(target *ActorInfo, source *ActorInfo) {
	if target.NameEn == "" && source.NameEn != "" {
		target.NameEn = source.NameEn
	}
	if target.NameJp == "" && source.NameJp != "" {
		target.NameJp = source.NameJp
	}
	if target.AvatarURL == "" && source.AvatarURL != "" {
		target.AvatarURL = source.AvatarURL
	}
	if target.BirthDate == nil && source.BirthDate != nil {
		target.BirthDate = source.BirthDate
	}
	if target.Height == 0 && source.Height > 0 {
		target.Height = source.Height
	}
	if target.Bust == 0 && source.Bust > 0 {
		target.Bust = source.Bust
	}
	if target.Waist == 0 && source.Waist > 0 {
		target.Waist = source.Waist
	}
	if target.Hip == 0 && source.Hip > 0 {
		target.Hip = source.Hip
	}
	if target.BloodType == "" && source.BloodType != "" {
		target.BloodType = source.BloodType
	}
	if target.Hobby == "" && source.Hobby != "" {
		target.Hobby = source.Hobby
	}
	if target.DebutDate == nil && source.DebutDate != nil {
		target.DebutDate = source.DebutDate
	}
}

// mergeGenres 合并分类信息
func (m *DataMerger) mergeGenres(target []GenreInfo, source []GenreInfo) []GenreInfo {
	// 创建分类名称到索引的映射
	genreMap := make(map[string]int)
	for i, genre := range target {
		genreMap[genre.Name] = i
	}
	
	// 合并源分类信息
	for _, sourceGenre := range source {
		if index, exists := genreMap[sourceGenre.Name]; exists {
			// 分类已存在，合并详细信息
			m.mergeGenreInfo(&target[index], &sourceGenre)
		} else {
			// 新分类，直接添加
			target = append(target, sourceGenre)
			genreMap[sourceGenre.Name] = len(target) - 1
		}
	}
	
	return target
}

// mergeGenreInfo 合并单个分类信息
func (m *DataMerger) mergeGenreInfo(target *GenreInfo, source *GenreInfo) {
	if target.NameEn == "" && source.NameEn != "" {
		target.NameEn = source.NameEn
	}
	if target.NameJp == "" && source.NameJp != "" {
		target.NameJp = source.NameJp
	}
	if target.Description == "" && source.Description != "" {
		target.Description = source.Description
	}
}

// mergeMagnets 合并磁力链接
func (m *DataMerger) mergeMagnets(target []MagnetInfo, source []MagnetInfo) []MagnetInfo {
	// 创建磁力链接URL到索引的映射
	magnetMap := make(map[string]int)
	for i, magnet := range target {
		magnetMap[magnet.MagnetURL] = i
	}
	
	// 合并源磁力链接
	for _, sourceMagnet := range source {
		if _, exists := magnetMap[sourceMagnet.MagnetURL]; !exists {
			// 新磁力链接，直接添加
			target = append(target, sourceMagnet)
		}
		// 如果磁力链接已存在，不重复添加
	}
	
	return target
}

// calculateMergedConfidence 计算合并后的可信度
func (m *DataMerger) calculateMergedConfidence(movieInfos []*MovieInfo) float64 {
	if len(movieInfos) == 0 {
		return 0.0
	}
	
	// 加权平均可信度
	var totalWeight float64
	var weightedSum float64
	
	for _, info := range movieInfos {
		weight := m.getSourceWeight(info.Source)
		totalWeight += weight
		weightedSum += info.Confidence * weight
	}
	
	if totalWeight == 0 {
		return 0.0
	}
	
	// 合并奖励：多个数据源的合并结果通常更可靠
	mergedConfidence := weightedSum / totalWeight
	if len(movieInfos) > 1 {
		mergedConfidence += 0.1 * float64(len(movieInfos)-1) // 每多一个数据源增加0.1
	}
	
	// 确保可信度不超过1.0
	if mergedConfidence > 1.0 {
		mergedConfidence = 1.0
	}
	
	return mergedConfidence
}

// UpdateStrategy 更新合并策略
func (m *DataMerger) UpdateStrategy(strategy *MergeStrategy) {
	if strategy != nil {
		m.strategy = strategy
	}
}

// GetStrategy 获取当前合并策略
func (m *DataMerger) GetStrategy() *MergeStrategy {
	return m.strategy
}

// ValidateMovieInfo 验证影片信息的完整性
func (m *DataMerger) ValidateMovieInfo(info *MovieInfo) []string {
	var issues []string
	
	if info.Code == "" {
		issues = append(issues, "影片番号不能为空")
	}
	
	if info.Title == "" && info.TitleEn == "" {
		issues = append(issues, "影片标题不能为空")
	}
	
	if info.Confidence < 0 || info.Confidence > 1 {
		issues = append(issues, "可信度必须在0-1之间")
	}
	
	if info.Source == "" {
		issues = append(issues, "数据源不能为空")
	}
	
	return issues
}