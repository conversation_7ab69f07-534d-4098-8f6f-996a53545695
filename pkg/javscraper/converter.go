package javscraper

import (
	"magnet-downloader/pkg/javscraper/javbus"
	"magnet-downloader/pkg/javscraper/javinizer"
	"magnet-downloader/pkg/javscraper/javsp"
)

// JavBus类型转换函数

func convertJavBusResult(result *javbus.ScrapingResult) *ScrapingResult {
	if result == nil {
		return nil
	}
	
	return &ScrapingResult{
		Success:   result.Success,
		MovieInfo: convertJavBusMovieInfo(result.MovieInfo),
		Error:     result.Error,
		Source:    result.Source,
		Duration:  result.Duration,
	}
}

func convertJavBusMovieInfo(info *javbus.MovieInfo) *MovieInfo {
	if info == nil {
		return nil
	}
	
	return &MovieInfo{
		Code:        info.Code,
		Title:       info.Title,
		TitleEn:     info.TitleEn,
		ReleaseDate: info.ReleaseDate,
		Duration:    info.Duration,
		Studio:      info.Studio,
		Series:      info.Series,
		Director:    info.Director,
		CoverURL:    info.CoverURL,
		PosterURL:   info.PosterURL,
		Plot:        info.Plot,
		PlotEn:      info.PlotEn,
		Rating:      info.Rating,
		Actors:        convertJavBusActors(info.Actors),
		Genres:        convertJavBusGenres(info.Genres),
		Magnets:       convertJavBusMagnets(info.Magnets),
		SimilarMovies: convertJavBusSimilarMovies(info.SimilarMovies),
		Source:        info.Source,
		ScrapedAt:     info.ScrapedAt,
		Confidence:    info.Confidence,
	}
}

func convertJavBusActors(actors []javbus.ActorInfo) []ActorInfo {
	result := make([]ActorInfo, len(actors))
	for i, actor := range actors {
		result[i] = ActorInfo{
			Name:      actor.Name,
			NameEn:    actor.NameEn,
			NameJp:    actor.NameJp,
			AvatarURL: actor.AvatarURL,
			BirthDate: actor.BirthDate,
			Height:    actor.Height,
			Bust:      actor.Bust,
			Waist:     actor.Waist,
			Hip:       actor.Hip,
			BloodType: actor.BloodType,
			Hobby:     actor.Hobby,
			DebutDate: actor.DebutDate,
		}
	}
	return result
}

func convertJavBusGenres(genres []javbus.GenreInfo) []GenreInfo {
	result := make([]GenreInfo, len(genres))
	for i, genre := range genres {
		result[i] = GenreInfo{
			Name:        genre.Name,
			NameEn:      genre.NameEn,
			NameJp:      genre.NameJp,
			Description: genre.Description,
		}
	}
	return result
}

func convertJavBusMagnets(magnets []javbus.MagnetInfo) []MagnetInfo {
	result := make([]MagnetInfo, len(magnets))
	for i, magnet := range magnets {
		result[i] = MagnetInfo{
			MagnetURL:        magnet.MagnetURL,
			FileName:         magnet.FileName,
			FileSize:         magnet.FileSize,
			Quality:          magnet.Quality,
			HasSubtitle:      magnet.HasSubtitle,
			SubtitleLanguage: magnet.SubtitleLanguage,
			Source:           magnet.Source,
			Uploader:         magnet.Uploader,
			UploadDate:       magnet.UploadDate,
			Seeders:          magnet.Seeders,
			Leechers:         magnet.Leechers,
		}
	}
	return result
}

func convertJavBusSimilarMovies(similarMovies []javbus.SimilarMovieInfo) []SimilarMovieInfo {
	if len(similarMovies) == 0 {
		return nil
	}
	
	result := make([]SimilarMovieInfo, len(similarMovies))
	for i, similar := range similarMovies {
		result[i] = SimilarMovieInfo{
			Code:     similar.Code,
			Title:    similar.Title,
			CoverURL: similar.CoverURL,
		}
	}
	
	return result
}

func convertJavBusSearchResult(result *javbus.SearchResult) *SearchResult {
	if result == nil {
		return nil
	}
	
	movies := make([]MovieInfo, len(result.Movies))
	for i, movie := range result.Movies {
		movies[i] = *convertJavBusMovieInfo(&movie)
	}
	
	return &SearchResult{
		Movies:   movies,
		Total:    result.Total,
		Page:     result.Page,
		PageSize: result.PageSize,
		HasMore:  result.HasMore,
		Source:   result.Source,
	}
}

func convertJavBusActorInfo(info *javbus.ActorInfo) *ActorInfo {
	if info == nil {
		return nil
	}
	
	return &ActorInfo{
		Name:      info.Name,
		NameEn:    info.NameEn,
		NameJp:    info.NameJp,
		AvatarURL: info.AvatarURL,
		BirthDate: info.BirthDate,
		Height:    info.Height,
		Bust:      info.Bust,
		Waist:     info.Waist,
		Hip:       info.Hip,
		BloodType: info.BloodType,
		Hobby:     info.Hobby,
		DebutDate: info.DebutDate,
	}
}

// Javinizer类型转换函数

func convertJavinizerResult(result *javinizer.ScrapingResult) *ScrapingResult {
	if result == nil {
		return nil
	}
	
	return &ScrapingResult{
		Success:   result.Success,
		MovieInfo: convertJavinizerMovieInfo(result.MovieInfo),
		Error:     result.Error,
		Source:    result.Source,
		Duration:  result.Duration,
	}
}

func convertJavinizerMovieInfo(info *javinizer.MovieInfo) *MovieInfo {
	if info == nil {
		return nil
	}
	
	return &MovieInfo{
		Code:        info.Code,
		Title:       info.Title,
		TitleEn:     info.TitleEn,
		ReleaseDate: info.ReleaseDate,
		Duration:    info.Duration,
		Studio:      info.Studio,
		Series:      info.Series,
		Director:    info.Director,
		CoverURL:    info.CoverURL,
		PosterURL:   info.PosterURL,
		Plot:        info.Plot,
		PlotEn:      info.PlotEn,
		Rating:      info.Rating,
		Actors:      convertJavinizerActors(info.Actors),
		Genres:      convertJavinizerGenres(info.Genres),
		Magnets:     convertJavinizerMagnets(info.Magnets),
		Source:      info.Source,
		ScrapedAt:   info.ScrapedAt,
		Confidence:  info.Confidence,
	}
}

func convertJavinizerActors(actors []javinizer.ActorInfo) []ActorInfo {
	result := make([]ActorInfo, len(actors))
	for i, actor := range actors {
		result[i] = ActorInfo{
			Name:      actor.Name,
			NameEn:    actor.NameEn,
			NameJp:    actor.NameJp,
			AvatarURL: actor.AvatarURL,
			BirthDate: actor.BirthDate,
			Height:    actor.Height,
			Bust:      actor.Bust,
			Waist:     actor.Waist,
			Hip:       actor.Hip,
			BloodType: actor.BloodType,
			Hobby:     actor.Hobby,
			DebutDate: actor.DebutDate,
		}
	}
	return result
}

func convertJavinizerGenres(genres []javinizer.GenreInfo) []GenreInfo {
	result := make([]GenreInfo, len(genres))
	for i, genre := range genres {
		result[i] = GenreInfo{
			Name:        genre.Name,
			NameEn:      genre.NameEn,
			NameJp:      genre.NameJp,
			Description: genre.Description,
		}
	}
	return result
}

func convertJavinizerMagnets(magnets []javinizer.MagnetInfo) []MagnetInfo {
	result := make([]MagnetInfo, len(magnets))
	for i, magnet := range magnets {
		result[i] = MagnetInfo{
			MagnetURL:        magnet.MagnetURL,
			FileName:         magnet.FileName,
			FileSize:         magnet.FileSize,
			Quality:          magnet.Quality,
			HasSubtitle:      magnet.HasSubtitle,
			SubtitleLanguage: magnet.SubtitleLanguage,
			Source:           magnet.Source,
			Uploader:         magnet.Uploader,
			UploadDate:       magnet.UploadDate,
			Seeders:          magnet.Seeders,
			Leechers:         magnet.Leechers,
		}
	}
	return result
}

func convertJavinizerSearchResult(result *javinizer.SearchResult) *SearchResult {
	if result == nil {
		return nil
	}
	
	movies := make([]MovieInfo, len(result.Movies))
	for i, movie := range result.Movies {
		movies[i] = *convertJavinizerMovieInfo(&movie)
	}
	
	return &SearchResult{
		Movies:   movies,
		Total:    result.Total,
		Page:     result.Page,
		PageSize: result.PageSize,
		HasMore:  result.HasMore,
		Source:   result.Source,
	}
}

func convertJavinizerActorInfo(info *javinizer.ActorInfo) *ActorInfo {
	if info == nil {
		return nil
	}
	
	return &ActorInfo{
		Name:      info.Name,
		NameEn:    info.NameEn,
		NameJp:    info.NameJp,
		AvatarURL: info.AvatarURL,
		BirthDate: info.BirthDate,
		Height:    info.Height,
		Bust:      info.Bust,
		Waist:     info.Waist,
		Hip:       info.Hip,
		BloodType: info.BloodType,
		Hobby:     info.Hobby,
		DebutDate: info.DebutDate,
	}
}

// JavSP类型转换函数

func convertJavSPResult(result *javsp.ScrapingResult) *ScrapingResult {
	if result == nil {
		return nil
	}
	
	return &ScrapingResult{
		Success:   result.Success,
		MovieInfo: convertJavSPMovieInfo(result.MovieInfo),
		Error:     result.Error,
		Source:    result.Source,
		Duration:  result.Duration,
	}
}

func convertJavSPMovieInfo(info *javsp.MovieInfo) *MovieInfo {
	if info == nil {
		return nil
	}
	
	return &MovieInfo{
		Code:        info.Code,
		Title:       info.Title,
		TitleEn:     info.TitleEn,
		ReleaseDate: info.ReleaseDate,
		Duration:    info.Duration,
		Studio:      info.Studio,
		Series:      info.Series,
		Director:    info.Director,
		CoverURL:    info.CoverURL,
		PosterURL:   info.PosterURL,
		Plot:        info.Plot,
		PlotEn:      info.PlotEn,
		Rating:      info.Rating,
		Actors:      convertJavSPActors(info.Actors),
		Genres:      convertJavSPGenres(info.Genres),
		Magnets:     convertJavSPMagnets(info.Magnets),
		Source:      info.Source,
		ScrapedAt:   info.ScrapedAt,
		Confidence:  info.Confidence,
	}
}

func convertJavSPActors(actors []javsp.ActorInfo) []ActorInfo {
	result := make([]ActorInfo, len(actors))
	for i, actor := range actors {
		result[i] = ActorInfo{
			Name:      actor.Name,
			NameEn:    actor.NameEn,
			NameJp:    actor.NameJp,
			AvatarURL: actor.AvatarURL,
			BirthDate: actor.BirthDate,
			Height:    actor.Height,
			Bust:      actor.Bust,
			Waist:     actor.Waist,
			Hip:       actor.Hip,
			BloodType: actor.BloodType,
			Hobby:     actor.Hobby,
			DebutDate: actor.DebutDate,
		}
	}
	return result
}

func convertJavSPGenres(genres []javsp.GenreInfo) []GenreInfo {
	result := make([]GenreInfo, len(genres))
	for i, genre := range genres {
		result[i] = GenreInfo{
			Name:        genre.Name,
			NameEn:      genre.NameEn,
			NameJp:      genre.NameJp,
			Description: genre.Description,
		}
	}
	return result
}

func convertJavSPMagnets(magnets []javsp.MagnetInfo) []MagnetInfo {
	result := make([]MagnetInfo, len(magnets))
	for i, magnet := range magnets {
		result[i] = MagnetInfo{
			MagnetURL:        magnet.MagnetURL,
			FileName:         magnet.FileName,
			FileSize:         magnet.FileSize,
			Quality:          magnet.Quality,
			HasSubtitle:      magnet.HasSubtitle,
			SubtitleLanguage: magnet.SubtitleLanguage,
			Source:           magnet.Source,
			Uploader:         magnet.Uploader,
			UploadDate:       magnet.UploadDate,
			Seeders:          magnet.Seeders,
			Leechers:         magnet.Leechers,
		}
	}
	return result
}

func convertJavSPSearchResult(result *javsp.SearchResult) *SearchResult {
	if result == nil {
		return nil
	}
	
	movies := make([]MovieInfo, len(result.Movies))
	for i, movie := range result.Movies {
		movies[i] = *convertJavSPMovieInfo(&movie)
	}
	
	return &SearchResult{
		Movies:   movies,
		Total:    result.Total,
		Page:     result.Page,
		PageSize: result.PageSize,
		HasMore:  result.HasMore,
		Source:   result.Source,
	}
}

func convertJavSPActorInfo(info *javsp.ActorInfo) *ActorInfo {
	if info == nil {
		return nil
	}
	
	return &ActorInfo{
		Name:      info.Name,
		NameEn:    info.NameEn,
		NameJp:    info.NameJp,
		AvatarURL: info.AvatarURL,
		BirthDate: info.BirthDate,
		Height:    info.Height,
		Bust:      info.Bust,
		Waist:     info.Waist,
		Hip:       info.Hip,
		BloodType: info.BloodType,
		Hobby:     info.Hobby,
		DebutDate: info.DebutDate,
	}
}