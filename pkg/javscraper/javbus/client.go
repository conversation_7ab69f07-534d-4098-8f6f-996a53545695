package javbus

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"magnet-downloader/pkg/javscraper/client"
	"magnet-downloader/pkg/logger"
)

// Client JavBus爬虫客户端
type Client struct {
	baseClient *client.BaseClient
	config     *Config
	enabled    bool
	apiBaseURL string
}

// NewClient 创建新的JavBus客户端
func NewClient(config *Config, userAgent string, apiBaseURL ...string) *Client {
	if config == nil {
		config = &Config{
			Enabled:   true,
			BaseURL:   "https://www.javbus.com",
			Timeout:   30 * time.Second,
			RateLimit: 3 * time.Second,
			MaxPages:  10,
		}
	}

	baseClient := client.NewBaseClient(
		userAgent,
		config.Timeout,
		3, // maxRetries
		config.RateLimit,
	)

	// 设置API基础URL
	defaultAPIURL := "http://localhost:3001"
	if len(apiBaseURL) > 0 && apiBaseURL[0] != "" {
		defaultAPIURL = apiBaseURL[0]
	}

	return &Client{
		baseClient: baseClient,
		config:     config,
		enabled:    config.Enabled,
		apiBaseURL: defaultAPIURL,
	}
}

// API响应结构体 - 直接映射javbus-api的响应格式
type APIMovie struct {
	ID            string             `json:"id"`
	Title         string             `json:"title"`
	Img           string             `json:"img"`
	Date          string             `json:"date"`
	VideoLength   int                `json:"videoLength"`
	Director      APIStaff           `json:"director"`
	Producer      APIStaff           `json:"producer"`
	Publisher     APIStaff           `json:"publisher"`
	Series        *APIStaff          `json:"series"`
	Genres        []APIGenre         `json:"genres"`
	Stars         []APIActor         `json:"stars"`
	Samples       []APISample        `json:"samples"`
	SimilarMovies []APISimilarMovie  `json:"similarMovies"`
	GID           string             `json:"gid"`
	UC            string             `json:"uc"`
}

type APIStaff struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type APIGenre struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type APISample struct {
	Alt       string `json:"alt"`
	ID        string `json:"id"`
	Src       string `json:"src"`
	Thumbnail string `json:"thumbnail"`
}

type APIActor struct {
	Name string `json:"name"`
	URL  string `json:"url"`
}

type APIMagnet struct {
	ID           string `json:"id"`
	Link         string `json:"link"`
	IsHD         bool   `json:"isHD"`
	Title        string `json:"title"`
	Size         string `json:"size"`
	NumberSize   int64  `json:"numberSize"`
	ShareDate    string `json:"shareDate"`
	HasSubtitle  bool   `json:"hasSubtitle"`
}

type APISimilarMovie struct {
	ID    string `json:"id"`
	Title string `json:"title"`
	Img   string `json:"img"`
}

type APIMoviesResponse struct {
	Movies     []APIMovieSimple `json:"movies"`
	Pagination APIPagination    `json:"pagination"`
}

type APIMovieSimple struct {
	ID    string   `json:"id"`
	Title string   `json:"title"`
	Img   string   `json:"img"`
	Date  string   `json:"date"`
	Tags  []string `json:"tags"`
}

type APIPagination struct {
	CurrentPage int   `json:"currentPage"`
	HasNextPage bool  `json:"hasNextPage"`
	NextPage    int   `json:"nextPage"`
	Pages       []int `json:"pages"`
}

// GetMovieByCode 根据番号获取影片信息
func (c *Client) GetMovieByCode(code string) (*ScrapingResult, error) {
	if !c.enabled {
		return &ScrapingResult{
			Success: false,
			Error:   "JavBus爬虫未启用",
			Source:  "javbus",
		}, nil
	}

	startTime := time.Now()

	// 调用javbus-api获取影片信息
	apiURL := fmt.Sprintf("%s/api/movies/%s", c.apiBaseURL, code)
	
	ctx, cancel := context.WithTimeout(context.Background(), c.config.Timeout)
	defer cancel()

	response, err := c.baseClient.Get(ctx, apiURL, nil)
	if err != nil {
		return &ScrapingResult{
			Success:  false,
			Error:    fmt.Sprintf("API请求失败: %v", err),
			Source:   "javbus",
			Duration: time.Since(startTime),
		}, nil
	}

	// 解析API响应
	var apiMovie APIMovie
	if err := json.Unmarshal(response, &apiMovie); err != nil {
		return &ScrapingResult{
			Success:  false,
			Error:    fmt.Sprintf("解析API响应失败: %v", err),
			Source:   "javbus",
			Duration: time.Since(startTime),
		}, nil
	}

	// 检查是否获取到有效数据
	if apiMovie.ID == "" {
		return &ScrapingResult{
			Success:  false,
			Error:    "未找到影片信息",
			Source:   "javbus",
			Duration: time.Since(startTime),
		}, nil
	}

	// 转换API数据为MovieInfo
	movieInfo := c.convertAPIMovieToMovieInfo(&apiMovie)
	
	// 补充获取演员头像信息（通过HTML解析）
	if len(movieInfo.Actors) > 0 {
		if err := c.enrichActorAvatars(movieInfo, code); err != nil {
			logger.Warnf("获取演员头像失败: %v", err)
		}
	}
	
	// 获取磁力链接
	if apiMovie.GID != "" && apiMovie.UC != "" {
		magnets, err := c.getMagnetLinksFromAPI(code, apiMovie.GID, apiMovie.UC)
		if err != nil {
			logger.Warnf("获取磁力链接失败: %v", err)
		} else {
			movieInfo.Magnets = magnets
		}
	}

	movieInfo.Source = "javbus"
	movieInfo.ScrapedAt = time.Now()
	movieInfo.Confidence = 0.9

	return &ScrapingResult{
		Success:   true,
		MovieInfo: movieInfo,
		Source:    "javbus",
		Duration:  time.Since(startTime),
	}, nil
}

// convertAPIMovieToMovieInfo 将API响应转换为MovieInfo
func (c *Client) convertAPIMovieToMovieInfo(apiMovie *APIMovie) *MovieInfo {
	movieInfo := &MovieInfo{
		Code:     apiMovie.ID,
		Title:    apiMovie.Title,
		CoverURL: apiMovie.Img,
		Studio:   apiMovie.Producer.Name,
		Director: apiMovie.Director.Name,
		Duration: apiMovie.VideoLength,
	}

	// 解析发布日期
	if apiMovie.Date != "" {
		if releaseDate, err := time.Parse("2006-01-02", apiMovie.Date); err == nil {
			movieInfo.ReleaseDate = releaseDate
		}
	}

	// 设置系列信息
	if apiMovie.Series != nil {
		movieInfo.Series = apiMovie.Series.Name
	}

	// 转换演员信息
	for _, star := range apiMovie.Stars {
		movieInfo.Actors = append(movieInfo.Actors, ActorInfo{
			Name: star.Name,
		})
	}

	// 转换分类信息
	for _, genre := range apiMovie.Genres {
		movieInfo.Genres = append(movieInfo.Genres, GenreInfo{
			Name: genre.Name,
		})
	}

	// 转换关联影片信息
	for _, similar := range apiMovie.SimilarMovies {
		movieInfo.SimilarMovies = append(movieInfo.SimilarMovies, SimilarMovieInfo{
			Code:     similar.ID,
			Title:    similar.Title,
			CoverURL: similar.Img,
		})
	}

	return movieInfo
}

// convertSimpleAPIMovieToMovieInfo 将简化的API响应转换为MovieInfo
func (c *Client) convertSimpleAPIMovieToMovieInfo(apiMovie *APIMovieSimple) *MovieInfo {
	movieInfo := &MovieInfo{
		Code:     apiMovie.ID,
		Title:    apiMovie.Title,
		CoverURL: apiMovie.Img,
	}

	// 解析发布日期
	if apiMovie.Date != "" {
		if releaseDate, err := time.Parse("2006-01-02", apiMovie.Date); err == nil {
			movieInfo.ReleaseDate = releaseDate
		}
	}

	return movieInfo
}

// getMagnetLinksFromAPI 从API获取磁力链接
func (c *Client) getMagnetLinksFromAPI(code, gid, uc string) ([]MagnetInfo, error) {
	apiURL := fmt.Sprintf("%s/api/magnets/%s?gid=%s&uc=%s", c.apiBaseURL, code, gid, uc)
	
	ctx, cancel := context.WithTimeout(context.Background(), c.config.Timeout)
	defer cancel()

	response, err := c.baseClient.Get(ctx, apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("API请求失败: %v", err)
	}

	var apiMagnets []APIMagnet
	if err := json.Unmarshal(response, &apiMagnets); err != nil {
		return nil, fmt.Errorf("解析API响应失败: %v", err)
	}

	var magnets []MagnetInfo
	for _, apiMagnet := range apiMagnets {
		// 解析上传日期
		var uploadDate time.Time
		if apiMagnet.ShareDate != "" {
			if date, err := time.Parse("2006-01-02", apiMagnet.ShareDate); err == nil {
				uploadDate = date
			}
		}

		magnets = append(magnets, MagnetInfo{
			FileName:     apiMagnet.Title,
			MagnetURL:    apiMagnet.Link,
			FileSize:     apiMagnet.NumberSize,
			Quality:      func() string { if apiMagnet.IsHD { return "HD" } else { return "SD" } }(),
			HasSubtitle:  apiMagnet.HasSubtitle,
			Source:       "javbus",
			UploadDate:   uploadDate,
		})
	}

	return magnets, nil
}

// parseMovieHTML 解析影片HTML页面 (已废弃，使用API替代)
/*
func (c *Client) parseMovieHTML(html, code string) (*MovieInfo, error) {
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(html))
	if err != nil {
		return nil, fmt.Errorf("解析HTML失败: %v", err)
	}

	movieInfo := &MovieInfo{
		Code: code,
	}

	// 解析标题
	title := doc.Find("h3").First().Text()
	if title != "" {
		// 移除番号前缀
		if strings.HasPrefix(title, code) {
			title = strings.TrimSpace(strings.TrimPrefix(title, code))
		}
		movieInfo.Title = title
	}

	// 解析封面图片
	coverImg := doc.Find(".bigImage img").First()
	if coverSrc, exists := coverImg.Attr("src"); exists {
		movieInfo.CoverURL = coverSrc
	}

	// 解析详细信息
	doc.Find(".info p").Each(func(i int, s *goquery.Selection) {
		text := s.Text()

		if strings.Contains(text, "發行日期:") {
			dateStr := strings.TrimSpace(strings.Split(text, ":")[1])
			if releaseDate, err := time.Parse("2006-01-02", dateStr); err == nil {
				movieInfo.ReleaseDate = releaseDate
			}
		} else if strings.Contains(text, "長度:") {
			durationStr := strings.TrimSpace(strings.Split(text, ":")[1])
			durationStr = strings.Replace(durationStr, "分鐘", "", -1)
			if duration, err := strconv.Atoi(strings.TrimSpace(durationStr)); err == nil {
				movieInfo.Duration = duration
			}
		} else if strings.Contains(text, "製作商:") {
			studio := s.Find("a").Text()
			if studio != "" {
				movieInfo.Studio = studio
			}
		} else if strings.Contains(text, "系列:") {
			series := s.Find("a").Text()
			if series != "" {
				movieInfo.Series = series
			}
		} else if strings.Contains(text, "導演:") {
			director := s.Find("a").Text()
			if director != "" {
				movieInfo.Director = director
			}
		}
	})

	// 解析演员信息
	var actors []ActorInfo
	doc.Find(".star-name a").Each(func(i int, s *goquery.Selection) {
		actorName := strings.TrimSpace(s.Text())
		if actorName != "" {
			// 获取演员头像
			avatarURL := ""
			if img := s.Parent().Find("img").First(); img.Length() > 0 {
				if src, exists := img.Attr("src"); exists {
					avatarURL = src
				}
			}

			actors = append(actors, ActorInfo{
				Name:      actorName,
				AvatarURL: avatarURL,
			})
		}
	})
	movieInfo.Actors = actors

	// 解析分类标签
	var genres []GenreInfo
	doc.Find(".genre a").Each(func(i int, s *goquery.Selection) {
		genreName := strings.TrimSpace(s.Text())
		if genreName != "" {
			genres = append(genres, GenreInfo{
				Name: genreName,
			})
		}
	})
	movieInfo.Genres = genres

	return movieInfo, nil
}
*/

// getMagnetLinks 获取磁力链接 (已废弃，使用API替代)
/*
func (c *Client) getMagnetLinks(code string) ([]MagnetInfo, error) {
	// 构建磁力链接页面URL
	magnetURL, err := c.baseClient.BuildURL(c.config.BaseURL, "ajax/uncledatoolsbyajax.php", map[string]string{
		"gid":  code,
		"lang": "zh",
		"img":  "ps",
	})
	if err != nil {
		return nil, fmt.Errorf("构建磁力链接URL失败: %v", err)
	}

	// 发送请求
	ctx, cancel := context.WithTimeout(context.Background(), c.config.Timeout)
	defer cancel()

	headers := map[string]string{
		"Referer":          c.config.BaseURL + "/" + code,
		"X-Requested-With": "XMLHttpRequest",
	}

	html, err := c.baseClient.Get(ctx, magnetURL, headers)
	if err != nil {
		return nil, fmt.Errorf("请求磁力链接失败: %v", err)
	}

	// 解析磁力链接
	return c.parseMagnetHTML(string(html))
}

// parseMagnetHTML 解析磁力链接HTML
func (c *Client) parseMagnetHTML(html string) ([]MagnetInfo, error) {
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(html))
	if err != nil {
		return nil, fmt.Errorf("解析磁力链接HTML失败: %v", err)
	}

	var magnets []MagnetInfo

	doc.Find("tr").Each(func(i int, s *goquery.Selection) {
		// 跳过表头
		if i == 0 {
			return
		}

		tds := s.Find("td")
		if tds.Length() < 4 {
			return
		}

		magnet := MagnetInfo{}

		// 获取磁力链接
		magnetLink := tds.Eq(0).Find("a")
		if href, exists := magnetLink.Attr("href"); exists && strings.HasPrefix(href, "magnet:") {
			magnet.MagnetURL = href
		} else {
			return // 没有有效的磁力链接，跳过
		}

		// 获取文件大小
		sizeText := strings.TrimSpace(tds.Eq(1).Text())
		if size, err := c.parseFileSize(sizeText); err == nil {
			magnet.FileSize = size
		}

		// 获取上传日期
		dateText := strings.TrimSpace(tds.Eq(2).Text())
		if uploadDate, err := time.Parse("2006-01-02", dateText); err == nil {
			magnet.UploadDate = uploadDate
		}

		// 获取做种数和下载数
		seedLeechText := strings.TrimSpace(tds.Eq(3).Text())
		if matches := regexp.MustCompile(`(\d+)/(\d+)`).FindStringSubmatch(seedLeechText); len(matches) == 3 {
			if seeders, err := strconv.Atoi(matches[1]); err == nil {
				magnet.Seeders = seeders
			}
			if leechers, err := strconv.Atoi(matches[2]); err == nil {
				magnet.Leechers = leechers
			}
		}

		// 从磁力链接中提取文件名
		if matches := regexp.MustCompile(`dn=([^&]+)`).FindStringSubmatch(magnet.MagnetURL); len(matches) > 1 {
			magnet.FileName = matches[1]
		}

		magnet.Source = "javbus"
		magnets = append(magnets, magnet)
	})

	return magnets, nil
}
*/

// parseFileSize 解析文件大小
func (c *Client) parseFileSize(sizeText string) (int64, error) {
	sizeText = strings.TrimSpace(sizeText)

	// 匹配数字和单位
	re := regexp.MustCompile(`([\d.]+)\s*([KMGT]?B)`)
	matches := re.FindStringSubmatch(sizeText)
	if len(matches) != 3 {
		return 0, fmt.Errorf("无法解析文件大小: %s", sizeText)
	}

	size, err := strconv.ParseFloat(matches[1], 64)
	if err != nil {
		return 0, fmt.Errorf("解析文件大小数值失败: %v", err)
	}

	unit := strings.ToUpper(matches[2])
	switch unit {
	case "B":
		return int64(size), nil
	case "KB":
		return int64(size * 1024), nil
	case "MB":
		return int64(size * 1024 * 1024), nil
	case "GB":
		return int64(size * 1024 * 1024 * 1024), nil
	case "TB":
		return int64(size * 1024 * 1024 * 1024 * 1024), nil
	default:
		return int64(size), nil
	}
}

// SearchMovies 搜索影片
func (c *Client) SearchMovies(keyword string, page int, pageSize int) (*SearchResult, error) {
	if !c.enabled {
		return &SearchResult{
			Movies: []MovieInfo{},
			Total:  0,
			Source: "javbus",
		}, nil
	}

	// 调用javbus-api搜索接口
	apiURL := fmt.Sprintf("%s/api/movies/search?keyword=%s&page=%d", c.apiBaseURL, keyword, page)
	
	ctx, cancel := context.WithTimeout(context.Background(), c.config.Timeout)
	defer cancel()

	response, err := c.baseClient.Get(ctx, apiURL, nil)
	if err != nil {
		return &SearchResult{
			Movies: []MovieInfo{},
			Total:  0,
			Source: "javbus",
		}, fmt.Errorf("API请求失败: %v", err)
	}

	var apiResp APIMoviesResponse
	if err := json.Unmarshal(response, &apiResp); err != nil {
		return &SearchResult{
			Movies: []MovieInfo{},
			Total:  0,
			Source: "javbus",
		}, fmt.Errorf("解析API响应失败: %v", err)
	}

	// 转换API数据
	var movies []MovieInfo
	for _, apiMovie := range apiResp.Movies {
		movieInfo := c.convertSimpleAPIMovieToMovieInfo(&apiMovie)
		movies = append(movies, *movieInfo)
	}

	return &SearchResult{
		Movies:   movies,
		Total:    len(movies),
		Page:     page,
		PageSize: pageSize,
		HasMore:  len(movies) >= pageSize,
		Source:   "javbus",
	}, nil
}

// GetLatestMovies 获取最新影片
func (c *Client) GetLatestMovies(page int, pageSize int) (*SearchResult, error) {
	if !c.enabled {
		return &SearchResult{
			Movies: []MovieInfo{},
			Total:  0,
			Source: "javbus",
		}, nil
	}

	// 调用javbus-api获取最新影片
	apiURL := fmt.Sprintf("%s/api/movies?page=%d", c.apiBaseURL, page)
	
	ctx, cancel := context.WithTimeout(context.Background(), c.config.Timeout)
	defer cancel()

	response, err := c.baseClient.Get(ctx, apiURL, nil)
	if err != nil {
		return &SearchResult{
			Movies: []MovieInfo{},
			Total:  0,
			Source: "javbus",
		}, fmt.Errorf("API请求失败: %v", err)
	}

	var apiResp APIMoviesResponse
	if err := json.Unmarshal(response, &apiResp); err != nil {
		return &SearchResult{
			Movies: []MovieInfo{},
			Total:  0,
			Source: "javbus",
		}, fmt.Errorf("解析API响应失败: %v", err)
	}

	// 转换API数据
	var movies []MovieInfo
	for _, apiMovie := range apiResp.Movies {
		movieInfo := c.convertSimpleAPIMovieToMovieInfo(&apiMovie)
		movies = append(movies, *movieInfo)
	}

	return &SearchResult{
		Movies:   movies,
		Total:    len(movies),
		Page:     page,
		PageSize: pageSize,
		HasMore:  len(movies) >= pageSize,
		Source:   "javbus",
	}, nil
}

// GetActorInfo 获取演员信息
func (c *Client) GetActorInfo(actorName string) (*ActorInfo, error) {
	if !c.enabled {
		return nil, fmt.Errorf("JavBus爬虫未启用")
	}

	// JavBus的演员信息获取功能实现
	// 这里可以根据实际的JavBus演员页面来实现
	// 暂时返回基本信息
	return &ActorInfo{
		Name: actorName,
	}, nil
}

// GetSourceName 获取数据源名称
func (c *Client) GetSourceName() string {
	return "javbus"
}

// IsEnabled 检查是否启用
func (c *Client) IsEnabled() bool {
	return c.enabled
}

// Close 关闭客户端
func (c *Client) Close() error {
	if c.baseClient != nil {
		return c.baseClient.Close()
	}
	return nil
}

// enrichActorAvatars 通过HTML解析补充演员头像信息
func (c *Client) enrichActorAvatars(movieInfo *MovieInfo, code string) error {
	// 构建影片页面URL
	movieURL := fmt.Sprintf("%s/%s", c.config.BaseURL, code)
	
	ctx, cancel := context.WithTimeout(context.Background(), c.config.Timeout)
	defer cancel()
	
	// 直接使用HTTP客户端获取HTML页面
	httpClient := &http.Client{Timeout: c.config.Timeout}
	req, err := http.NewRequestWithContext(ctx, "GET", movieURL, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}
	
	// 添加浏览器请求头（不要gzip压缩）
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2")
	// 不要gzip压缩，避免解压问题
	req.Header.Set("Accept-Encoding", "identity")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
	
	resp, err := httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("获取HTML页面失败: %v", err)
	}
	defer resp.Body.Close()
	
	// 读取响应内容
	response, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}
	
	// 解析HTML获取演员头像
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(string(response)))
	if err != nil {
		return fmt.Errorf("解析HTML失败: %v", err)
	}
	
	// 创建演员名称到头像URL的映射
	actorAvatars := make(map[string]string)
	
	logger.Infof("开始解析演员头像，URL: %s", movieURL)
	
	// 调试：保存HTML内容到文件
	htmlContent := string(response)
	
	// 保存HTML到文件用于调试
	if err := os.WriteFile("/tmp/javbus_debug.html", response, 0644); err == nil {
		logger.Infof("HTML内容已保存到 /tmp/javbus_debug.html")
	}
	
	if strings.Contains(htmlContent, "宍戸里帆") {
		logger.Infof("HTML中包含演员名称")
	} else {
		logger.Warnf("HTML中不包含演员名称")
	}
	
	if strings.Contains(htmlContent, "avatar-box") {
		logger.Infof("HTML中包含avatar-box")
	} else {
		logger.Warnf("HTML中不包含avatar-box")
	}
	
	// 检查HTML长度
	logger.Infof("HTML内容长度: %d 字节", len(htmlContent))
	
	// 解析演员信息和头像
	avatarBoxCount := 0
	doc.Find(".avatar-box").Each(func(i int, s *goquery.Selection) {
		avatarBoxCount++
		// 获取演员名称
		nameSpan := s.Find("span").Text()
		actorName := strings.TrimSpace(nameSpan)
		
		// 获取演员头像
		if img := s.Find("img").First(); img.Length() > 0 {
			if src, exists := img.Attr("src"); exists && src != "" {
				logger.Infof("找到演员: %s, 头像: %s", actorName, src)
				// 跳过默认头像
				if !strings.Contains(src, "nowprinting.gif") {
					// 如果是相对路径，转换为绝对路径
					if strings.HasPrefix(src, "/") {
						src = c.config.BaseURL + src
					}
					actorAvatars[actorName] = src
					logger.Infof("保存演员头像: %s -> %s", actorName, src)
				} else {
					logger.Infof("跳过默认头像: %s", src)
				}
			}
		}
	})
	
	logger.Infof("找到 %d 个 avatar-box 元素", avatarBoxCount)
	logger.Infof("解析到 %d 个演员头像", len(actorAvatars))
	
	// 更新MovieInfo中的演员头像URL
	for i := range movieInfo.Actors {
		if avatarURL, exists := actorAvatars[movieInfo.Actors[i].Name]; exists {
			movieInfo.Actors[i].AvatarURL = avatarURL
			logger.Infof("更新演员头像: %s -> %s", movieInfo.Actors[i].Name, avatarURL)
		} else {
			logger.Warnf("未找到演员头像: %s", movieInfo.Actors[i].Name)
		}
	}
	
	return nil
}
