package selector

import (
	"fmt"
	"math"
	"regexp"
	"sort"
	"strings"
	"time"

	"magnet-downloader/pkg/logger"
)

// SelectorWeights 磁力筛选算法权重配置
type SelectorWeights struct {
	SizeWeight     float64 `json:"size_weight"`     // 文件大小权重 (默认: 40%)
	SubtitleWeight float64 `json:"subtitle_weight"` // 字幕权重 (默认: 35%)
	QualityWeight  float64 `json:"quality_weight"`  // 清晰度权重 (默认: 15%)
	SourceWeight   float64 `json:"source_weight"`   // 来源可靠性权重 (默认: 10%)
}

// MagnetInfo 磁力链接信息结构（本地定义避免循环导入）
type MagnetInfo struct {
	MagnetURL        string    `json:"magnet_url"`
	FileName         string    `json:"file_name"`
	FileSize         int64     `json:"file_size"`
	Quality          string    `json:"quality"`
	HasSubtitle      bool      `json:"has_subtitle"`
	SubtitleLanguage string    `json:"subtitle_language"`
	Source           string    `json:"source"`
	Uploader         string    `json:"uploader"`
	UploadDate       time.Time `json:"upload_date"`
	Seeders          int       `json:"seeders"`
	Leechers         int       `json:"leechers"`
	
	// 评分字段
	Score         float64 `json:"score"`
	SizeScore     float64 `json:"size_score"`
	SubtitleScore float64 `json:"subtitle_score"`
	QualityScore  float64 `json:"quality_score"`
	SourceScore   float64 `json:"source_score"`
	HealthScore   float64 `json:"health_score"`
}

// ScoredMagnet 带评分的磁力链接
type ScoredMagnet struct {
	*MagnetInfo
	FinalScore float64 `json:"final_score"`
	Rank       int     `json:"rank"`
}

// SelectionResult 筛选结果
type SelectionResult struct {
	BestMagnet     *ScoredMagnet   `json:"best_magnet"`
	AllMagnets     []*ScoredMagnet `json:"all_magnets"`
	TotalCount     int             `json:"total_count"`
	ValidCount     int             `json:"valid_count"`
	SelectionTime  time.Duration   `json:"selection_time"`
	SelectionRules []string        `json:"selection_rules"`
}

// MagnetSelector 磁力链接筛选器
type MagnetSelector struct {
	weights *SelectorWeights
}

// NewMagnetSelector 创建新的磁力链接筛选器
func NewMagnetSelector(weights *SelectorWeights) *MagnetSelector {
	if weights == nil {
		weights = DefaultSelectorWeights()
	}
	
	// 验证权重总和
	if err := validateWeights(weights); err != nil {
		logger.Warnf("权重配置无效，使用默认配置: %v", err)
		weights = DefaultSelectorWeights()
	}
	
	return &MagnetSelector{
		weights: weights,
	}
}

// DefaultSelectorWeights 默认筛选权重
func DefaultSelectorWeights() *SelectorWeights {
	return &SelectorWeights{
		SizeWeight:     0.40, // 40%
		SubtitleWeight: 0.35, // 35%
		QualityWeight:  0.15, // 15%
		SourceWeight:   0.10, // 10%
	}
}

// validateWeights 验证权重配置
func validateWeights(weights *SelectorWeights) error {
	total := weights.SizeWeight + weights.SubtitleWeight + weights.QualityWeight + weights.SourceWeight
	if math.Abs(total-1.0) > 0.01 {
		return fmt.Errorf("权重总和应为1.0，当前为%.2f", total)
	}
	
	if weights.SizeWeight < 0 || weights.SubtitleWeight < 0 || weights.QualityWeight < 0 || weights.SourceWeight < 0 {
		return fmt.Errorf("权重不能为负数")
	}
	
	return nil
}

// SelectBestMagnet 选择最佳磁力链接
func (s *MagnetSelector) SelectBestMagnet(magnets []*MagnetInfo) (*SelectionResult, error) {
	startTime := time.Now()
	
	if len(magnets) == 0 {
		return &SelectionResult{
			TotalCount:    0,
			ValidCount:    0,
			SelectionTime: time.Since(startTime),
		}, nil
	}
	
	// 预处理磁力链接
	processedMagnets := s.preprocessMagnets(magnets)
	
	// 计算评分
	scoredMagnets := s.scoreMagnets(processedMagnets)
	
	// 排序
	sort.Slice(scoredMagnets, func(i, j int) bool {
		return scoredMagnets[i].FinalScore > scoredMagnets[j].FinalScore
	})
	
	// 设置排名
	for i, magnet := range scoredMagnets {
		magnet.Rank = i + 1
	}
	
	result := &SelectionResult{
		AllMagnets:     scoredMagnets,
		TotalCount:     len(magnets),
		ValidCount:     len(scoredMagnets),
		SelectionTime:  time.Since(startTime),
		SelectionRules: s.getSelectionRules(),
	}
	
	if len(scoredMagnets) > 0 {
		result.BestMagnet = scoredMagnets[0]
		logger.Infof("选择最佳磁力链接: 评分%.2f, 文件大小%s, 字幕%v", 
			result.BestMagnet.FinalScore, 
			formatBytes(result.BestMagnet.FileSize),
			result.BestMagnet.HasSubtitle)
	}
	
	return result, nil
}

// preprocessMagnets 预处理磁力链接
func (s *MagnetSelector) preprocessMagnets(magnets []*MagnetInfo) []*MagnetInfo {
	var processed []*MagnetInfo
	
	for _, magnet := range magnets {
		// 验证磁力链接
		if !s.isValidMagnet(magnet) {
			logger.Debugf("跳过无效磁力链接: %s", magnet.MagnetURL)
			continue
		}
		
		// 创建副本避免修改原数据
		processedMagnet := *magnet
		
		// 解析文件名中的信息
		s.parseFileNameInfo(&processedMagnet)
		
		processed = append(processed, &processedMagnet)
	}
	
	return processed
}

// isValidMagnet 验证磁力链接是否有效
func (s *MagnetSelector) isValidMagnet(magnet *MagnetInfo) bool {
	// 检查磁力链接格式
	if !strings.HasPrefix(magnet.MagnetURL, "magnet:") {
		return false
	}
	
	// 检查文件大小（至少100MB）
	if magnet.FileSize < 100*1024*1024 {
		return false
	}
	
	// 检查是否有基本信息
	if magnet.FileName == "" && magnet.FileSize == 0 {
		return false
	}
	
	return true
}

// parseFileNameInfo 从文件名解析信息
func (s *MagnetSelector) parseFileNameInfo(magnet *MagnetInfo) {
	if magnet.FileName == "" {
		// 尝试从磁力链接中提取文件名
		if matches := regexp.MustCompile(`dn=([^&]+)`).FindStringSubmatch(magnet.MagnetURL); len(matches) > 1 {
			magnet.FileName = matches[1]
		}
	}
	
	if magnet.FileName == "" {
		return
	}
	
	fileName := strings.ToLower(magnet.FileName)
	
	// 解析视频质量
	if magnet.Quality == "" {
		magnet.Quality = s.parseQuality(fileName)
	}
	
	// 解析字幕信息
	if !magnet.HasSubtitle {
		magnet.HasSubtitle, magnet.SubtitleLanguage = s.parseSubtitle(fileName)
	}
}

// parseQuality 解析视频质量
func (s *MagnetSelector) parseQuality(fileName string) string {
	qualityPatterns := map[string][]string{
		"4K": {`\b(4k|uhd|2160p)\b`},
		"1080p": {`\b1080p?\b`},
		"720p": {`\b720p?\b`},
		"480p": {`\b480p?\b`},
	}
	
	for quality, patterns := range qualityPatterns {
		for _, pattern := range patterns {
			if matched, _ := regexp.MatchString(pattern, fileName); matched {
				return quality
			}
		}
	}
	
	return "unknown"
}

// parseSubtitle 解析字幕信息
func (s *MagnetSelector) parseSubtitle(fileName string) (bool, string) {
	// 中文字幕检测（包含中文字符）
	chinesePatterns := []string{
		`(中文|中字|字幕)`,
		`\b(chs|cht|chinese)\b`,
		`(简体|繁体|简中|繁中)`,
	}
	
	for _, pattern := range chinesePatterns {
		if matched, _ := regexp.MatchString(pattern, fileName); matched {
			return true, "chinese"
		}
	}
	
	// 英文字幕检测（避免与中文字幕冲突）
	englishPatterns := []string{
		`\b(english|eng)\b`,
		`\bsub.*eng\b`,
	}
	
	for _, pattern := range englishPatterns {
		if matched, _ := regexp.MatchString(pattern, fileName); matched {
			return true, "english"
		}
	}
	
	// 日文字幕检测
	japanesePatterns := []string{
		`\b(japanese|jpn|jp)\b`,
		`(日文|日语|日字)`,
	}
	
	for _, pattern := range japanesePatterns {
		if matched, _ := regexp.MatchString(pattern, fileName); matched {
			return true, "japanese"
		}
	}
	
	return false, "none"
}

// scoreMagnets 为磁力链接评分
func (s *MagnetSelector) scoreMagnets(magnets []*MagnetInfo) []*ScoredMagnet {
	var scored []*ScoredMagnet
	
	for _, magnet := range magnets {
		// 计算各项评分
		sizeScore := s.calculateSizeScore(magnet)
		subtitleScore := s.calculateSubtitleScore(magnet)
		qualityScore := s.calculateQualityScore(magnet)
		sourceScore := s.calculateSourceScore(magnet)
		healthScore := s.calculateHealthScore(magnet)
		
		// 计算加权总分
		finalScore := sizeScore*s.weights.SizeWeight +
			subtitleScore*s.weights.SubtitleWeight +
			qualityScore*s.weights.QualityWeight +
			sourceScore*s.weights.SourceWeight
		
		// 健康度作为额外加分项（最多加10分）
		finalScore += healthScore * 0.1
		
		// 确保评分在合理范围内
		if finalScore > 100 {
			finalScore = 100
		}
		
		// 更新磁力链接的评分字段
		magnet.Score = finalScore
		magnet.SizeScore = sizeScore
		magnet.SubtitleScore = subtitleScore
		magnet.QualityScore = qualityScore
		magnet.SourceScore = sourceScore
		magnet.HealthScore = healthScore
		
		scored = append(scored, &ScoredMagnet{
			MagnetInfo: magnet,
			FinalScore: finalScore,
		})
	}
	
	return scored
}

// calculateSizeScore 计算文件大小评分
func (s *MagnetSelector) calculateSizeScore(magnet *MagnetInfo) float64 {
	if magnet.FileSize <= 0 {
		return 0
	}
	
	// 转换为GB
	sizeGB := float64(magnet.FileSize) / (1024 * 1024 * 1024)
	
	switch {
	case sizeGB >= 4.0:
		return 100.0 // >4GB: 100分
	case sizeGB >= 2.0:
		return 80.0 // 2-4GB: 80分
	case sizeGB >= 1.0:
		return 60.0 // 1-2GB: 60分
	case sizeGB >= 0.5:
		return 30.0 // 0.5-1GB: 30分
	default:
		return 10.0 // <0.5GB: 10分
	}
}

// calculateSubtitleScore 计算字幕评分
func (s *MagnetSelector) calculateSubtitleScore(magnet *MagnetInfo) float64 {
	if !magnet.HasSubtitle {
		return 0.0 // 无字幕: 0分
	}
	
	switch magnet.SubtitleLanguage {
	case "chinese":
		return 100.0 // 中文字幕: 100分
	case "english":
		return 60.0 // 英文字幕: 60分
	case "japanese":
		return 30.0 // 日文字幕: 30分
	default:
		return 20.0 // 其他字幕: 20分
	}
}

// calculateQualityScore 计算清晰度评分
func (s *MagnetSelector) calculateQualityScore(magnet *MagnetInfo) float64 {
	switch magnet.Quality {
	case "4K":
		return 100.0 // 4K/UHD: 100分
	case "1080p":
		return 80.0 // 1080p: 80分
	case "720p":
		return 60.0 // 720p: 60分
	case "480p":
		return 40.0 // 480p: 40分
	default:
		return 50.0 // 未知: 50分
	}
}

// calculateSourceScore 计算来源可靠性评分
func (s *MagnetSelector) calculateSourceScore(magnet *MagnetInfo) float64 {
	if magnet.Source == "" && magnet.Uploader == "" && magnet.FileName == "" {
		return 50.0 // 未知来源: 50分
	}
	
	// 知名发布组列表
	knownGroups := []string{
		"fhd", "hd", "uncensored", "leaked", "1080p", "720p",
		"javhd", "javfull", "javmost", "javfree", "javdoe",
		"thz", "sukebei", "nyaa", "rarbg", "1337x",
		"carib", "caribbeancom", "1pondo", "10musume",
		"pacopacomama", "muramura", "heyzo", "fc2",
	}
	
	source := strings.ToLower(magnet.Source + " " + magnet.Uploader + " " + magnet.FileName)
	
	for _, group := range knownGroups {
		if strings.Contains(source, group) {
			return 100.0 // 知名发布组: 100分
		}
	}
	
	// 检查是否有明确的发布组标识
	if regexp.MustCompile(`\[[^\]]+\]`).MatchString(source) {
		return 70.0 // 一般来源: 70分
	}
	
	return 50.0 // 未知来源: 50分
}

// calculateHealthScore 计算健康度评分
func (s *MagnetSelector) calculateHealthScore(magnet *MagnetInfo) float64 {
	if magnet.Seeders == 0 && magnet.Leechers == 0 {
		return 0.0
	}
	
	total := magnet.Seeders + magnet.Leechers
	if total == 0 {
		return 0.0
	}
	
	// 健康度 = 做种数 / (做种数 + 下载数) * 100
	healthScore := float64(magnet.Seeders) / float64(total) * 100
	
	// 考虑总活跃度
	if total >= 100 {
		healthScore += 10 // 高活跃度奖励
	} else if total >= 50 {
		healthScore += 5 // 中等活跃度奖励
	}
	
	if healthScore > 100 {
		healthScore = 100
	}
	
	return healthScore
}

// getSelectionRules 获取筛选规则说明
func (s *MagnetSelector) getSelectionRules() []string {
	return []string{
		fmt.Sprintf("文件大小权重: %.0f%%", s.weights.SizeWeight*100),
		fmt.Sprintf("字幕权重: %.0f%%", s.weights.SubtitleWeight*100),
		fmt.Sprintf("清晰度权重: %.0f%%", s.weights.QualityWeight*100),
		fmt.Sprintf("来源可靠性权重: %.0f%%", s.weights.SourceWeight*100),
		"优先级: 带中文字幕的最大文件 > 无字幕的最大文件",
		"最小文件大小: 100MB",
		"健康度作为额外加分项",
	}
}

// UpdateWeights 更新权重配置
func (s *MagnetSelector) UpdateWeights(weights *SelectorWeights) error {
	if err := validateWeights(weights); err != nil {
		return err
	}
	
	s.weights = weights
	logger.Infof("磁力筛选权重已更新: 大小%.0f%%, 字幕%.0f%%, 清晰度%.0f%%, 来源%.0f%%",
		weights.SizeWeight*100, weights.SubtitleWeight*100,
		weights.QualityWeight*100, weights.SourceWeight*100)
	
	return nil
}

// GetWeights 获取当前权重配置
func (s *MagnetSelector) GetWeights() *SelectorWeights {
	return s.weights
}

// FilterByMinScore 按最小评分过滤磁力链接
func (s *MagnetSelector) FilterByMinScore(magnets []*ScoredMagnet, minScore float64) []*ScoredMagnet {
	var filtered []*ScoredMagnet
	for _, magnet := range magnets {
		if magnet.FinalScore >= minScore {
			filtered = append(filtered, magnet)
		}
	}
	return filtered
}

// FilterBySubtitle 按字幕过滤磁力链接
func (s *MagnetSelector) FilterBySubtitle(magnets []*ScoredMagnet, requireSubtitle bool, preferredLanguage string) []*ScoredMagnet {
	var filtered []*ScoredMagnet
	for _, magnet := range magnets {
		if requireSubtitle && !magnet.HasSubtitle {
			continue
		}
		
		if preferredLanguage != "" && magnet.SubtitleLanguage != preferredLanguage {
			continue
		}
		
		filtered = append(filtered, magnet)
	}
	return filtered
}

// FilterByQuality 按清晰度过滤磁力链接
func (s *MagnetSelector) FilterByQuality(magnets []*ScoredMagnet, minQuality string) []*ScoredMagnet {
	qualityOrder := map[string]int{
		"4K":      4,
		"1080p":   3,
		"720p":    2,
		"480p":    1,
		"unknown": 0,
	}
	
	minLevel := qualityOrder[minQuality]
	var filtered []*ScoredMagnet
	
	for _, magnet := range magnets {
		if qualityOrder[magnet.Quality] >= minLevel {
			filtered = append(filtered, magnet)
		}
	}
	
	return filtered
}

// GetStatistics 获取筛选统计信息
func (s *MagnetSelector) GetStatistics(result *SelectionResult) map[string]interface{} {
	if result == nil || len(result.AllMagnets) == 0 {
		return map[string]interface{}{
			"total_count": 0,
			"valid_count": 0,
		}
	}
	
	stats := map[string]interface{}{
		"total_count":    result.TotalCount,
		"valid_count":    result.ValidCount,
		"selection_time": result.SelectionTime.String(),
	}
	
	// 统计各项指标
	var totalScore, totalSize float64
	qualityCount := make(map[string]int)
	subtitleCount := make(map[string]int)
	
	for _, magnet := range result.AllMagnets {
		totalScore += magnet.FinalScore
		totalSize += float64(magnet.FileSize)
		
		qualityCount[magnet.Quality]++
		if magnet.HasSubtitle {
			subtitleCount[magnet.SubtitleLanguage]++
		} else {
			subtitleCount["none"]++
		}
	}
	
	stats["average_score"] = totalScore / float64(len(result.AllMagnets))
	stats["average_size"] = formatBytes(int64(totalSize / float64(len(result.AllMagnets))))
	stats["quality_distribution"] = qualityCount
	stats["subtitle_distribution"] = subtitleCount
	
	if result.BestMagnet != nil {
		stats["best_magnet"] = map[string]interface{}{
			"score":    result.BestMagnet.FinalScore,
			"size":     formatBytes(result.BestMagnet.FileSize),
			"quality":  result.BestMagnet.Quality,
			"subtitle": result.BestMagnet.HasSubtitle,
		}
	}
	
	return stats
}

// formatBytes 格式化字节大小
func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}