package javsp

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"magnet-downloader/pkg/javscraper/client"
	"magnet-downloader/pkg/logger"
)

// JavSP包装器API响应结构体
type WrapperAPIResponse struct {
	Success bool                   `json:"success"`
	Data    *WrapperMovieInfo      `json:"data"`
	Code    string                 `json:"code"`
	Source  string                 `json:"source"`
	Error   string                 `json:"error,omitempty"`
}

type WrapperMovieInfo struct {
	Code        string   `json:"code"`
	Title       string   `json:"title"`
	Plot        string   `json:"plot"`
	Cover       string   `json:"cover"`
	BigCover    string   `json:"big_cover"`
	Genre       []string `json:"genre"`
	Actress     []string `json:"actress"`
	Director    string   `json:"director"`
	Duration    string   `json:"duration"`
	Producer    string   `json:"producer"`
	Publisher   string   `json:"publisher"`
	ReleaseDate string   `json:"release_date"`
	Serial      string   `json:"serial"`
	Score       string   `json:"score"`
	URL         string   `json:"url"`
	Magnet      string   `json:"magnet"`
	Source      string   `json:"source"`
	ScrapedAt   string   `json:"scraped_at"`
}

// Adapter JavSP数据源适配器
type Adapter struct {
	baseClient     *client.BaseClient
	config         *Config
	enabled        bool
	sources        map[string]SourceHandler
	wrapperBaseURL string
}

// SourceHandler 数据源处理器接口
type SourceHandler interface {
	GetMovieByCode(code string) (*MovieInfo, error)
	GetSourceName() string
	IsEnabled() bool
}

// NewAdapter 创建新的JavSP适配器
func NewAdapter(config *Config, userAgent string, wrapperBaseURL ...string) *Adapter {
	if config == nil {
		config = &Config{
			Enabled:   true,
			Sources:   []string{"javdb", "javlibrary", "avsox"},
			Timeout:   30 * time.Second,
			RateLimit: 2 * time.Second,
		}
	}

	baseClient := client.NewBaseClient(
		userAgent,
		config.Timeout,
		3, // maxRetries
		config.RateLimit,
	)

	// 设置包装器URL
	defaultWrapperURL := "http://localhost:3002"
	if len(wrapperBaseURL) > 0 && wrapperBaseURL[0] != "" {
		defaultWrapperURL = wrapperBaseURL[0]
	}

	adapter := &Adapter{
		baseClient:     baseClient,
		config:         config,
		enabled:        config.Enabled,
		sources:        make(map[string]SourceHandler),
		wrapperBaseURL: defaultWrapperURL,
	}

	// 初始化数据源处理器
	adapter.initSourceHandlers()

	return adapter
}

// getMovieFromWrapper 从JavSP包装器获取影片信息
func (a *Adapter) getMovieFromWrapper(code string) (*MovieInfo, error) {
	// 构建API URL
	apiURL := fmt.Sprintf("%s/api/movie/%s", a.wrapperBaseURL, code)
	
	ctx, cancel := context.WithTimeout(context.Background(), a.config.Timeout)
	defer cancel()

	// 发送HTTP请求
	response, err := a.baseClient.Get(ctx, apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("JavSP包装器API请求失败: %v", err)
	}

	// 解析API响应
	var apiResp WrapperAPIResponse
	if err := json.Unmarshal(response, &apiResp); err != nil {
		return nil, fmt.Errorf("解析JavSP包装器API响应失败: %v", err)
	}

	if !apiResp.Success {
		return nil, fmt.Errorf("JavSP包装器API返回错误: %s", apiResp.Error)
	}

	if apiResp.Data == nil {
		return nil, fmt.Errorf("JavSP包装器API返回空数据")
	}

	// 转换API数据为MovieInfo
	movieInfo := a.convertWrapperDataToMovieInfo(apiResp.Data)
	return movieInfo, nil
}

// convertWrapperDataToMovieInfo 将包装器API数据转换为MovieInfo
func (a *Adapter) convertWrapperDataToMovieInfo(data *WrapperMovieInfo) *MovieInfo {
	movieInfo := &MovieInfo{
		Code:     data.Code,
		Title:    data.Title,
		Plot:     data.Plot,
		CoverURL: data.Cover,
		Studio:   data.Producer,
		Series:   data.Serial,
		Director: data.Director,
		Source:   "javsp-wrapper",
		ScrapedAt: time.Now(),
		Confidence: 0.8, // JavSP包装器的基础可信度
	}

	// 解析发布日期
	if data.ReleaseDate != "" {
		if releaseDate, err := time.Parse("2006-01-02", data.ReleaseDate); err == nil {
			movieInfo.ReleaseDate = releaseDate
		}
	}

	// 解析时长
	if data.Duration != "" {
		if duration, err := time.ParseDuration(data.Duration + "m"); err == nil {
			movieInfo.Duration = int(duration.Minutes())
		}
	}

	// 转换演员信息
	for _, actress := range data.Actress {
		movieInfo.Actors = append(movieInfo.Actors, ActorInfo{
			Name: actress,
		})
	}

	// 转换分类信息
	for _, genre := range data.Genre {
		movieInfo.Genres = append(movieInfo.Genres, GenreInfo{
			Name: genre,
		})
	}

	// 添加磁力链接
	if data.Magnet != "" {
		movieInfo.Magnets = append(movieInfo.Magnets, MagnetInfo{
			MagnetURL: data.Magnet,
			Source:    "javsp-wrapper",
		})
	}

	return movieInfo
}

// checkWrapperHealth 检查包装器服务健康状态
func (a *Adapter) checkWrapperHealth() error {
	healthURL := fmt.Sprintf("%s/health", a.wrapperBaseURL)
	
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := a.baseClient.Get(ctx, healthURL, nil)
	if err != nil {
		return fmt.Errorf("JavSP包装器服务不可用: %v", err)
	}
	
	return nil
}

// initSourceHandlers 初始化数据源处理器
func (a *Adapter) initSourceHandlers() {
	for _, sourceName := range a.config.Sources {
		switch sourceName {
		case "javdb":
			a.sources[sourceName] = NewJavDBHandler(a.baseClient)
		case "javlibrary":
			a.sources[sourceName] = NewJavLibraryHandler(a.baseClient)
		case "avsox":
			a.sources[sourceName] = NewAvsoxHandler(a.baseClient)
		case "javmenu":
			a.sources[sourceName] = NewJavMenuHandler(a.baseClient)
		case "fanza":
			a.sources[sourceName] = NewFanzaHandler(a.baseClient)
		default:
			logger.Warnf("未知的JavSP数据源: %s", sourceName)
		}
	}
}

// GetMovieByCode 根据番号获取影片信息
func (a *Adapter) GetMovieByCode(code string) (*ScrapingResult, error) {
	if !a.enabled {
		return &ScrapingResult{
			Success: false,
			Error:   "JavSP适配器未启用",
			Source:  "javsp",
		}, nil
	}

	startTime := time.Now()

	// 首先尝试调用JavSP包装器API
	movieInfo, err := a.getMovieFromWrapper(code)
	if err == nil && movieInfo != nil {
		return &ScrapingResult{
			Success:   true,
			MovieInfo: movieInfo,
			Source:    "javsp",
			Duration:  time.Since(startTime),
		}, nil
	}

	logger.Warnf("JavSP包装器调用失败，回退到传统数据源: %v", err)

	// 回退到传统的多数据源方式
	var lastErr error
	var bestResult *MovieInfo
	var allResults []*MovieInfo

	// 尝试从各个数据源获取信息
	for sourceName, handler := range a.sources {
		if !handler.IsEnabled() {
			continue
		}

		logger.Debugf("从%s获取影片信息: %s", sourceName, code)
		
		movieInfo, err := handler.GetMovieByCode(code)
		if err != nil {
			logger.Warnf("从%s获取影片信息失败: %v", sourceName, err)
			lastErr = err
			continue
		}

		if movieInfo != nil {
			// 设置数据源信息
			movieInfo.Source = fmt.Sprintf("javsp-%s", sourceName)
			movieInfo.ScrapedAt = time.Now()
			movieInfo.Confidence = a.calculateConfidence(sourceName, movieInfo)

			allResults = append(allResults, movieInfo)

			// 选择最佳结果
			if bestResult == nil || a.isResultBetter(movieInfo, bestResult) {
				bestResult = movieInfo
			}
		}
	}

	if bestResult == nil {
		return &ScrapingResult{
			Success:  false,
			Error:    fmt.Sprintf("所有JavSP数据源都无法获取影片信息: %v", lastErr),
			Source:   "javsp",
			Duration: time.Since(startTime),
		}, nil
	}

	// 合并多个数据源的信息
	mergedResult := a.mergeResults(allResults)
	if mergedResult != nil {
		bestResult = mergedResult
	}

	return &ScrapingResult{
		Success:   true,
		MovieInfo: bestResult,
		Source:    "javsp",
		Duration:  time.Since(startTime),
	}, nil
}

// calculateConfidence 计算数据可信度
func (a *Adapter) calculateConfidence(sourceName string, movieInfo *MovieInfo) float64 {
	baseConfidence := map[string]float64{
		"javdb":      0.85,
		"javlibrary": 0.8,
		"avsox":      0.75,
		"javmenu":    0.7,
		"fanza":      0.9,
	}

	confidence := baseConfidence[sourceName]
	if confidence == 0 {
		confidence = 0.6 // 默认可信度
	}

	// 根据数据完整性调整可信度
	if movieInfo.Title != "" {
		confidence += 0.02
	}
	if len(movieInfo.Actors) > 0 {
		confidence += 0.03
	}
	if len(movieInfo.Genres) > 0 {
		confidence += 0.02
	}
	if movieInfo.CoverURL != "" {
		confidence += 0.02
	}
	if movieInfo.Rating > 0 {
		confidence += 0.03
	}

	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// isResultBetter 判断结果是否更好
func (a *Adapter) isResultBetter(newResult, currentResult *MovieInfo) bool {
	// 优先级顺序：fanza > javdb > javlibrary > avsox > javmenu
	sourcePriority := map[string]int{
		"javsp-fanza":      1,
		"javsp-javdb":      2,
		"javsp-javlibrary": 3,
		"javsp-avsox":      4,
		"javsp-javmenu":    5,
	}

	newPriority := sourcePriority[newResult.Source]
	currentPriority := sourcePriority[currentResult.Source]

	if newPriority == 0 {
		newPriority = 999
	}
	if currentPriority == 0 {
		currentPriority = 999
	}

	return newPriority < currentPriority
}

// mergeResults 合并多个数据源的结果
func (a *Adapter) mergeResults(results []*MovieInfo) *MovieInfo {
	if len(results) == 0 {
		return nil
	}

	if len(results) == 1 {
		return results[0]
	}

	// 以最高优先级的结果为基础
	merged := *results[0]
	
	// 合并其他结果的信息
	for _, result := range results[1:] {
		// 补充缺失的基本信息
		if merged.Title == "" && result.Title != "" {
			merged.Title = result.Title
		}
		if merged.TitleEn == "" && result.TitleEn != "" {
			merged.TitleEn = result.TitleEn
		}
		if merged.Plot == "" && result.Plot != "" {
			merged.Plot = result.Plot
		}
		if merged.PlotEn == "" && result.PlotEn != "" {
			merged.PlotEn = result.PlotEn
		}
		if merged.Studio == "" && result.Studio != "" {
			merged.Studio = result.Studio
		}
		if merged.Series == "" && result.Series != "" {
			merged.Series = result.Series
		}
		if merged.Director == "" && result.Director != "" {
			merged.Director = result.Director
		}
		if merged.CoverURL == "" && result.CoverURL != "" {
			merged.CoverURL = result.CoverURL
		}
		if merged.PosterURL == "" && result.PosterURL != "" {
			merged.PosterURL = result.PosterURL
		}
		if merged.Rating == 0 && result.Rating > 0 {
			merged.Rating = result.Rating
		}

		// 合并演员信息（去重）
		for _, actor := range result.Actors {
			found := false
			for _, existingActor := range merged.Actors {
				if existingActor.Name == actor.Name {
					found = true
					// 补充演员详细信息
					if existingActor.NameEn == "" && actor.NameEn != "" {
						existingActor.NameEn = actor.NameEn
					}
					if existingActor.AvatarURL == "" && actor.AvatarURL != "" {
						existingActor.AvatarURL = actor.AvatarURL
					}
					break
				}
			}
			if !found {
				merged.Actors = append(merged.Actors, actor)
			}
		}

		// 合并分类信息（去重）
		for _, genre := range result.Genres {
			found := false
			for _, existingGenre := range merged.Genres {
				if existingGenre.Name == genre.Name {
					found = true
					break
				}
			}
			if !found {
				merged.Genres = append(merged.Genres, genre)
			}
		}

		// 合并磁力链接（去重）
		for _, magnet := range result.Magnets {
			found := false
			for _, existingMagnet := range merged.Magnets {
				if existingMagnet.MagnetURL == magnet.MagnetURL {
					found = true
					break
				}
			}
			if !found {
				merged.Magnets = append(merged.Magnets, magnet)
			}
		}
	}

	merged.Source = "javsp-merged"
	merged.Confidence = 0.8 // 合并结果的可信度

	return &merged
}

// SearchMovies 搜索影片
func (a *Adapter) SearchMovies(keyword string, page int, pageSize int) (*SearchResult, error) {
	if !a.enabled {
		return &SearchResult{
			Movies: []MovieInfo{},
			Total:  0,
			Source: "javsp",
		}, nil
	}

	// JavSP主要用于数据缺漏补充，搜索功能暂不实现
	return &SearchResult{
		Movies:   []MovieInfo{},
		Total:    0,
		Page:     page,
		PageSize: pageSize,
		HasMore:  false,
		Source:   "javsp",
	}, nil
}

// GetLatestMovies 获取最新影片
func (a *Adapter) GetLatestMovies(page int, pageSize int) (*SearchResult, error) {
	if !a.enabled {
		return &SearchResult{
			Movies: []MovieInfo{},
			Total:  0,
			Source: "javsp",
		}, nil
	}

	// JavSP主要用于数据缺漏补充，最新影片功能暂不实现
	return &SearchResult{
		Movies:   []MovieInfo{},
		Total:    0,
		Page:     page,
		PageSize: pageSize,
		HasMore:  false,
		Source:   "javsp",
	}, nil
}

// GetActorInfo 获取演员信息
func (a *Adapter) GetActorInfo(actorName string) (*ActorInfo, error) {
	if !a.enabled {
		return nil, fmt.Errorf("JavSP适配器未启用")
	}

	// 尝试从各个数据源获取演员信息
	for sourceName, handler := range a.sources {
		if !handler.IsEnabled() {
			continue
		}

		logger.Debugf("从%s获取演员信息: %s", sourceName, actorName)
	}

	// 暂时返回基本信息
	return &ActorInfo{
		Name: actorName,
	}, nil
}

// GetSourceName 获取数据源名称
func (a *Adapter) GetSourceName() string {
	return "javsp"
}

// IsEnabled 检查是否启用
func (a *Adapter) IsEnabled() bool {
	return a.enabled
}

// Close 关闭适配器
func (a *Adapter) Close() error {
	if a.baseClient != nil {
		return a.baseClient.Close()
	}
	return nil
}

// 以下是各个数据源处理器的简化实现

// JavDBHandler JavDB处理器
type JavDBHandler struct {
	baseClient *client.BaseClient
}

func NewJavDBHandler(baseClient *client.BaseClient) *JavDBHandler {
	return &JavDBHandler{baseClient: baseClient}
}

func (h *JavDBHandler) GetMovieByCode(code string) (*MovieInfo, error) {
	return &MovieInfo{
		Code:   code,
		Source: "javsp-javdb",
	}, nil
}

func (h *JavDBHandler) GetSourceName() string { return "javdb" }
func (h *JavDBHandler) IsEnabled() bool       { return true }

// JavLibraryHandler JavLibrary处理器
type JavLibraryHandler struct {
	baseClient *client.BaseClient
}

func NewJavLibraryHandler(baseClient *client.BaseClient) *JavLibraryHandler {
	return &JavLibraryHandler{baseClient: baseClient}
}

func (h *JavLibraryHandler) GetMovieByCode(code string) (*MovieInfo, error) {
	return &MovieInfo{
		Code:   code,
		Source: "javsp-javlibrary",
	}, nil
}

func (h *JavLibraryHandler) GetSourceName() string { return "javlibrary" }
func (h *JavLibraryHandler) IsEnabled() bool       { return true }

// AvsoxHandler Avsox处理器
type AvsoxHandler struct {
	baseClient *client.BaseClient
}

func NewAvsoxHandler(baseClient *client.BaseClient) *AvsoxHandler {
	return &AvsoxHandler{baseClient: baseClient}
}

func (h *AvsoxHandler) GetMovieByCode(code string) (*MovieInfo, error) {
	return &MovieInfo{
		Code:   code,
		Source: "javsp-avsox",
	}, nil
}

func (h *AvsoxHandler) GetSourceName() string { return "avsox" }
func (h *AvsoxHandler) IsEnabled() bool       { return true }

// JavMenuHandler JavMenu处理器
type JavMenuHandler struct {
	baseClient *client.BaseClient
}

func NewJavMenuHandler(baseClient *client.BaseClient) *JavMenuHandler {
	return &JavMenuHandler{baseClient: baseClient}
}

func (h *JavMenuHandler) GetMovieByCode(code string) (*MovieInfo, error) {
	return &MovieInfo{
		Code:   code,
		Source: "javsp-javmenu",
	}, nil
}

func (h *JavMenuHandler) GetSourceName() string { return "javmenu" }
func (h *JavMenuHandler) IsEnabled() bool       { return true }

// FanzaHandler Fanza处理器
type FanzaHandler struct {
	baseClient *client.BaseClient
}

func NewFanzaHandler(baseClient *client.BaseClient) *FanzaHandler {
	return &FanzaHandler{baseClient: baseClient}
}

func (h *FanzaHandler) GetMovieByCode(code string) (*MovieInfo, error) {
	return &MovieInfo{
		Code:   code,
		Source: "javsp-fanza",
	}, nil
}

func (h *FanzaHandler) GetSourceName() string { return "fanza" }
func (h *FanzaHandler) IsEnabled() bool       { return true }