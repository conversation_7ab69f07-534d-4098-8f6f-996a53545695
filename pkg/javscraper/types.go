package javscraper

import (
	"fmt"
	"time"
)

// MovieInfo 影片信息结构
type MovieInfo struct {
	// 基本信息
	Code        string    `json:"code"`         // 影片番号
	Title       string    `json:"title"`        // 影片标题（日文）
	TitleEn     string    `json:"title_en"`     // 影片标题（英文）
	ReleaseDate time.Time `json:"release_date"` // 发行日期
	Duration    int       `json:"duration"`     // 时长（分钟）
	
	// 制作信息
	Studio   string `json:"studio"`   // 制作公司
	Series   string `json:"series"`   // 系列名称
	Director string `json:"director"` // 导演
	
	// 图片信息
	CoverURL  string `json:"cover_url"`  // 封面图片URL
	PosterURL string `json:"poster_url"` // 海报图片URL
	
	// 描述信息
	Plot   string  `json:"plot"`    // 剧情简介（日文）
	PlotEn string  `json:"plot_en"` // 剧情简介（英文）
	Rating float64 `json:"rating"`  // 评分
	
	// 演员信息
	Actors []ActorInfo `json:"actors"` // 演员列表
	
	// 分类信息
	Genres []GenreInfo `json:"genres"` // 分类标签
	
	// 磁力链接
	Magnets []MagnetInfo `json:"magnets"` // 磁力链接列表
	
	// 关联影片
	SimilarMovies []SimilarMovieInfo `json:"similar_movies"` // 相关影片列表
	
	// 元数据
	Source     string    `json:"source"`      // 数据来源
	ScrapedAt  time.Time `json:"scraped_at"`  // 采集时间
	Confidence float64   `json:"confidence"`  // 数据可信度（0-1）
}

// ActorInfo 演员信息结构
type ActorInfo struct {
	Name      string     `json:"name"`       // 演员姓名（日文）
	NameEn    string     `json:"name_en"`    // 演员姓名（英文）
	NameJp    string     `json:"name_jp"`    // 演员姓名（日文假名）
	AvatarURL string     `json:"avatar_url"` // 头像图片URL
	BirthDate *time.Time `json:"birth_date"` // 出生日期
	Height    int        `json:"height"`     // 身高（cm）
	Bust      int        `json:"bust"`       // 胸围（cm）
	Waist     int        `json:"waist"`      // 腰围（cm）
	Hip       int        `json:"hip"`        // 臀围（cm）
	BloodType string     `json:"blood_type"` // 血型
	Hobby     string     `json:"hobby"`      // 兴趣爱好
	DebutDate *time.Time `json:"debut_date"` // 出道日期
}

// GenreInfo 分类信息结构
type GenreInfo struct {
	Name        string `json:"name"`         // 分类名称（中文）
	NameEn      string `json:"name_en"`      // 分类名称（英文）
	NameJp      string `json:"name_jp"`      // 分类名称（日文）
	Description string `json:"description"`  // 分类描述
}

// MagnetInfo 磁力链接信息结构
type MagnetInfo struct {
	MagnetURL        string    `json:"magnet_url"`         // 磁力链接URL
	FileName         string    `json:"file_name"`          // 文件名
	FileSize         int64     `json:"file_size"`          // 文件大小（字节）
	Quality          string    `json:"quality"`            // 视频质量
	HasSubtitle      bool      `json:"has_subtitle"`       // 是否有字幕
	SubtitleLanguage string    `json:"subtitle_language"`  // 字幕语言
	Source           string    `json:"source"`             // 磁力来源
	Uploader         string    `json:"uploader"`           // 上传者
	UploadDate       time.Time `json:"upload_date"`        // 上传日期
	Seeders          int       `json:"seeders"`            // 做种数
	Leechers         int       `json:"leechers"`           // 下载数
}

// SimilarMovieInfo 相关影片信息
type SimilarMovieInfo struct {
	Code     string `json:"code"`      // 影片编码
	Title    string `json:"title"`     // 影片标题
	CoverURL string `json:"cover_url"` // 封面图片URL
}

// ScrapingResult 采集结果结构
type ScrapingResult struct {
	Success   bool        `json:"success"`    // 是否成功
	MovieInfo *MovieInfo  `json:"movie_info"` // 影片信息
	Error     string      `json:"error"`      // 错误信息
	Source    string      `json:"source"`     // 数据来源
	Duration  time.Duration `json:"duration"` // 采集耗时
}

// SearchResult 搜索结果结构
type SearchResult struct {
	Movies    []MovieInfo `json:"movies"`     // 影片列表
	Total     int         `json:"total"`      // 总数
	Page      int         `json:"page"`       // 当前页
	PageSize  int         `json:"page_size"`  // 每页大小
	HasMore   bool        `json:"has_more"`   // 是否有更多
	Source    string      `json:"source"`     // 数据来源
}

// Scraper 爬虫接口
type Scraper interface {
	// GetMovieByCode 根据番号获取影片信息
	GetMovieByCode(code string) (*ScrapingResult, error)
	
	// SearchMovies 搜索影片
	SearchMovies(keyword string, page int, pageSize int) (*SearchResult, error)
	
	// GetLatestMovies 获取最新影片
	GetLatestMovies(page int, pageSize int) (*SearchResult, error)
	
	// GetActorInfo 获取演员信息
	GetActorInfo(actorName string) (*ActorInfo, error)
	
	// GetSourceName 获取数据源名称
	GetSourceName() string
	
	// IsEnabled 检查是否启用
	IsEnabled() bool
	
	// Close 关闭爬虫
	Close() error
}

// MergedMovieInfo 合并后的影片信息
type MergedMovieInfo struct {
	MovieInfo
	Sources []string `json:"sources"` // 数据来源列表
}

// MergeStrategy 数据合并策略
type MergeStrategy struct {
	// 字段优先级（数据源名称 -> 优先级，数字越小优先级越高）
	FieldPriority map[string]map[string]int `json:"field_priority"`
	
	// 是否合并演员信息
	MergeActors bool `json:"merge_actors"`
	
	// 是否合并分类信息
	MergeGenres bool `json:"merge_genres"`
	
	// 是否合并磁力链接
	MergeMagnets bool `json:"merge_magnets"`
	
	// 最小可信度阈值
	MinConfidence float64 `json:"min_confidence"`
}

// DefaultMergeStrategy 默认合并策略
func DefaultMergeStrategy() *MergeStrategy {
	return &MergeStrategy{
		FieldPriority: map[string]map[string]int{
			"title": {
				"javbus":    1,
				"javinizer": 2,
				"javsp":     3,
			},
			"title_en": {
				"javinizer": 1,
				"javsp":     2,
				"javbus":    3,
			},
			"plot_en": {
				"javinizer": 1,
				"javsp":     2,
				"javbus":    3,
			},
			"actors": {
				"javbus":    1,
				"javsp":     2,
				"javinizer": 3,
			},
			"magnets": {
				"javbus":    1,
				"javsp":     2,
				"javinizer": 3,
			},
		},
		MergeActors:   true,
		MergeGenres:   true,
		MergeMagnets:  true,
		MinConfidence: 0.5,
	}
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string `json:"field"`   // 字段名
	Message string `json:"message"` // 错误信息
}

func (e *ValidationError) Error() string {
	return e.Message
}

// ScrapingError 采集错误
type ScrapingError struct {
	Source  string `json:"source"`  // 数据源
	Code    string `json:"code"`    // 影片番号
	Message string `json:"message"` // 错误信息
	Cause   error  `json:"cause"`   // 原因
}

func (e *ScrapingError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("[%s] %s: %s (原因: %v)", e.Source, e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("[%s] %s: %s", e.Source, e.Code, e.Message)
}

func (e *ScrapingError) Unwrap() error {
	return e.Cause
}