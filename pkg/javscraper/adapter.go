package javscraper

import (
	"magnet-downloader/pkg/javscraper/javbus"
	"magnet-downloader/pkg/javscraper/javinizer"
	"magnet-downloader/pkg/javscraper/javsp"
)

// JavBusAdapter JavBus适配器
type JavBusAdapter struct {
	client *javbus.Client
}

func NewJavBusAdapter(config *JavBusConfig, userAgent string, externalServiceConfig *ExternalServiceConfig) *JavBusAdapter {
	javbusConfig := &javbus.Config{
		Enabled:   config.Enabled,
		BaseURL:   config.BaseURL,
		Timeout:   config.Timeout,
		RateLimit: config.RateLimit,
		MaxPages:  config.MaxPages,
	}
	
	// 使用外部服务配置的API URL
	var apiBaseURL string
	if externalServiceConfig != nil && externalServiceConfig.Enabled {
		apiBaseURL = externalServiceConfig.BaseURL
	}
	
	client := javbus.NewClient(javbusConfig, userAgent, apiBaseURL)
	return &JavBusAdapter{client: client}
}

func (a *JavBusAdapter) GetMovieByCode(code string) (*ScrapingResult, error) {
	result, err := a.client.GetMovieByCode(code)
	if err != nil {
		return nil, err
	}
	
	// 转换类型
	return convertJavBusResult(result), nil
}

func (a *JavBusAdapter) SearchMovies(keyword string, page int, pageSize int) (*SearchResult, error) {
	result, err := a.client.SearchMovies(keyword, page, pageSize)
	if err != nil {
		return nil, err
	}
	
	return convertJavBusSearchResult(result), nil
}

func (a *JavBusAdapter) GetLatestMovies(page int, pageSize int) (*SearchResult, error) {
	result, err := a.client.GetLatestMovies(page, pageSize)
	if err != nil {
		return nil, err
	}
	
	return convertJavBusSearchResult(result), nil
}

func (a *JavBusAdapter) GetActorInfo(actorName string) (*ActorInfo, error) {
	result, err := a.client.GetActorInfo(actorName)
	if err != nil {
		return nil, err
	}
	
	return convertJavBusActorInfo(result), nil
}

func (a *JavBusAdapter) GetSourceName() string {
	return a.client.GetSourceName()
}

func (a *JavBusAdapter) IsEnabled() bool {
	return a.client.IsEnabled()
}

func (a *JavBusAdapter) Close() error {
	return a.client.Close()
}

// JavinizerAdapter Javinizer适配器
type JavinizerAdapter struct {
	adapter *javinizer.Adapter
}

func NewJavinizerAdapter(config *JavinizerConfig, userAgent string, externalServiceConfig *ExternalServiceConfig) *JavinizerAdapter {
	javinizerConfig := &javinizer.Config{
		Enabled:   config.Enabled,
		Sources:   config.Sources,
		Timeout:   config.Timeout,
		RateLimit: config.RateLimit,
	}
	
	// 使用外部服务配置的包装器URL
	var wrapperBaseURL string
	if externalServiceConfig != nil && externalServiceConfig.Enabled {
		wrapperBaseURL = externalServiceConfig.BaseURL
	}
	
	adapter := javinizer.NewAdapter(javinizerConfig, userAgent, wrapperBaseURL)
	return &JavinizerAdapter{adapter: adapter}
}

func (a *JavinizerAdapter) GetMovieByCode(code string) (*ScrapingResult, error) {
	result, err := a.adapter.GetMovieByCode(code)
	if err != nil {
		return nil, err
	}
	
	return convertJavinizerResult(result), nil
}

func (a *JavinizerAdapter) SearchMovies(keyword string, page int, pageSize int) (*SearchResult, error) {
	result, err := a.adapter.SearchMovies(keyword, page, pageSize)
	if err != nil {
		return nil, err
	}
	
	return convertJavinizerSearchResult(result), nil
}

func (a *JavinizerAdapter) GetLatestMovies(page int, pageSize int) (*SearchResult, error) {
	result, err := a.adapter.GetLatestMovies(page, pageSize)
	if err != nil {
		return nil, err
	}
	
	return convertJavinizerSearchResult(result), nil
}

func (a *JavinizerAdapter) GetActorInfo(actorName string) (*ActorInfo, error) {
	result, err := a.adapter.GetActorInfo(actorName)
	if err != nil {
		return nil, err
	}
	
	return convertJavinizerActorInfo(result), nil
}

func (a *JavinizerAdapter) GetSourceName() string {
	return a.adapter.GetSourceName()
}

func (a *JavinizerAdapter) IsEnabled() bool {
	return a.adapter.IsEnabled()
}

func (a *JavinizerAdapter) Close() error {
	return a.adapter.Close()
}

// JavSPAdapter JavSP适配器
type JavSPAdapter struct {
	adapter *javsp.Adapter
}

func NewJavSPAdapter(config *JavSPConfig, userAgent string, externalServiceConfig *ExternalServiceConfig) *JavSPAdapter {
	javspConfig := &javsp.Config{
		Enabled:   config.Enabled,
		Sources:   config.Sources,
		Timeout:   config.Timeout,
		RateLimit: config.RateLimit,
	}
	
	// 使用外部服务配置的包装器URL
	var wrapperBaseURL string
	if externalServiceConfig != nil && externalServiceConfig.Enabled {
		wrapperBaseURL = externalServiceConfig.BaseURL
	}
	
	adapter := javsp.NewAdapter(javspConfig, userAgent, wrapperBaseURL)
	return &JavSPAdapter{adapter: adapter}
}

func (a *JavSPAdapter) GetMovieByCode(code string) (*ScrapingResult, error) {
	result, err := a.adapter.GetMovieByCode(code)
	if err != nil {
		return nil, err
	}
	
	return convertJavSPResult(result), nil
}

func (a *JavSPAdapter) SearchMovies(keyword string, page int, pageSize int) (*SearchResult, error) {
	result, err := a.adapter.SearchMovies(keyword, page, pageSize)
	if err != nil {
		return nil, err
	}
	
	return convertJavSPSearchResult(result), nil
}

func (a *JavSPAdapter) GetLatestMovies(page int, pageSize int) (*SearchResult, error) {
	result, err := a.adapter.GetLatestMovies(page, pageSize)
	if err != nil {
		return nil, err
	}
	
	return convertJavSPSearchResult(result), nil
}

func (a *JavSPAdapter) GetActorInfo(actorName string) (*ActorInfo, error) {
	result, err := a.adapter.GetActorInfo(actorName)
	if err != nil {
		return nil, err
	}
	
	return convertJavSPActorInfo(result), nil
}

func (a *JavSPAdapter) GetSourceName() string {
	return a.adapter.GetSourceName()
}

func (a *JavSPAdapter) IsEnabled() bool {
	return a.adapter.IsEnabled()
}

func (a *JavSPAdapter) Close() error {
	return a.adapter.Close()
}