package javscraper

import (
	"fmt"
	"time"
)

// Config JAV爬虫配置
type Config struct {
	// 全局配置
	Enabled     bool          `json:"enabled" yaml:"enabled"`         // 是否启用JAV爬虫
	UserAgent   string        `json:"user_agent" yaml:"user_agent"`   // 用户代理
	Timeout     time.Duration `json:"timeout" yaml:"timeout"`         // 请求超时时间
	MaxRetries  int           `json:"max_retries" yaml:"max_retries"` // 最大重试次数
	RateLimit   time.Duration `json:"rate_limit" yaml:"rate_limit"`   // 请求间隔限制
	
	// 外部服务配置
	ExternalServices ExternalServicesConfig `json:"external_services" yaml:"external_services"`
	
	// 数据源配置
	Sources SourcesConfig `json:"sources" yaml:"sources"`
}

// ExternalServicesConfig 外部服务配置
type ExternalServicesConfig struct {
	JavBusAPI        ExternalServiceConfig `json:"javbus_api" yaml:"javbus_api"`               // JavBus API服务
	JavSPWrapper     ExternalServiceConfig `json:"javsp_wrapper" yaml:"javsp_wrapper"`         // JavSP包装器服务
	JavinizerWrapper ExternalServiceConfig `json:"javinizer_wrapper" yaml:"javinizer_wrapper"` // Javinizer包装器服务
}

// ExternalServiceConfig 单个外部服务配置
type ExternalServiceConfig struct {
	Enabled    bool          `json:"enabled" yaml:"enabled"`         // 是否启用
	BaseURL    string        `json:"base_url" yaml:"base_url"`       // 服务基础URL
	Timeout    time.Duration `json:"timeout" yaml:"timeout"`         // 超时时间
	MaxRetries int           `json:"max_retries" yaml:"max_retries"` // 最大重试次数
	RateLimit  time.Duration `json:"rate_limit" yaml:"rate_limit"`   // 速率限制
}

// SourcesConfig 数据源配置
type SourcesConfig struct {
	JavBus    JavBusConfig    `json:"javbus" yaml:"javbus"`
	Javinizer JavinizerConfig `json:"javinizer" yaml:"javinizer"`
	JavSP     JavSPConfig     `json:"javsp" yaml:"javsp"`
}

// JavBusConfig JavBus配置
type JavBusConfig struct {
	Enabled   bool          `json:"enabled" yaml:"enabled"`     // 是否启用
	BaseURL   string        `json:"base_url" yaml:"base_url"`   // 基础URL
	Timeout   time.Duration `json:"timeout" yaml:"timeout"`     // 超时时间
	RateLimit time.Duration `json:"rate_limit" yaml:"rate_limit"` // 请求间隔
	MaxPages  int           `json:"max_pages" yaml:"max_pages"` // 最大抓取页数
}

// JavinizerConfig Javinizer配置
type JavinizerConfig struct {
	Enabled   bool          `json:"enabled" yaml:"enabled"`     // 是否启用
	Sources   []string      `json:"sources" yaml:"sources"`     // 启用的数据源列表
	Timeout   time.Duration `json:"timeout" yaml:"timeout"`     // 超时时间
	RateLimit time.Duration `json:"rate_limit" yaml:"rate_limit"` // 请求间隔
}

// JavSPConfig JavSP配置
type JavSPConfig struct {
	Enabled   bool          `json:"enabled" yaml:"enabled"`     // 是否启用
	Sources   []string      `json:"sources" yaml:"sources"`     // 启用的数据源列表
	Timeout   time.Duration `json:"timeout" yaml:"timeout"`     // 超时时间
	RateLimit time.Duration `json:"rate_limit" yaml:"rate_limit"` // 请求间隔
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		Enabled:    true,
		UserAgent:  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
		Timeout:    30 * time.Second,
		MaxRetries: 3,
		RateLimit:  2 * time.Second, // 2秒间隔，避免被封
		Sources: SourcesConfig{
			JavBus: JavBusConfig{
				Enabled:   true,
				BaseURL:   "https://www.javbus.com",
				Timeout:   30 * time.Second,
				RateLimit: 3 * time.Second, // JavBus较严格，3秒间隔
				MaxPages:  10,
			},
			Javinizer: JavinizerConfig{
				Enabled: true,
				Sources: []string{"javlibrary", "r18", "dmm"},
				Timeout: 30 * time.Second,
				RateLimit: 2 * time.Second,
			},
			JavSP: JavSPConfig{
				Enabled: true,
				Sources: []string{"javdb", "javlibrary", "avsox"},
				Timeout: 30 * time.Second,
				RateLimit: 2 * time.Second,
			},
		},
	}
}

// Validate 验证配置
func (c *Config) Validate() error {
	if !c.Enabled {
		return nil // 如果未启用，跳过验证
	}
	
	if c.UserAgent == "" {
		return fmt.Errorf("用户代理不能为空")
	}
	
	if c.Timeout <= 0 {
		c.Timeout = 30 * time.Second
	}
	
	if c.MaxRetries <= 0 {
		c.MaxRetries = 3
	}
	
	if c.RateLimit <= 0 {
		c.RateLimit = 2 * time.Second
	}
	
	// 验证外部服务配置
	if err := c.validateExternalServices(); err != nil {
		return fmt.Errorf("外部服务配置验证失败: %v", err)
	}
	
	// 验证JavBus配置
	if c.Sources.JavBus.Enabled {
		if c.Sources.JavBus.BaseURL == "" {
			c.Sources.JavBus.BaseURL = "https://www.javbus.com"
		}
		if c.Sources.JavBus.Timeout <= 0 {
			c.Sources.JavBus.Timeout = c.Timeout
		}
		if c.Sources.JavBus.RateLimit <= 0 {
			c.Sources.JavBus.RateLimit = c.RateLimit
		}
		if c.Sources.JavBus.MaxPages <= 0 {
			c.Sources.JavBus.MaxPages = 10
		}
	}
	
	// 验证Javinizer配置
	if c.Sources.Javinizer.Enabled {
		if len(c.Sources.Javinizer.Sources) == 0 {
			c.Sources.Javinizer.Sources = []string{"javlibrary", "r18", "dmm"}
		}
		if c.Sources.Javinizer.Timeout <= 0 {
			c.Sources.Javinizer.Timeout = c.Timeout
		}
		if c.Sources.Javinizer.RateLimit <= 0 {
			c.Sources.Javinizer.RateLimit = c.RateLimit
		}
	}
	
	// 验证JavSP配置
	if c.Sources.JavSP.Enabled {
		if len(c.Sources.JavSP.Sources) == 0 {
			c.Sources.JavSP.Sources = []string{"javdb", "javlibrary", "avsox"}
		}
		if c.Sources.JavSP.Timeout <= 0 {
			c.Sources.JavSP.Timeout = c.Timeout
		}
		if c.Sources.JavSP.RateLimit <= 0 {
			c.Sources.JavSP.RateLimit = c.RateLimit
		}
	}
	
	return nil
}

// validateExternalServices 验证外部服务配置
func (c *Config) validateExternalServices() error {
	// 验证JavBus API服务
	if c.ExternalServices.JavBusAPI.Enabled {
		if c.ExternalServices.JavBusAPI.BaseURL == "" {
			c.ExternalServices.JavBusAPI.BaseURL = "http://localhost:3001"
		}
		if c.ExternalServices.JavBusAPI.Timeout <= 0 {
			c.ExternalServices.JavBusAPI.Timeout = 30 * time.Second
		}
		if c.ExternalServices.JavBusAPI.MaxRetries <= 0 {
			c.ExternalServices.JavBusAPI.MaxRetries = 3
		}
		if c.ExternalServices.JavBusAPI.RateLimit <= 0 {
			c.ExternalServices.JavBusAPI.RateLimit = 2 * time.Second
		}
	}
	
	// 验证JavSP包装器服务
	if c.ExternalServices.JavSPWrapper.Enabled {
		if c.ExternalServices.JavSPWrapper.BaseURL == "" {
			c.ExternalServices.JavSPWrapper.BaseURL = "http://localhost:3002"
		}
		if c.ExternalServices.JavSPWrapper.Timeout <= 0 {
			c.ExternalServices.JavSPWrapper.Timeout = 30 * time.Second
		}
		if c.ExternalServices.JavSPWrapper.MaxRetries <= 0 {
			c.ExternalServices.JavSPWrapper.MaxRetries = 3
		}
		if c.ExternalServices.JavSPWrapper.RateLimit <= 0 {
			c.ExternalServices.JavSPWrapper.RateLimit = 3 * time.Second
		}
	}
	
	// 验证Javinizer包装器服务
	if c.ExternalServices.JavinizerWrapper.Enabled {
		if c.ExternalServices.JavinizerWrapper.BaseURL == "" {
			c.ExternalServices.JavinizerWrapper.BaseURL = "http://localhost:3003"
		}
		if c.ExternalServices.JavinizerWrapper.Timeout <= 0 {
			c.ExternalServices.JavinizerWrapper.Timeout = 30 * time.Second
		}
		if c.ExternalServices.JavinizerWrapper.MaxRetries <= 0 {
			c.ExternalServices.JavinizerWrapper.MaxRetries = 3
		}
		if c.ExternalServices.JavinizerWrapper.RateLimit <= 0 {
			c.ExternalServices.JavinizerWrapper.RateLimit = 3 * time.Second
		}
	}
	
	return nil
}

// IsSourceEnabled 检查指定数据源是否启用
func (c *Config) IsSourceEnabled(source string) bool {
	if !c.Enabled {
		return false
	}
	
	switch source {
	case "javbus":
		return c.Sources.JavBus.Enabled
	case "javinizer":
		return c.Sources.Javinizer.Enabled
	case "javsp":
		return c.Sources.JavSP.Enabled
	default:
		return false
	}
}

// GetSourceTimeout 获取指定数据源的超时时间
func (c *Config) GetSourceTimeout(source string) time.Duration {
	switch source {
	case "javbus":
		return c.Sources.JavBus.Timeout
	case "javinizer":
		return c.Sources.Javinizer.Timeout
	case "javsp":
		return c.Sources.JavSP.Timeout
	default:
		return c.Timeout
	}
}

// GetSourceRateLimit 获取指定数据源的请求间隔
func (c *Config) GetSourceRateLimit(source string) time.Duration {
	switch source {
	case "javbus":
		return c.Sources.JavBus.RateLimit
	case "javinizer":
		return c.Sources.Javinizer.RateLimit
	case "javsp":
		return c.Sources.JavSP.RateLimit
	default:
		return c.RateLimit
	}
}