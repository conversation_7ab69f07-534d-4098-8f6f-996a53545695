package javinizer

import "time"

// Config Javinizer配置
type Config struct {
	Enabled   bool          `json:"enabled" yaml:"enabled"`     // 是否启用
	Sources   []string      `json:"sources" yaml:"sources"`     // 启用的数据源列表
	Timeout   time.Duration `json:"timeout" yaml:"timeout"`     // 超时时间
	RateLimit time.Duration `json:"rate_limit" yaml:"rate_limit"` // 请求间隔
}

// MovieInfo 影片信息结构
type MovieInfo struct {
	// 基本信息
	Code        string    `json:"code"`         // 影片番号
	Title       string    `json:"title"`        // 影片标题（日文）
	TitleEn     string    `json:"title_en"`     // 影片标题（英文）
	ReleaseDate time.Time `json:"release_date"` // 发行日期
	Duration    int       `json:"duration"`     // 时长（分钟）
	
	// 制作信息
	Studio   string `json:"studio"`   // 制作公司
	Series   string `json:"series"`   // 系列名称
	Director string `json:"director"` // 导演
	
	// 图片信息
	CoverURL  string `json:"cover_url"`  // 封面图片URL
	PosterURL string `json:"poster_url"` // 海报图片URL
	
	// 描述信息
	Plot   string  `json:"plot"`    // 剧情简介（日文）
	PlotEn string  `json:"plot_en"` // 剧情简介（英文）
	Rating float64 `json:"rating"`  // 评分
	
	// 演员信息
	Actors []ActorInfo `json:"actors"` // 演员列表
	
	// 分类信息
	Genres []GenreInfo `json:"genres"` // 分类标签
	
	// 磁力链接
	Magnets []MagnetInfo `json:"magnets"` // 磁力链接列表
	
	// 元数据
	Source     string    `json:"source"`      // 数据来源
	ScrapedAt  time.Time `json:"scraped_at"`  // 采集时间
	Confidence float64   `json:"confidence"`  // 数据可信度（0-1）
}

// ActorInfo 演员信息结构
type ActorInfo struct {
	Name      string     `json:"name"`       // 演员姓名（日文）
	NameEn    string     `json:"name_en"`    // 演员姓名（英文）
	NameJp    string     `json:"name_jp"`    // 演员姓名（日文假名）
	AvatarURL string     `json:"avatar_url"` // 头像图片URL
	BirthDate *time.Time `json:"birth_date"` // 出生日期
	Height    int        `json:"height"`     // 身高（cm）
	Bust      int        `json:"bust"`       // 胸围（cm）
	Waist     int        `json:"waist"`      // 腰围（cm）
	Hip       int        `json:"hip"`        // 臀围（cm）
	BloodType string     `json:"blood_type"` // 血型
	Hobby     string     `json:"hobby"`      // 兴趣爱好
	DebutDate *time.Time `json:"debut_date"` // 出道日期
}

// GenreInfo 分类信息结构
type GenreInfo struct {
	Name        string `json:"name"`         // 分类名称（中文）
	NameEn      string `json:"name_en"`      // 分类名称（英文）
	NameJp      string `json:"name_jp"`      // 分类名称（日文）
	Description string `json:"description"`  // 分类描述
}

// MagnetInfo 磁力链接信息结构
type MagnetInfo struct {
	MagnetURL        string    `json:"magnet_url"`         // 磁力链接URL
	FileName         string    `json:"file_name"`          // 文件名
	FileSize         int64     `json:"file_size"`          // 文件大小（字节）
	Quality          string    `json:"quality"`            // 视频质量
	HasSubtitle      bool      `json:"has_subtitle"`       // 是否有字幕
	SubtitleLanguage string    `json:"subtitle_language"`  // 字幕语言
	Source           string    `json:"source"`             // 磁力来源
	Uploader         string    `json:"uploader"`           // 上传者
	UploadDate       time.Time `json:"upload_date"`        // 上传日期
	Seeders          int       `json:"seeders"`            // 做种数
	Leechers         int       `json:"leechers"`           // 下载数
}

// ScrapingResult 采集结果结构
type ScrapingResult struct {
	Success   bool          `json:"success"`    // 是否成功
	MovieInfo *MovieInfo    `json:"movie_info"` // 影片信息
	Error     string        `json:"error"`      // 错误信息
	Source    string        `json:"source"`     // 数据来源
	Duration  time.Duration `json:"duration"`   // 采集耗时
}

// SearchResult 搜索结果结构
type SearchResult struct {
	Movies    []MovieInfo `json:"movies"`     // 影片列表
	Total     int         `json:"total"`      // 总数
	Page      int         `json:"page"`       // 当前页
	PageSize  int         `json:"page_size"`  // 每页大小
	HasMore   bool        `json:"has_more"`   // 是否有更多
	Source    string      `json:"source"`     // 数据来源
}