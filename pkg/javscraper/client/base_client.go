package client

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"magnet-downloader/pkg/logger"
)

// BaseClient 统一的HTTP客户端基类
type BaseClient struct {
	httpClient *http.Client
	userAgent  string
	timeout    time.Duration
	maxRetries int
	rateLimit  time.Duration
	
	// 速率限制
	lastRequest time.Time
	mutex       sync.Mutex
}

// NewBaseClient 创建新的基础HTTP客户端
func NewBaseClient(userAgent string, timeout time.Duration, maxRetries int, rateLimit time.Duration) *BaseClient {
	// 创建优化的HTTP传输配置
	transport := &http.Transport{
		MaxIdleConns:          20,
		MaxIdleConnsPerHost:   10,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		WriteBufferSize:       64 * 1024, // 64KB
		ReadBufferSize:        64 * 1024, // 64KB
	}

	httpClient := &http.Client{
		Transport: transport,
		Timeout:   timeout,
	}

	return &BaseClient{
		httpClient: httpClient,
		userAgent:  userAgent,
		timeout:    timeout,
		maxRetries: maxRetries,
		rateLimit:  rateLimit,
		lastRequest: time.Now().Add(-rateLimit), // 初始化为可以立即请求
	}
}

// Get 发送GET请求
func (c *BaseClient) Get(ctx context.Context, url string, headers map[string]string) ([]byte, error) {
	return c.doRequest(ctx, "GET", url, nil, headers)
}

// Post 发送POST请求
func (c *BaseClient) Post(ctx context.Context, url string, body io.Reader, headers map[string]string) ([]byte, error) {
	return c.doRequest(ctx, "POST", url, body, headers)
}

// doRequest 执行HTTP请求（带重试机制）
func (c *BaseClient) doRequest(ctx context.Context, method, url string, body io.Reader, headers map[string]string) ([]byte, error) {
	var lastErr error
	
	for attempt := 0; attempt <= c.maxRetries; attempt++ {
		if attempt > 0 {
			// 重试前等待
			waitTime := time.Duration(attempt) * time.Second
			logger.Warnf("第%d次重试请求 %s，等待%v", attempt, url, waitTime)
			
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(waitTime):
			}
		}
		
		// 速率限制
		if err := c.waitForRateLimit(); err != nil {
			return nil, err
		}
		
		// 执行请求
		resp, err := c.executeRequest(ctx, method, url, body, headers)
		if err != nil {
			lastErr = err
			logger.Warnf("请求失败 %s: %v", url, err)
			continue
		}
		
		return resp, nil
	}
	
	return nil, fmt.Errorf("请求失败，已重试%d次: %v", c.maxRetries, lastErr)
}

// executeRequest 执行单次HTTP请求
func (c *BaseClient) executeRequest(ctx context.Context, method, url string, body io.Reader, headers map[string]string) ([]byte, error) {
	// 创建请求
	req, err := http.NewRequestWithContext(ctx, method, url, body)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}
	
	// 设置默认头部
	req.Header.Set("User-Agent", c.userAgent)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br")
	req.Header.Set("DNT", "1")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
	
	// 设置自定义头部
	for key, value := range headers {
		req.Header.Set(key, value)
	}
	
	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	// 检查状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return nil, fmt.Errorf("HTTP状态码错误: %d", resp.StatusCode)
	}
	
	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %v", err)
	}
	
	logger.Debugf("成功请求 %s，响应大小: %d bytes", url, len(respBody))
	return respBody, nil
}

// waitForRateLimit 等待速率限制
func (c *BaseClient) waitForRateLimit() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	elapsed := time.Since(c.lastRequest)
	if elapsed < c.rateLimit {
		waitTime := c.rateLimit - elapsed
		logger.Debugf("速率限制，等待 %v", waitTime)
		time.Sleep(waitTime)
	}
	
	c.lastRequest = time.Now()
	return nil
}

// BuildURL 构建URL
func (c *BaseClient) BuildURL(baseURL string, path string, params map[string]string) (string, error) {
	u, err := url.Parse(baseURL)
	if err != nil {
		return "", fmt.Errorf("解析基础URL失败: %v", err)
	}
	
	// 添加路径
	if path != "" {
		u.Path = strings.TrimSuffix(u.Path, "/") + "/" + strings.TrimPrefix(path, "/")
	}
	
	// 添加查询参数
	if len(params) > 0 {
		query := u.Query()
		for key, value := range params {
			query.Set(key, value)
		}
		u.RawQuery = query.Encode()
	}
	
	return u.String(), nil
}

// SetUserAgent 设置用户代理
func (c *BaseClient) SetUserAgent(userAgent string) {
	c.userAgent = userAgent
}

// SetTimeout 设置超时时间
func (c *BaseClient) SetTimeout(timeout time.Duration) {
	c.timeout = timeout
	c.httpClient.Timeout = timeout
}

// SetRateLimit 设置速率限制
func (c *BaseClient) SetRateLimit(rateLimit time.Duration) {
	c.rateLimit = rateLimit
}

// GetUserAgent 获取用户代理
func (c *BaseClient) GetUserAgent() string {
	return c.userAgent
}

// GetTimeout 获取超时时间
func (c *BaseClient) GetTimeout() time.Duration {
	return c.timeout
}

// GetRateLimit 获取速率限制
func (c *BaseClient) GetRateLimit() time.Duration {
	return c.rateLimit
}

// Close 关闭客户端
func (c *BaseClient) Close() error {
	// 关闭空闲连接
	if transport, ok := c.httpClient.Transport.(*http.Transport); ok {
		transport.CloseIdleConnections()
	}
	return nil
}