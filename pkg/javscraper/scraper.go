package javscraper

import (
	"fmt"
	"sync"

	"magnet-downloader/pkg/logger"
)

// Manager JAV爬虫管理器
type Manager struct {
	config   *Config
	scrapers map[string]Scraper
	mutex    sync.RWMutex
}

// NewManager 创建新的爬虫管理器
func NewManager(config *Config) (*Manager, error) {
	if config == nil {
		config = DefaultConfig()
	}

	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	manager := &Manager{
		config:   config,
		scrapers: make(map[string]Scraper),
	}

	// 初始化爬虫
	if err := manager.initScrapers(); err != nil {
		return nil, fmt.Errorf("初始化爬虫失败: %v", err)
	}

	return manager, nil
}

// initScrapers 初始化爬虫
func (m *Manager) initScrapers() error {
	// 初始化JavBus爬虫
	if m.config.Sources.JavBus.Enabled {
		javbusAdapter := NewJavBusAdapter(&m.config.Sources.JavBus, m.config.UserAgent, &m.config.ExternalServices.JavBusAPI)
		if javbusAdapter != nil {
			m.scrapers["javbus"] = javbusAdapter
			logger.Infof("JavBus爬虫初始化成功")
		} else {
			logger.Warnf("JavBus爬虫初始化失败")
		}
	}

	// 初始化Javinizer适配器
	if m.config.Sources.Javinizer.Enabled {
		javinizerAdapter := NewJavinizerAdapter(&m.config.Sources.Javinizer, m.config.UserAgent, &m.config.ExternalServices.JavinizerWrapper)
		if javinizerAdapter != nil {
			m.scrapers["javinizer"] = javinizerAdapter
			logger.Infof("Javinizer适配器初始化成功")
		} else {
			logger.Warnf("Javinizer适配器初始化失败")
		}
	}

	// 初始化JavSP适配器
	if m.config.Sources.JavSP.Enabled {
		javspAdapter := NewJavSPAdapter(&m.config.Sources.JavSP, m.config.UserAgent, &m.config.ExternalServices.JavSPWrapper)
		if javspAdapter != nil {
			m.scrapers["javsp"] = javspAdapter
			logger.Infof("JavSP适配器初始化成功")
		} else {
			logger.Warnf("JavSP适配器初始化失败")
		}
	}

	if len(m.scrapers) == 0 {
		return fmt.Errorf("没有可用的爬虫")
	}

	logger.Infof("爬虫管理器初始化完成，共%d个爬虫", len(m.scrapers))
	return nil
}

// GetMovieByCode 根据番号获取影片信息
func (m *Manager) GetMovieByCode(code string) (*ScrapingResult, error) {
	if !m.config.Enabled {
		return &ScrapingResult{
			Success: false,
			Error:   "JAV爬虫未启用",
		}, nil
	}

	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 优先使用JavBus作为主要数据源
	if scraper, exists := m.scrapers["javbus"]; exists && scraper.IsEnabled() {
		result, err := scraper.GetMovieByCode(code)
		if err == nil && result.Success {
			logger.Infof("从JavBus成功获取影片信息: %s", code)
			return result, nil
		}
		logger.Warnf("JavBus获取影片信息失败: %v", err)
	}

	// 如果JavBus失败，尝试其他数据源
	for sourceName, scraper := range m.scrapers {
		if sourceName == "javbus" || !scraper.IsEnabled() {
			continue
		}

		result, err := scraper.GetMovieByCode(code)
		if err == nil && result.Success {
			logger.Infof("从%s成功获取影片信息: %s", sourceName, code)
			return result, nil
		}
		logger.Warnf("%s获取影片信息失败: %v", sourceName, err)
	}

	return &ScrapingResult{
		Success: false,
		Error:   "所有数据源都无法获取影片信息",
	}, nil
}

// GetMovieByCodeFromAllSources 从所有数据源获取影片信息
func (m *Manager) GetMovieByCodeFromAllSources(code string) (map[string]*ScrapingResult, error) {
	if !m.config.Enabled {
		return nil, fmt.Errorf("JAV爬虫未启用")
	}

	m.mutex.RLock()
	defer m.mutex.RUnlock()

	results := make(map[string]*ScrapingResult)
	var wg sync.WaitGroup
	var mutex sync.Mutex

	// 并发从所有数据源获取信息
	for sourceName, scraper := range m.scrapers {
		if !scraper.IsEnabled() {
			continue
		}

		wg.Add(1)
		go func(name string, s Scraper) {
			defer wg.Done()

			result, err := s.GetMovieByCode(code)
			
			mutex.Lock()
			if err != nil {
				results[name] = &ScrapingResult{
					Success: false,
					Error:   err.Error(),
					Source:  name,
				}
			} else {
				results[name] = result
			}
			mutex.Unlock()
		}(sourceName, scraper)
	}

	wg.Wait()
	return results, nil
}

// SearchMovies 搜索影片
func (m *Manager) SearchMovies(keyword string, page int, pageSize int) (*SearchResult, error) {
	if !m.config.Enabled {
		return &SearchResult{
			Movies: []MovieInfo{},
			Total:  0,
		}, nil
	}

	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 优先使用JavBus进行搜索
	if scraper, exists := m.scrapers["javbus"]; exists && scraper.IsEnabled() {
		result, err := scraper.SearchMovies(keyword, page, pageSize)
		if err == nil && len(result.Movies) > 0 {
			return result, nil
		}
	}

	// 如果JavBus搜索失败，尝试其他数据源
	for sourceName, scraper := range m.scrapers {
		if sourceName == "javbus" || !scraper.IsEnabled() {
			continue
		}

		result, err := scraper.SearchMovies(keyword, page, pageSize)
		if err == nil && len(result.Movies) > 0 {
			return result, nil
		}
	}

	return &SearchResult{
		Movies:   []MovieInfo{},
		Total:    0,
		Page:     page,
		PageSize: pageSize,
		HasMore:  false,
	}, nil
}

// GetLatestMovies 获取最新影片
func (m *Manager) GetLatestMovies(page int, pageSize int) (*SearchResult, error) {
	if !m.config.Enabled {
		return &SearchResult{
			Movies: []MovieInfo{},
			Total:  0,
		}, nil
	}

	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 优先使用JavBus获取最新影片
	if scraper, exists := m.scrapers["javbus"]; exists && scraper.IsEnabled() {
		result, err := scraper.GetLatestMovies(page, pageSize)
		if err == nil && len(result.Movies) > 0 {
			return result, nil
		}
	}

	// 如果JavBus失败，尝试其他数据源
	for sourceName, scraper := range m.scrapers {
		if sourceName == "javbus" || !scraper.IsEnabled() {
			continue
		}

		result, err := scraper.GetLatestMovies(page, pageSize)
		if err == nil && len(result.Movies) > 0 {
			return result, nil
		}
	}

	return &SearchResult{
		Movies:   []MovieInfo{},
		Total:    0,
		Page:     page,
		PageSize: pageSize,
		HasMore:  false,
	}, nil
}

// GetActorInfo 获取演员信息
func (m *Manager) GetActorInfo(actorName string) (*ActorInfo, error) {
	if !m.config.Enabled {
		return nil, fmt.Errorf("JAV爬虫未启用")
	}

	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 尝试从各个数据源获取演员信息
	for sourceName, scraper := range m.scrapers {
		if !scraper.IsEnabled() {
			continue
		}

		actorInfo, err := scraper.GetActorInfo(actorName)
		if err == nil && actorInfo != nil {
			logger.Infof("从%s成功获取演员信息: %s", sourceName, actorName)
			return actorInfo, nil
		}
	}

	return nil, fmt.Errorf("无法获取演员信息: %s", actorName)
}

// GetEnabledSources 获取启用的数据源列表
func (m *Manager) GetEnabledSources() []string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var sources []string
	for sourceName, scraper := range m.scrapers {
		if scraper.IsEnabled() {
			sources = append(sources, sourceName)
		}
	}
	return sources
}

// IsSourceEnabled 检查指定数据源是否启用
func (m *Manager) IsSourceEnabled(sourceName string) bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	scraper, exists := m.scrapers[sourceName]
	return exists && scraper.IsEnabled()
}

// GetConfig 获取配置
func (m *Manager) GetConfig() *Config {
	return m.config
}

// UpdateConfig 更新配置
func (m *Manager) UpdateConfig(config *Config) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}

	if err := config.Validate(); err != nil {
		return fmt.Errorf("配置验证失败: %v", err)
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 关闭现有爬虫
	for _, scraper := range m.scrapers {
		if err := scraper.Close(); err != nil {
			logger.Warnf("关闭爬虫失败: %v", err)
		}
	}

	// 更新配置
	m.config = config
	m.scrapers = make(map[string]Scraper)

	// 重新初始化爬虫
	if err := m.initScrapers(); err != nil {
		return fmt.Errorf("重新初始化爬虫失败: %v", err)
	}

	logger.Infof("爬虫管理器配置更新成功")
	return nil
}

// Close 关闭爬虫管理器
func (m *Manager) Close() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	var lastErr error
	for sourceName, scraper := range m.scrapers {
		if err := scraper.Close(); err != nil {
			logger.Warnf("关闭%s爬虫失败: %v", sourceName, err)
			lastErr = err
		}
	}

	m.scrapers = make(map[string]Scraper)
	logger.Infof("爬虫管理器已关闭")
	
	return lastErr
}

// GetStats 获取统计信息
func (m *Manager) GetStats() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	stats := map[string]interface{}{
		"enabled":        m.config.Enabled,
		"total_scrapers": len(m.scrapers),
		"enabled_scrapers": len(m.GetEnabledSources()),
		"sources":        m.GetEnabledSources(),
		"config": map[string]interface{}{
			"user_agent":  m.config.UserAgent,
			"timeout":     m.config.Timeout.String(),
			"max_retries": m.config.MaxRetries,
			"rate_limit":  m.config.RateLimit.String(),
		},
	}

	return stats
}