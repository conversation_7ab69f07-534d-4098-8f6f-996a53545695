package fileprocessor

import (
	"magnet-downloader/pkg/logger"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// DownloadChecker 检查下载是否完成
type DownloadChecker struct {
	stableTime time.Duration // 文件稳定时间阈值
}

// NewDownloadChecker 创建下载检查器
func NewDownloadChecker() *DownloadChecker {
	return &DownloadChecker{
		stableTime: 30 * time.Second, // 减少到30秒，更快响应下载完成
	}
}

// IsDownloadComplete 检查目录中的下载是否完成
func (dc *DownloadChecker) IsDownloadComplete(taskDir string) bool {
	// 1. 首先检查是否有aria2临时文件（最可靠的指标）
	if dc.hasAria2TempFiles(taskDir) {
		logger.Debugf("目录 %s 包含aria2临时文件，下载未完成", taskDir)
		return false
	}

	// 2. 检查是否有有效的视频文件
	if !dc.hasValidVideoFiles(taskDir) {
		logger.Debugf("目录 %s 没有有效的视频文件，下载可能未完成", taskDir)
		return false
	}

	// 3. 检查视频文件是否稳定（减少等待时间）
	if !dc.areVideoFilesStable(taskDir) {
		logger.Debugf("目录 %s 中的视频文件仍在被修改，下载未完成", taskDir)
		return false
	}

	logger.Debugf("目录 %s 下载已完成", taskDir)
	return true
}

// hasAria2TempFiles 检查是否有aria2临时文件
func (dc *DownloadChecker) hasAria2TempFiles(taskDir string) bool {
	// 1. 检查任务目录内部的.aria2文件
	err := filepath.Walk(taskDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil // 忽略错误，继续检查
		}

		// 检查aria2临时文件
		if filepath.Ext(path) == ".aria2" {
			return filepath.SkipDir // 找到临时文件，停止检查
		}

		return nil
	})

	// 如果Walk被SkipDir中断，说明找到了临时文件
	if err == filepath.SkipDir {
		return true
	}

	// 2. 检查父目录下对应的.aria2文件
	taskDirName := filepath.Base(taskDir)
	parentDir := filepath.Dir(taskDir)
	aria2File := filepath.Join(parentDir, taskDirName+".aria2")

	if _, err := os.Stat(aria2File); err == nil {
		logger.Debugf("发现aria2临时文件: %s", aria2File)
		return true
	}

	return false
}

// hasValidVideoFiles 检查是否有有效的视频文件
func (dc *DownloadChecker) hasValidVideoFiles(taskDir string) bool {
	videoExtensions := map[string]bool{
		".mp4": true, ".avi": true, ".mkv": true, ".mov": true,
		".wmv": true, ".flv": true, ".webm": true, ".m4v": true,
		".MP4": true, ".AVI": true, ".MKV": true, ".MOV": true,
	}

	hasVideo := false
	err := filepath.Walk(taskDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}

		// 检查是否为视频文件
		ext := filepath.Ext(path)
		if videoExtensions[ext] {
			// 检查文件大小是否合理（大于10MB）
			if info.Size() > 10*1024*1024 {
				hasVideo = true
				return filepath.SkipDir // 找到有效视频文件，停止检查
			}
		}

		return nil
	})

	if err != nil {
		logger.Debugf("检查视频文件时出错: %v", err)
	}

	return hasVideo
}

// IsDownloadCompleteAdvanced 高级下载完成检测
func (dc *DownloadChecker) IsDownloadCompleteAdvanced(taskDir string) bool {
	// 1. 检查aria2临时文件（最重要的指标）
	if dc.hasAria2TempFiles(taskDir) {
		logger.Debugf("目录 %s 包含aria2临时文件，下载未完成", taskDir)
		return false
	}

	// 2. 检查是否有部分下载文件（.part, .tmp等）
	if dc.hasPartialFiles(taskDir) {
		logger.Debugf("目录 %s 包含部分下载文件，下载未完成", taskDir)
		return false
	}

	// 3. 检查是否有有效的视频文件
	if !dc.hasValidVideoFiles(taskDir) {
		logger.Debugf("目录 %s 没有有效的视频文件，下载可能未完成", taskDir)
		return false
	}

	// 4. 快速稳定性检查（只需要10秒）
	if !dc.areVideoFilesStableQuick(taskDir) {
		logger.Debugf("目录 %s 中的视频文件刚刚被修改，等待稳定", taskDir)
		return false
	}

	logger.Infof("目录 %s 下载已完成（高级检测）", taskDir)
	return true
}

// hasPartialFiles 检查是否有部分下载文件
func (dc *DownloadChecker) hasPartialFiles(taskDir string) bool {
	partialExtensions := []string{".part", ".tmp", ".download", ".crdownload", ".partial"}

	err := filepath.Walk(taskDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}

		fileName := strings.ToLower(info.Name())
		for _, ext := range partialExtensions {
			if strings.HasSuffix(fileName, ext) {
				return filepath.SkipDir // 找到部分文件
			}
		}

		return nil
	})

	return err == filepath.SkipDir
}

// areVideoFilesStableQuick 快速检查视频文件是否稳定（10秒）
func (dc *DownloadChecker) areVideoFilesStableQuick(taskDir string) bool {
	now := time.Now()
	quickStableTime := 10 * time.Second // 只需要10秒稳定时间

	videoExtensions := map[string]bool{
		".mp4": true, ".avi": true, ".mkv": true, ".mov": true,
		".wmv": true, ".flv": true, ".webm": true, ".m4v": true,
		".MP4": true, ".AVI": true, ".MKV": true, ".MOV": true,
	}

	stable := true
	err := filepath.Walk(taskDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}

		ext := filepath.Ext(path)
		if !videoExtensions[ext] {
			return nil
		}

		// 检查文件修改时间（只需要10秒稳定）
		if now.Sub(info.ModTime()) < quickStableTime {
			logger.Debugf("视频文件 %s 最近被修改 (%v)，等待稳定",
				path, now.Sub(info.ModTime()))
			stable = false
			return filepath.SkipDir
		}

		return nil
	})

	if err != nil {
		logger.Debugf("检查文件稳定性时出错: %v", err)
	}

	return stable
}

// areVideoFilesStable 检查视频文件是否稳定
func (dc *DownloadChecker) areVideoFilesStable(taskDir string) bool {
	now := time.Now()
	videoExtensions := map[string]bool{
		".mp4": true, ".avi": true, ".mkv": true, ".mov": true,
		".wmv": true, ".flv": true, ".webm": true, ".m4v": true,
	}

	stable := true
	err := filepath.Walk(taskDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil // 忽略错误，继续检查
		}

		// 只检查视频文件
		ext := filepath.Ext(path)
		if !videoExtensions[ext] {
			return nil
		}

		// 检查文件修改时间
		if now.Sub(info.ModTime()) < dc.stableTime {
			logger.Debugf("视频文件 %s 最近被修改 (%v)，可能仍在下载",
				path, now.Sub(info.ModTime()))
			stable = false
			return filepath.SkipDir // 找到不稳定的文件，停止检查
		}

		// 检查文件大小是否合理（大于1MB）
		if info.Size() < 1024*1024 {
			logger.Debugf("视频文件 %s 大小过小 (%d bytes)，可能下载未完成", path, info.Size())
			stable = false
			return filepath.SkipDir
		}

		return nil
	})

	return stable && err != filepath.SkipDir
}

// IsFileBeingDownloaded 检查单个文件是否正在被下载
func (dc *DownloadChecker) IsFileBeingDownloaded(filePath string) bool {
	// 检查是否有对应的aria2临时文件
	aria2File := filePath + ".aria2"
	if _, err := os.Stat(aria2File); err == nil {
		return true // 存在.aria2文件，正在下载
	}

	// 检查文件是否最近被修改
	info, err := os.Stat(filePath)
	if err != nil {
		return false // 文件不存在或无法访问
	}

	return time.Since(info.ModTime()) < dc.stableTime
}
