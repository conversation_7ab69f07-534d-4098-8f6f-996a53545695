package fileprocessor

import (
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"time"

	"magnet-downloader/pkg/logger"
)

// VideoFile 视频文件信息
type VideoFile struct {
	Path         string `json:"path"`
	Name         string `json:"name"`
	Size         int64  `json:"size"`
	IsSegment    bool   `json:"is_segment"`
	SegmentIndex int    `json:"segment_index"`
	TaskDir      string `json:"task_dir"` // 所属任务目录
}

// VideoSelector 视频文件选择器
type VideoSelector struct {
	// 支持的视频格式
	videoExtensions []string
	// 分段文件模式
	segmentPatterns []*regexp.Regexp
	// 最小文件大小（字节）
	minFileSize int64
	// 下载完成检查器
	downloadChecker *DownloadChecker
}

// NewVideoSelector 创建视频选择器
func NewVideoSelector() *VideoSelector {
	return &VideoSelector{
		videoExtensions: []string{
			".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv",
			".webm", ".m4v", ".3gp", ".ts", ".m2ts", ".vob",
		},
		segmentPatterns: []*regexp.Regexp{
			// 匹配 filename.part1.mp4, filename.part01.mp4 等
			regexp.MustCompile(`\.part\d+\.`),
			// 匹配 filename.001.mp4, filename.01.mp4 等
			regexp.MustCompile(`\.\d{2,3}\.`),
			// 匹配 filename_part1.mp4, filename_1.mp4 等
			regexp.MustCompile(`[_-](part)?\d+\.`),
			// 匹配 filename.cd1.mp4, filename.disc1.mp4 等
			regexp.MustCompile(`\.(cd|disc)\d+\.`),
		},
		minFileSize:     100 * 1024 * 1024, // 100MB最小文件大小
		downloadChecker: NewDownloadChecker(),
	}
}

// SelectVideosFromDirectory 从目录中选择需要上传的视频文件
func (vs *VideoSelector) SelectVideosFromDirectory(downloadDir string) ([]VideoFile, error) {
	logger.Infof("开始扫描下载目录: %s", downloadDir)

	var selectedVideos []VideoFile

	// 1. 首先扫描根目录下的视频文件
	rootVideos, err := vs.selectVideosFromRootDirectory(downloadDir)
	if err != nil {
		logger.Errorf("扫描根目录视频文件失败: %v", err)
	} else {
		selectedVideos = append(selectedVideos, rootVideos...)
		if len(rootVideos) > 0 {
			logger.Infof("根目录中找到 %d 个视频文件", len(rootVideos))
		}
	}

	// 2. 获取所有子目录
	taskDirs, err := vs.getTaskDirectories(downloadDir)
	if err != nil {
		return nil, fmt.Errorf("获取任务目录失败: %w", err)
	}

	// 3. 遍历每个任务目录
	for _, taskDir := range taskDirs {
		logger.Infof("处理任务目录: %s", taskDir)

		// 检查下载是否完成（使用高级检测）
		if !vs.downloadChecker.IsDownloadCompleteAdvanced(taskDir) {
			logger.Infof("目录 %s 下载未完成，跳过上传", taskDir)
			continue
		}

		videos, err := vs.selectVideosFromTaskDirectory(taskDir)
		if err != nil {
			logger.Errorf("处理任务目录失败 %s: %v", taskDir, err)
			continue
		}

		selectedVideos = append(selectedVideos, videos...)
	}

	logger.Infof("总共选择了 %d 个视频文件进行上传", len(selectedVideos))
	return selectedVideos, nil
}

// getTaskDirectories 获取所有任务目录
func (vs *VideoSelector) getTaskDirectories(downloadDir string) ([]string, error) {
	var taskDirs []string

	entries, err := os.ReadDir(downloadDir)
	if err != nil {
		return nil, err
	}

	for _, entry := range entries {
		if entry.IsDir() {
			taskDir := filepath.Join(downloadDir, entry.Name())
			taskDirs = append(taskDirs, taskDir)
		}
	}

	return taskDirs, nil
}

// selectVideosFromRootDirectory 从根目录中选择视频文件（不包括子目录）
func (vs *VideoSelector) selectVideosFromRootDirectory(downloadDir string) ([]VideoFile, error) {
	logger.Debugf("扫描根目录下的视频文件: %s", downloadDir)

	// 获取根目录中的所有视频文件（不递归）
	allVideos, err := vs.findVideoFilesInDirectory(downloadDir, false)
	if err != nil {
		return nil, err
	}

	if len(allVideos) == 0 {
		logger.Debugf("根目录 %s 中没有找到视频文件", downloadDir)
		return nil, nil
	}

	logger.Infof("根目录 %s 中找到 %d 个视频文件", downloadDir, len(allVideos))

	// 🔥 关键修复：检查根目录文件是否正在被aria2下载
	var readyVideos []VideoFile
	for _, video := range allVideos {
		// 检查文件是否正在被下载
		if vs.downloadChecker.IsFileBeingDownloaded(video.Path) {
			logger.Infof("根目录文件 %s 正在被aria2下载，跳过上传", video.Name)
			continue
		}

		// 检查文件是否稳定（最近没有被修改）
		if !vs.isFileStableForUpload(video.Path) {
			logger.Infof("根目录文件 %s 最近被修改，等待稳定", video.Name)
			continue
		}

		readyVideos = append(readyVideos, video)
	}

	if len(readyVideos) == 0 {
		logger.Debugf("根目录 %s 中没有准备好上传的视频文件", downloadDir)
		return nil, nil
	}

	logger.Infof("根目录 %s 中有 %d 个文件准备好上传", downloadDir, len(readyVideos))

	// 为所有视频文件设置任务目录为特殊标识（避免误删根目录）
	for i := range readyVideos {
		readyVideos[i].TaskDir = downloadDir + "/__ROOT__"
	}

	// 分析文件类型和选择策略
	return vs.analyzeAndSelectVideos(readyVideos)
}

// selectVideosFromTaskDirectory 从单个任务目录中选择视频文件
func (vs *VideoSelector) selectVideosFromTaskDirectory(taskDir string) ([]VideoFile, error) {
	// 获取目录中的所有视频文件
	allVideos, err := vs.findAllVideoFiles(taskDir)
	if err != nil {
		return nil, err
	}

	if len(allVideos) == 0 {
		logger.Infof("目录 %s 中没有找到视频文件", taskDir)
		return nil, nil
	}

	logger.Infof("目录 %s 中找到 %d 个视频文件", taskDir, len(allVideos))

	// 为所有视频文件设置任务目录
	for i := range allVideos {
		allVideos[i].TaskDir = taskDir
	}

	// 分析文件类型和选择策略
	return vs.analyzeAndSelectVideos(allVideos)
}

// findVideoFilesInDirectory 查找目录中的视频文件
// recursive: true表示递归查找子目录，false表示只查找当前目录
func (vs *VideoSelector) findVideoFilesInDirectory(dir string, recursive bool) ([]VideoFile, error) {
	var videos []VideoFile

	if recursive {
		// 递归查找（原有逻辑）
		err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			// 跳过目录
			if info.IsDir() {
				return nil
			}

			// 检查是否为视频文件
			if !vs.isVideoFile(path) {
				return nil
			}

			// 检查文件大小
			if info.Size() < vs.minFileSize {
				logger.Debugf("跳过小文件: %s (大小: %d bytes)", path, info.Size())
				return nil
			}

			// 检查是否为分段文件
			isSegment, segmentIndex := vs.isSegmentFile(path)

			video := VideoFile{
				Path:         path,
				Name:         info.Name(),
				Size:         info.Size(),
				IsSegment:    isSegment,
				SegmentIndex: segmentIndex,
			}

			videos = append(videos, video)
			return nil
		})
		return videos, err
	} else {
		// 非递归查找（只查找当前目录）
		entries, err := os.ReadDir(dir)
		if err != nil {
			return nil, err
		}

		for _, entry := range entries {
			// 跳过子目录
			if entry.IsDir() {
				continue
			}

			filePath := filepath.Join(dir, entry.Name())

			// 检查是否为视频文件
			if !vs.isVideoFile(filePath) {
				continue
			}

			// 获取文件信息
			info, err := entry.Info()
			if err != nil {
				logger.Warnf("无法获取文件信息: %s -> %v", filePath, err)
				continue
			}

			// 检查文件大小
			if info.Size() < vs.minFileSize {
				logger.Debugf("跳过小文件: %s (大小: %d bytes)", filePath, info.Size())
				continue
			}

			// 检查是否为分段文件
			isSegment, segmentIndex := vs.isSegmentFile(filePath)

			video := VideoFile{
				Path:         filePath,
				Name:         info.Name(),
				Size:         info.Size(),
				IsSegment:    isSegment,
				SegmentIndex: segmentIndex,
			}

			videos = append(videos, video)
		}
		return videos, nil
	}
}

// findAllVideoFiles 查找目录中的所有视频文件（递归）
func (vs *VideoSelector) findAllVideoFiles(dir string) ([]VideoFile, error) {
	return vs.findVideoFilesInDirectory(dir, true)
}

// isVideoFile 检查文件是否为视频文件
func (vs *VideoSelector) isVideoFile(filePath string) bool {
	ext := strings.ToLower(filepath.Ext(filePath))
	for _, videoExt := range vs.videoExtensions {
		if ext == videoExt {
			return true
		}
	}
	return false
}

// isSegmentFile 检查文件是否为分段文件
func (vs *VideoSelector) isSegmentFile(filePath string) (bool, int) {
	fileName := strings.ToLower(filepath.Base(filePath))

	for _, pattern := range vs.segmentPatterns {
		if pattern.MatchString(fileName) {
			// 尝试提取分段索引
			segmentIndex := vs.extractSegmentIndex(fileName)
			return true, segmentIndex
		}
	}

	return false, 0
}

// extractSegmentIndex 提取分段索引
func (vs *VideoSelector) extractSegmentIndex(fileName string) int {
	// 简单的数字提取逻辑
	re := regexp.MustCompile(`\d+`)
	matches := re.FindAllString(fileName, -1)

	if len(matches) > 0 {
		// 取最后一个数字作为分段索引
		lastMatch := matches[len(matches)-1]
		if len(lastMatch) <= 3 { // 避免提取到年份等大数字
			var index int
			fmt.Sscanf(lastMatch, "%d", &index)
			return index
		}
	}

	return 0
}

// analyzeAndSelectVideos 分析并选择需要上传的视频文件
func (vs *VideoSelector) analyzeAndSelectVideos(videos []VideoFile) ([]VideoFile, error) {
	if len(videos) == 0 {
		return nil, nil
	}

	// 分离分段文件和非分段文件
	var segmentFiles []VideoFile
	var singleFiles []VideoFile

	for _, video := range videos {
		if video.IsSegment {
			segmentFiles = append(segmentFiles, video)
		} else {
			singleFiles = append(singleFiles, video)
		}
	}

	var selectedVideos []VideoFile

	// 处理非分段文件：选择最大的文件
	if len(singleFiles) > 0 {
		largestFile := vs.findLargestFile(singleFiles)
		selectedVideos = append(selectedVideos, largestFile)
		logger.Infof("选择最大的单个文件: %s (大小: %.2f MB)",
			largestFile.Name, float64(largestFile.Size)/(1024*1024))
	}

	// 处理分段文件：按系列分组并选择每个系列
	if len(segmentFiles) > 0 {
		segmentSeries := vs.groupSegmentFiles(segmentFiles)
		for seriesName, series := range segmentSeries {
			// 对分段文件按索引排序
			sort.Slice(series, func(i, j int) bool {
				return series[i].SegmentIndex < series[j].SegmentIndex
			})

			selectedVideos = append(selectedVideos, series...)
			logger.Infof("选择分段文件系列: %s (%d 个文件)", seriesName, len(series))
		}
	}

	return selectedVideos, nil
}

// findLargestFile 找到最大的文件（过滤广告文件）
func (vs *VideoSelector) findLargestFile(files []VideoFile) VideoFile {
	if len(files) == 0 {
		return VideoFile{}
	}

	// 过滤掉广告文件
	validFiles := vs.filterAdvertisementFiles(files)
	if len(validFiles) == 0 {
		// 如果所有文件都被过滤了，返回原始最大文件
		validFiles = files
	}

	largest := validFiles[0]
	for _, file := range validFiles[1:] {
		if file.Size > largest.Size {
			largest = file
		}
	}

	return largest
}

// filterAdvertisementFiles 过滤广告文件
func (vs *VideoSelector) filterAdvertisementFiles(files []VideoFile) []VideoFile {
	var validFiles []VideoFile

	// 广告文件关键词
	adKeywords := []string{
		"苍老师", "苍 老 师", "强力推荐", "推荐", "广告", "ad", "advertisement",
		"promo", "promotion", "trailer", "preview", "sample", "demo",
		"免费", "free", "游戏", "game", "下载", "download", "网站", "website",
		"命运女神", "命運女神", "18禁", "手游", "指定",
	}

	for _, file := range files {
		fileName := strings.ToLower(file.Name)
		isAd := false

		// 检查是否包含广告关键词
		for _, keyword := range adKeywords {
			if strings.Contains(fileName, strings.ToLower(keyword)) {
				isAd = true
				logger.Debugf("过滤广告文件: %s (包含关键词: %s)", file.Name, keyword)
				break
			}
		}

		// 检查文件大小是否过小（可能是广告文件）
		if file.Size < 100*1024*1024 { // 小于100MB的文件可能是广告
			// 但如果所有文件都小于100MB，则不过滤
			hasLargeFile := false
			for _, f := range files {
				if f.Size >= 100*1024*1024 {
					hasLargeFile = true
					break
				}
			}
			if hasLargeFile {
				logger.Debugf("过滤小文件（可能是广告）: %s (大小: %.2f MB)",
					file.Name, float64(file.Size)/(1024*1024))
				isAd = true
			}
		}

		if !isAd {
			validFiles = append(validFiles, file)
		}
	}

	return validFiles
}

// groupSegmentFiles 将分段文件按系列分组
func (vs *VideoSelector) groupSegmentFiles(segmentFiles []VideoFile) map[string][]VideoFile {
	series := make(map[string][]VideoFile)

	for _, file := range segmentFiles {
		// 提取系列名称（去除分段标识）
		seriesName := vs.extractSeriesName(file.Name)
		series[seriesName] = append(series[seriesName], file)
	}

	return series
}

// extractSeriesName 提取系列名称
func (vs *VideoSelector) extractSeriesName(fileName string) string {
	// 移除分段标识，保留基础名称
	name := fileName

	// 移除扩展名
	ext := filepath.Ext(name)
	name = strings.TrimSuffix(name, ext)

	// 移除常见的分段标识
	patterns := []string{
		`\.part\d+$`,
		`\.\d{2,3}$`,
		`[_-](part)?\d+$`,
		`\.(cd|disc)\d+$`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		name = re.ReplaceAllString(name, "")
	}

	return name
}

// GetVideoSelectionSummary 获取视频选择摘要
func (vs *VideoSelector) GetVideoSelectionSummary(videos []VideoFile) string {
	if len(videos) == 0 {
		return "没有选择任何视频文件"
	}

	var totalSize int64
	segmentCount := 0
	singleCount := 0

	for _, video := range videos {
		totalSize += video.Size
		if video.IsSegment {
			segmentCount++
		} else {
			singleCount++
		}
	}

	summary := fmt.Sprintf("选择了 %d 个视频文件进行上传:\n", len(videos))
	summary += fmt.Sprintf("- 单个文件: %d 个\n", singleCount)
	summary += fmt.Sprintf("- 分段文件: %d 个\n", segmentCount)
	summary += fmt.Sprintf("- 总大小: %.2f GB\n", float64(totalSize)/(1024*1024*1024))

	return summary
}

// ValidateVideoFile 验证视频文件
func (vs *VideoSelector) ValidateVideoFile(filePath string) error {
	// 检查文件是否存在
	info, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		return fmt.Errorf("视频文件不存在: %s", filePath)
	}
	if err != nil {
		return fmt.Errorf("无法访问视频文件: %w", err)
	}

	// 检查是否为文件
	if info.IsDir() {
		return fmt.Errorf("路径是目录而不是文件: %s", filePath)
	}

	// 检查文件大小
	if info.Size() < vs.minFileSize {
		return fmt.Errorf("文件大小过小 (%.2f MB < %.2f MB): %s",
			float64(info.Size())/(1024*1024),
			float64(vs.minFileSize)/(1024*1024),
			filePath)
	}

	// 检查文件格式
	if !vs.isVideoFile(filePath) {
		return fmt.Errorf("不支持的视频格式: %s", filePath)
	}

	return nil
}

// isFileStableForUpload 检查文件是否稳定可以上传
func (vs *VideoSelector) isFileStableForUpload(filePath string) bool {
	info, err := os.Stat(filePath)
	if err != nil {
		logger.Debugf("无法获取文件信息 %s: %v", filePath, err)
		return false
	}

	// 检查文件是否在最近30秒内被修改
	stableTime := 30 * time.Second
	if time.Since(info.ModTime()) < stableTime {
		logger.Debugf("文件 %s 最近被修改 (%v)，等待稳定",
			filePath, time.Since(info.ModTime()))
		return false
	}

	// 检查文件大小是否合理（大于1MB）
	if info.Size() < 1024*1024 {
		logger.Debugf("文件 %s 大小过小 (%d bytes)，可能下载未完成", filePath, info.Size())
		return false
	}

	return true
}

// SmartRenameVideoFile 智能重命名视频文件
// 将所有视频文件重命名为 getav.net@xxx.mp4/mkv 格式
func SmartRenameVideoFile(originalFilename string) string {
	// 如果文件名已经以 getav.net@ 开头，保持原样
	if strings.HasPrefix(originalFilename, "getav.net@") {
		logger.Debugf("文件名已经是getav.net格式，保持原样: %s", originalFilename)
		return originalFilename
	}

	var baseFilename string

	// 检查文件名是否包含@符号
	if strings.Contains(originalFilename, "@") {
		// 分割文件名，取@后面的部分
		parts := strings.SplitN(originalFilename, "@", 2)
		if len(parts) == 2 {
			domainPart := parts[0]
			filenamePart := parts[1]

			// 检查域名部分是否包含域名格式
			domainPattern := regexp.MustCompile(`^[a-zA-Z0-9\-]+\.(com|net|org|cn|jp|tv|xxx|info|biz)$`)
			if domainPattern.MatchString(domainPart) {
				// 是域名格式，使用@后面的部分
				baseFilename = filenamePart
				logger.Debugf("检测到域名格式，提取文件名部分: %s", baseFilename)
			} else {
				// 不是域名格式，使用整个文件名
				baseFilename = originalFilename
			}
		} else {
			// 分割失败，使用整个文件名
			baseFilename = originalFilename
		}
	} else {
		// 没有@符号，直接使用原文件名
		baseFilename = originalFilename
	}

	// 生成新的文件名
	newFilename := fmt.Sprintf("getav.net@%s", baseFilename)

	logger.Infof("智能重命名视频文件: %s -> %s", originalFilename, newFilename)
	return newFilename
}

// GetSmartRenamedFilename 获取智能重命名后的文件名（不修改原文件）
func GetSmartRenamedFilename(filePath string) string {
	originalFilename := filepath.Base(filePath)
	return SmartRenameVideoFile(originalFilename)
}
