package streamhg

import (
	"fmt"
	"strings"
)

// ErrorType 错误类型
type ErrorType string

const (
	ErrorTypeNetwork    ErrorType = "network"
	ErrorTypeAPI        ErrorType = "api"
	ErrorTypeValidation ErrorType = "validation"
	ErrorTypeFileAccess ErrorType = "file_access"
	ErrorTypeFileSize   ErrorType = "file_size"
	ErrorTypeUpload     ErrorType = "upload"
	ErrorTypeRateLimit  ErrorType = "rate_limit"
)

// StreamHGError StreamHG错误接口
type StreamHGError interface {
	error
	Type() ErrorType
	Code() int
	Details() string
}

// BaseError 基础错误结构
type BaseError struct {
	ErrorType ErrorType `json:"error_type"`
	Message   string    `json:"message"`
	Detail    string    `json:"detail"`
	ErrorCode int       `json:"error_code"`
}

func (e *BaseError) Error() string {
	if e.Detail != "" {
		return fmt.Sprintf("%s: %s", e.Message, e.Detail)
	}
	return e.Message
}

func (e *BaseError) Type() ErrorType {
	return e.ErrorType
}

func (e *BaseError) Code() int {
	return e.ErrorCode
}

func (e *BaseError) Details() string {
	return e.Detail
}

// NetworkError 网络错误
type NetworkError struct {
	*BaseError
}

func NewNetworkError(message, detail string) *NetworkError {
	return &NetworkError{
		BaseError: &BaseError{
			ErrorType: ErrorTypeNetwork,
			Message:   message,
			Detail:    detail,
			ErrorCode: 0,
		},
	}
}

// APIError API错误
type APIError struct {
	*BaseError
}

func NewAPIError(message, detail string, code int) *APIError {
	return &APIError{
		BaseError: &BaseError{
			ErrorType: ErrorTypeAPI,
			Message:   message,
			Detail:    detail,
			ErrorCode: code,
		},
	}
}// ValidationError 验证错误
type ValidationError struct {
	*BaseError
}

func NewValidationError(message, detail string) *ValidationError {
	return &ValidationError{
		BaseError: &BaseError{
			ErrorType: ErrorTypeValidation,
			Message:   message,
			Detail:    detail,
			ErrorCode: 400,
		},
	}
}

// FileError 文件错误
type FileError struct {
	*BaseError
}

func NewFileError(errorType ErrorType, message string, err error) *FileError {
	detail := ""
	if err != nil {
		detail = err.Error()
	}
	
	return &FileError{
		BaseError: &BaseError{
			ErrorType: errorType,
			Message:   message,
			Detail:    detail,
			ErrorCode: 500,
		},
	}
}

// UploadError 上传错误
type UploadError struct {
	*BaseError
}

func NewUploadError(message, detail string, code int) *UploadError {
	return &UploadError{
		BaseError: &BaseError{
			ErrorType: ErrorTypeUpload,
			Message:   message,
			Detail:    detail,
			ErrorCode: code,
		},
	}
}

// IsRateLimitError 检查是否为速率限制错误
func IsRateLimitError(err error) bool {
	if err == nil {
		return false
	}
	
	if streamhgErr, ok := err.(StreamHGError); ok {
		return streamhgErr.Type() == ErrorTypeRateLimit
	}
	
	// 检查错误消息中是否包含速率限制关键词
	errMsg := strings.ToLower(err.Error())
	rateLimitKeywords := []string{
		"rate limit", "too many requests", "quota exceeded",
		"api limit", "请求过于频繁", "速率限制",
	}
	
	for _, keyword := range rateLimitKeywords {
		if strings.Contains(errMsg, keyword) {
			return true
		}
	}
	
	return false
}