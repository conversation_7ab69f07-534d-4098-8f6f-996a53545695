package streamhg

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"magnet-downloader/pkg/logger"
)

// StreamingUploader 实现真正的零内存流式上传（复制StreamTape策略）
type StreamingUploader struct {
	client     *http.Client
	chunkSize  int64 // 读取块大小
	bufferSize int   // 缓冲区大小
}

// NewStreamingUploader 创建流式上传器
func NewStreamingUploader(timeout time.Duration) *StreamingUploader {
	// 对于大文件，确保超时时间足够长
	if timeout < 30*time.Minute {
		timeout = 30 * time.Minute // 最少30分钟超时
	}

	return &StreamingUploader{
		client: &http.Client{
			Timeout: timeout,
			Transport: &http.Transport{
				MaxIdleConns:          10,
				IdleConnTimeout:       5 * time.Minute, // 增加空闲连接超时
				TLSHandshakeTimeout:   30 * time.Second,
				ResponseHeaderTimeout: 60 * time.Second, // 增加响应头超时
				DisableCompression:    true,
				DisableKeepAlives:     false, // 启用keep-alive
			},
		},
		chunkSize:  64 * 1024,  // 64KB读取块
		bufferSize: 256 * 1024, // 256KB缓冲区
	}
}

// UploadFileStreaming 执行真正的零内存流式上传
func (su *StreamingUploader) UploadFileStreaming(serverURL, filePath, filename string, client *Client) (*UploadResult, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return nil, NewFileError(ErrorTypeFileAccess, "打开文件失败", err)
	}
	defer file.Close()

	// 获取文件信息
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, NewFileError(ErrorTypeFileAccess, "获取文件信息失败", err)
	}

	logger.Infof("开始零内存流式上传: %s (%.2f MB)", filename, float64(fileInfo.Size())/(1024*1024))

	// 生成multipart边界
	boundary := generateBoundary()

	// 计算multipart总大小
	totalSize := su.calculateStreamingMultipartSize(boundary, filename, fileInfo.Size(), client.config.APIKey)

	// 创建流式读取器
	reader := su.createStreamingReader(file, boundary, filename, fileInfo.Size(), client.config.APIKey)

	// 创建HTTP请求
	req, err := http.NewRequest("POST", serverURL, reader)
	if err != nil {
		return nil, NewUploadError("创建HTTP请求失败", err.Error(), 500)
	}

	// 设置头部
	req.Header.Set("Content-Type", fmt.Sprintf("multipart/form-data; boundary=%s", boundary))
	req.Header.Set("User-Agent", "magnet-downloader/1.0")
	req.Header.Set("Connection", "keep-alive")

	// 关键：设置确切的Content-Length，避免chunked encoding
	req.ContentLength = totalSize
	req.Header.Set("Content-Length", fmt.Sprintf("%d", totalSize))

	// 强制禁用chunked encoding
	req.TransferEncoding = []string{"identity"}

	logger.Infof("流式上传请求: Content-Length=%d, Boundary=%s", totalSize, boundary)

	// 执行请求
	resp, err := su.client.Do(req)
	if err != nil {
		return nil, NewNetworkError("流式上传HTTP请求失败", err.Error())
	}
	defer resp.Body.Close()

	logger.Infof("流式上传响应: HTTP %d", resp.StatusCode)

	// 处理响应（复用现有的响应处理逻辑）
	return client.handleUploadResponse(resp, filename)
}

// createStreamingReader 创建流式读取器（StreamHG使用"file"字段名）
func (su *StreamingUploader) createStreamingReader(file *os.File, boundary, filename string, fileSize int64, apiKey string) io.Reader {
	// StreamHG API key字段
	apiKeyField := fmt.Sprintf("--%s\r\nContent-Disposition: form-data; name=\"key\"\r\n\r\n%s\r\n", boundary, apiKey)
	
	// 文件字段（注意：StreamHG使用"file"而不是"file1"）
	fileHeader := fmt.Sprintf("--%s\r\nContent-Disposition: form-data; name=\"file\"; filename=\"%s\"\r\nContent-Type: application/octet-stream\r\n\r\n", boundary, filename)
	footer := fmt.Sprintf("\r\n--%s--\r\n", boundary)

	// 组合读取器：API key + 文件头部 + 文件内容 + 尾部
	return io.MultiReader(
		strings.NewReader(apiKeyField),
		strings.NewReader(fileHeader),
		&ChunkedFileReader{
			file:      file,
			filename:  filename,
			totalSize: fileSize,
		},
		strings.NewReader(footer),
	)
}

// calculateStreamingMultipartSize 计算流式multipart总大小
func (su *StreamingUploader) calculateStreamingMultipartSize(boundary, filename string, fileSize int64, apiKey string) int64 {
	apiKeyField := fmt.Sprintf("--%s\r\nContent-Disposition: form-data; name=\"key\"\r\n\r\n%s\r\n", boundary, apiKey)
	fileHeader := fmt.Sprintf("--%s\r\nContent-Disposition: form-data; name=\"file\"; filename=\"%s\"\r\nContent-Type: application/octet-stream\r\n\r\n", boundary, filename)
	footer := fmt.Sprintf("\r\n--%s--\r\n", boundary)

	return int64(len(apiKeyField)) + int64(len(fileHeader)) + fileSize + int64(len(footer))
}

// ChunkedFileReader 分块文件读取器（带进度显示）
type ChunkedFileReader struct {
	file         *os.File
	filename     string
	totalSize    int64
	bytesRead    int64
	lastProgress int
	mutex        sync.Mutex // 保护并发访问
}

// Read 实现io.Reader接口，带进度显示的文件读取
func (cfr *ChunkedFileReader) Read(p []byte) (n int, err error) {
	// 从文件读取
	n, err = cfr.file.Read(p)
	if n > 0 {
		cfr.mutex.Lock()
		cfr.bytesRead += int64(n)
		currentBytes := cfr.bytesRead
		cfr.mutex.Unlock()

		// 计算进度百分比
		progress := int(float64(currentBytes) / float64(cfr.totalSize) * 100)

		// 每5%显示一次进度，避免日志过多，并且防止进度倒退
		cfr.mutex.Lock()
		if progress > cfr.lastProgress && (progress >= cfr.lastProgress+5 || progress == 100) {
			cfr.lastProgress = progress
			logger.Infof("StreamHG上传进度: %s - %d%% (%.2f MB / %.2f MB)",
				cfr.filename,
				progress,
				float64(currentBytes)/(1024*1024),
				float64(cfr.totalSize)/(1024*1024))
		}
		cfr.mutex.Unlock()
	}

	return n, err
}

// generateBoundary 生成multipart边界
func generateBoundary() string {
	return fmt.Sprintf("----formdata-magnet-downloader-%d", time.Now().UnixNano())
}

// performZeroMemoryUpload 执行零内存流式上传（使用StreamTape策略）
func (c *Client) performZeroMemoryUpload(filePath, filename, folderID string) (*UploadResult, error) {
	var lastErr error

	// 重试上传
	for attempt := 0; attempt < c.config.MaxRetries; attempt++ {
		if attempt > 0 {
			waitTime := c.calculateBackoff(attempt, IsRateLimitError(lastErr))
			logger.Debugf("重试StreamHG零内存上传，等待 %v (attempt %d/%d)", waitTime, attempt+1, c.config.MaxRetries)
			time.Sleep(waitTime)
		}

		// 1. 获取上传服务器URL
		serverURL, err := c.GetUploadServer(folderID)
		if err != nil {
			lastErr = err
			logger.Warnf("获取StreamHG上传服务器失败 (attempt %d/%d): %v", attempt+1, c.config.MaxRetries, err)
			continue
		}

		// 2. 使用StreamTape策略执行零内存流式上传
		uploader := NewStreamingUploader(c.config.Timeout)
		result, err := uploader.UploadFileStreaming(serverURL, filePath, filename, c)
		if err == nil {
			logger.Infof("StreamHG零内存上传成功: %s", filename)
			return result, nil
		}

		lastErr = err
		logger.Warnf("StreamHG零内存上传失败 (attempt %d/%d): %v", attempt+1, c.config.MaxRetries, err)
	}

	return nil, NewUploadError("StreamHG零内存上传失败", 
		fmt.Sprintf("所有重试都失败了，最后错误: %v", lastErr), 500)
}