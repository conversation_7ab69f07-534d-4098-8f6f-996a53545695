package streamhg

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"magnet-downloader/pkg/logger"
)

// Client StreamHG客户端
type Client struct {
	config     *Config
	httpClient *http.Client
	rateLimiter *time.Ticker
	mutex      sync.Mutex
}

// NewClient 创建新的StreamHG客户端
func NewClient(config *Config) *Client {
	if config == nil {
		config = DefaultConfig()
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		logger.Errorf("StreamHG配置验证失败: %v", err)
		return nil
	}

	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: config.Timeout,
		Transport: &http.Transport{
			MaxIdleConns:        10,
			IdleConnTimeout:     30 * time.Second,
			DisableCompression:  true,
			DisableKeepAlives:   false,
		},
	}

	// 创建速率限制器 (20请求/秒)
	rateLimiter := time.NewTicker(50 * time.Millisecond)

	client := &Client{
		config:      config,
		httpClient:  httpClient,
		rateLimiter: rateLimiter,
	}

	logger.Infof("StreamHG客户端初始化成功: %s", config.BaseURL)
	return client
}// Close 关闭客户端
func (c *Client) Close() {
	if c.rateLimiter != nil {
		c.rateLimiter.Stop()
	}
}

// TestConnection 测试连接
func (c *Client) TestConnection() error {
	logger.Infof("测试StreamHG连接...")
	
	// 构建账户信息请求URL
	reqURL := fmt.Sprintf("%s/api/account/info?key=%s", 
		c.config.BaseURL, c.config.APIKey)
	
	resp, err := c.httpClient.Get(reqURL)
	if err != nil {
		return NewNetworkError("连接StreamHG失败", err.Error())
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return NewNetworkError("读取响应失败", err.Error())
	}

	logger.Debugf("StreamHG API响应: %s", string(body))

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return NewAPIError("解析响应失败", err.Error(), resp.StatusCode)
	}

	// 检查响应状态
	if status, ok := result["status"].(float64); ok {
		if status != 200 {
			msg := "Unknown error"
			if msgStr, exists := result["msg"].(string); exists {
				msg = msgStr
			}
			logger.Errorf("StreamHG API错误: status=%v, msg=%s, 请求URL=%s", status, msg, reqURL)
			return NewAPIError("StreamHG API错误", msg, int(status))
		}
	} else {
		return NewAPIError("无效的响应格式", "缺少status字段", resp.StatusCode)
	}

	logger.Infof("StreamHG连接测试成功")
	return nil
}

// GetUploadServer 获取上传服务器URL
func (c *Client) GetUploadServer(folderID string) (string, error) {
	// 应用速率限制
	c.applyRateLimit()

	// 构建请求URL
	reqURL := fmt.Sprintf("%s/api/upload/server?key=%s", 
		c.config.BaseURL, c.config.APIKey)
	
	if folderID != "" {
		reqURL += "&fld_id=" + folderID
	}

	logger.Debugf("获取StreamHG上传服务器: %s", reqURL)

	resp, err := c.httpClient.Get(reqURL)
	if err != nil {
		return "", NewNetworkError("获取上传服务器失败", err.Error())
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", NewNetworkError("读取响应失败", err.Error())
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return "", NewAPIError("解析响应失败", err.Error(), resp.StatusCode)
	}

	// 检查响应状态
	if status, ok := result["status"].(float64); ok && status != 200 {
		msg := "Unknown error"
		if msgStr, exists := result["msg"].(string); exists {
			msg = msgStr
		}
		return "", NewAPIError("获取上传服务器失败", msg, int(status))
	}

	// 提取上传服务器URL
	if serverURL, ok := result["result"].(string); ok {
		logger.Debugf("StreamHG上传服务器URL: %s", serverURL)
		return serverURL, nil
	}

	return "", NewAPIError("无效的响应格式", "缺少result字段", resp.StatusCode)
}

// applyRateLimit 应用速率限制
func (c *Client) applyRateLimit() {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	<-c.rateLimiter.C
}