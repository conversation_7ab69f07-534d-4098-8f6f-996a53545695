package streamhg

import (
	"fmt"
	"time"
)

// Config StreamHG客户端配置
type Config struct {
	APIKey     string        `json:"api_key" yaml:"api_key"`         // API密钥
	BaseURL    string        `json:"base_url" yaml:"base_url"`       // API基础URL
	Timeout    time.Duration `json:"timeout" yaml:"timeout"`         // 请求超时时间
	MaxRetries int           `json:"max_retries" yaml:"max_retries"` // 最大重试次数
}

// Validate 验证配置
func (c *Config) Validate() error {
	if c.APIKey == "" {
		return fmt.Errorf("StreamHG API key is required")
	}
	
	if c.BaseURL == "" {
		return fmt.Errorf("StreamHG base URL is required")
	}
	
	if c.Timeout <= 0 {
		c.Timeout = 300 * time.Second // 默认5分钟
	}
	
	if c.MaxRetries <= 0 {
		c.MaxRetries = 3 // 默认重试3次
	}
	
	return nil
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		BaseURL:    "https://streamhgapi.com",
		Timeout:    300 * time.Second,
		MaxRetries: 3,
	}
}