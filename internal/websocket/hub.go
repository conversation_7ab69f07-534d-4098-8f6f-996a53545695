package websocket

import (
	"sync"
	"time"

	"magnet-downloader/pkg/logger"
)

// Hub WebSocket连接管理中心
type Hub struct {
	// 注册的客户端
	clients map[*Client]bool

	// 用户ID到客户端的映射
	userClients map[uint][]*Client

	// 频道到客户端的映射
	channelClients map[string][]*Client

	// 广播消息通道
	broadcast chan *Message

	// 注册客户端通道
	register chan *Client

	// 注销客户端通道
	unregister chan *Client

	// 发送用户消息通道
	sendToUser chan *UserMessage

	// 发送频道消息通道
	sendToChannel chan *ChannelMessage

	// 读写锁
	mutex sync.RWMutex

	// 统计信息
	stats *HubStats
}

// UserMessage 用户消息
type UserMessage struct {
	UserID  uint
	Message *Message
}

// ChannelMessage 频道消息
type ChannelMessage struct {
	Channel string
	Message *Message
}

// HubStats Hub统计信息
type HubStats struct {
	TotalConnections     int64     `json:"total_connections"`
	CurrentConnections   int       `json:"current_connections"`
	MessagesSent         int64     `json:"messages_sent"`
	MessagesReceived     int64     `json:"messages_received"`
	StartTime            time.Time `json:"start_time"`
	LastMessageTime      time.Time `json:"last_message_time"`
	UserConnections      int       `json:"user_connections"`
	AdminConnections     int       `json:"admin_connections"`
	ChannelSubscriptions int       `json:"channel_subscriptions"`
}

// NewHub 创建新的Hub
func NewHub() *Hub {
	return &Hub{
		clients:        make(map[*Client]bool),
		userClients:    make(map[uint][]*Client),
		channelClients: make(map[string][]*Client),
		broadcast:      make(chan *Message, 100), // 增加缓冲区避免阻塞
		register:       make(chan *Client),
		unregister:     make(chan *Client),
		sendToUser:     make(chan *UserMessage),
		sendToChannel:  make(chan *ChannelMessage),
		stats: &HubStats{
			StartTime: time.Now(),
		},
	}
}

// Run 运行Hub
func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.registerClient(client)

		case client := <-h.unregister:
			h.unregisterClient(client)

		case message := <-h.broadcast:
			h.broadcastMessage(message)

		case userMsg := <-h.sendToUser:
			h.sendToUserClients(userMsg.UserID, userMsg.Message)

		case channelMsg := <-h.sendToChannel:
			h.sendToChannelClients(channelMsg.Channel, channelMsg.Message)
		}
	}
}

// registerClient 注册客户端
func (h *Hub) registerClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.clients[client] = true

	// 添加到用户客户端映射
	h.userClients[client.userID] = append(h.userClients[client.userID], client)

	// 更新统计信息
	h.stats.TotalConnections++
	h.stats.CurrentConnections = len(h.clients)
	if client.IsAdmin() {
		h.stats.AdminConnections++
	} else {
		h.stats.UserConnections++
	}

	logger.Infof("Client registered: ID=%s, UserID=%d, Username=%s, Total=%d",
		client.id, client.userID, client.username, len(h.clients))
}

// unregisterClient 注销客户端
func (h *Hub) unregisterClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if _, ok := h.clients[client]; ok {
		delete(h.clients, client)
		close(client.send)

		// 从用户客户端映射中移除
		userClients := h.userClients[client.userID]
		for i, c := range userClients {
			if c == client {
				h.userClients[client.userID] = append(userClients[:i], userClients[i+1:]...)
				break
			}
		}

		// 如果用户没有其他连接，删除映射
		if len(h.userClients[client.userID]) == 0 {
			delete(h.userClients, client.userID)
		}

		// 从频道客户端映射中移除
		for channel := range client.channels {
			h.removeClientFromChannel(client, channel)
		}

		// 更新统计信息
		h.stats.CurrentConnections = len(h.clients)
		if client.IsAdmin() {
			h.stats.AdminConnections--
		} else {
			h.stats.UserConnections--
		}

		logger.Infof("Client unregistered: ID=%s, UserID=%d, Username=%s, Total=%d",
			client.id, client.userID, client.username, len(h.clients))
	}
}

// broadcastMessage 广播消息
func (h *Hub) broadcastMessage(message *Message) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	count := 0
	for client := range h.clients {
		if client.CanReceiveMessage(message) {
			select {
			case client.send <- func() []byte {
				data, _ := message.ToJSON()
				return data
			}():
				count++
			default:
				close(client.send)
				delete(h.clients, client)
			}
		}
	}

	h.stats.MessagesSent += int64(count)
	h.stats.LastMessageTime = time.Now()

	logger.Debugf("Broadcast message: Type=%s, Recipients=%d", message.Type, count)
}

// sendToUserClients 发送消息给指定用户的所有客户端
func (h *Hub) sendToUserClients(userID uint, message *Message) {
	h.mutex.RLock()
	clients := h.userClients[userID]
	h.mutex.RUnlock()

	if len(clients) == 0 {
		logger.Debugf("No clients found for user %d", userID)
		return
	}

	count := 0
	data, _ := message.ToJSON()

	for _, client := range clients {
		select {
		case client.send <- data:
			count++
		default:
			// 客户端发送缓冲区满，关闭连接
			h.unregister <- client
		}
	}

	h.stats.MessagesSent += int64(count)
	h.stats.LastMessageTime = time.Now()

	logger.Debugf("Sent message to user %d: Type=%s, Recipients=%d", userID, message.Type, count)
}

// sendToChannelClients 发送消息给频道的所有客户端
func (h *Hub) sendToChannelClients(channel string, message *Message) {
	h.mutex.RLock()
	clients := h.channelClients[channel]
	h.mutex.RUnlock()

	if len(clients) == 0 {
		logger.Debugf("No clients found for channel %s", channel)
		return
	}

	count := 0
	data, _ := message.ToJSON()

	for _, client := range clients {
		select {
		case client.send <- data:
			count++
		default:
			// 客户端发送缓冲区满，关闭连接
			h.unregister <- client
		}
	}

	h.stats.MessagesSent += int64(count)
	h.stats.LastMessageTime = time.Now()

	logger.Debugf("Sent message to channel %s: Type=%s, Recipients=%d", channel, message.Type, count)
}

// addClientToChannel 添加客户端到频道
func (h *Hub) addClientToChannel(client *Client, channel string) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.channelClients[channel] = append(h.channelClients[channel], client)
	h.stats.ChannelSubscriptions++
}

// removeClientFromChannel 从频道移除客户端
func (h *Hub) removeClientFromChannel(client *Client, channel string) {
	clients := h.channelClients[channel]
	for i, c := range clients {
		if c == client {
			h.channelClients[channel] = append(clients[:i], clients[i+1:]...)
			h.stats.ChannelSubscriptions--
			break
		}
	}

	// 如果频道没有客户端，删除频道
	if len(h.channelClients[channel]) == 0 {
		delete(h.channelClients, channel)
	}
}

// Broadcast 广播消息
func (h *Hub) Broadcast(message *Message) {
	select {
	case h.broadcast <- message:
	default:
		logger.Warn("Broadcast channel is full, message dropped")
	}
}

// SendToUser 发送消息给指定用户
func (h *Hub) SendToUser(userID uint, message *Message) {
	select {
	case h.sendToUser <- &UserMessage{UserID: userID, Message: message}:
	default:
		logger.Warn("SendToUser channel is full, message dropped")
	}
}

// SendToChannel 发送消息给指定频道
func (h *Hub) SendToChannel(channel string, message *Message) {
	select {
	case h.sendToChannel <- &ChannelMessage{Channel: channel, Message: message}:
	default:
		logger.Warn("SendToChannel channel is full, message dropped")
	}
}

// GetStats 获取统计信息
func (h *Hub) GetStats() *HubStats {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	// 复制统计信息
	stats := *h.stats
	stats.CurrentConnections = len(h.clients)

	// 重新计算用户和管理员连接数
	userCount := 0
	adminCount := 0
	for client := range h.clients {
		if client.IsAdmin() {
			adminCount++
		} else {
			userCount++
		}
	}
	stats.UserConnections = userCount
	stats.AdminConnections = adminCount

	return &stats
}

// GetClients 获取所有客户端信息
func (h *Hub) GetClients() []map[string]interface{} {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	clients := make([]map[string]interface{}, 0, len(h.clients))
	for client := range h.clients {
		clients = append(clients, client.GetInfo())
	}

	return clients
}

// GetUserClients 获取指定用户的客户端
func (h *Hub) GetUserClients(userID uint) []*Client {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	return h.userClients[userID]
}

// GetChannelClients 获取指定频道的客户端
func (h *Hub) GetChannelClients(channel string) []*Client {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	return h.channelClients[channel]
}

// IsUserOnline 检查用户是否在线
func (h *Hub) IsUserOnline(userID uint) bool {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	return len(h.userClients[userID]) > 0
}

// GetOnlineUserCount 获取在线用户数量
func (h *Hub) GetOnlineUserCount() int {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	return len(h.userClients)
}
