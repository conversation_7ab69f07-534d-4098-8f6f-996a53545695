package scheduler

import (
	"context"
	"fmt"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/internal/repository"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/cron"
	"magnet-downloader/pkg/logger"
)

// JAVMaintenanceJob JAV数据维护定时任务
type JAVMaintenanceJob struct {
	repo       repository.Repository
	javService service.JAVService
}

// NewJAVMaintenanceJob 创建JAV数据维护任务
func NewJAVMaintenanceJob(repo repository.Repository, javService service.JAVService) cron.Job {
	return &JAVMaintenanceJob{
		repo:       repo,
		javService: javService,
	}
}

// Execute 执行JAV数据维护任务
func (j *JAVMaintenanceJob) Execute(ctx context.Context) error {
	logger.Info("Starting JAV maintenance job")
	startTime := time.Now()

	// 创建带超时的上下文
	jobCtx, cancel := context.WithTimeout(ctx, 30*time.Minute)
	defer cancel()

	// 执行维护任务
	stats, err := j.executeMaintenanceTasks(jobCtx)
	if err != nil {
		logger.Errorf("JAV maintenance job failed: %v", err)
		return err
	}

	duration := time.Since(startTime)
	logger.Infof("JAV maintenance job completed in %v: %s", duration, stats.String())

	return nil
}

// JAVMaintenanceStats 维护统计信息
type JAVMaintenanceStats struct {
	CleanedMovies        int `json:"cleaned_movies"`
	CleanedActors        int `json:"cleaned_actors"`
	CleanedMagnets       int `json:"cleaned_magnets"`
	UpdatedScores        int `json:"updated_scores"`
	FixedRelations       int `json:"fixed_relations"`
	OptimizedIndexes     int `json:"optimized_indexes"`
	CleanedOrphanRecords int `json:"cleaned_orphan_records"`
	TotalErrors          int `json:"total_errors"`
}

// String 返回统计信息的字符串表示
func (s *JAVMaintenanceStats) String() string {
	return fmt.Sprintf("Cleaned: %d movies, %d actors, %d magnets; Updated: %d scores; Fixed: %d relations; Optimized: %d indexes; Orphans: %d; Errors: %d",
		s.CleanedMovies, s.CleanedActors, s.CleanedMagnets, s.UpdatedScores,
		s.FixedRelations, s.OptimizedIndexes, s.CleanedOrphanRecords, s.TotalErrors)
}

// executeMaintenanceTasks 执行维护任务
func (j *JAVMaintenanceJob) executeMaintenanceTasks(ctx context.Context) (*JAVMaintenanceStats, error) {
	stats := &JAVMaintenanceStats{}

	// 1. 清理无效的影片记录
	if err := j.cleanInvalidMovies(ctx, stats); err != nil {
		logger.Errorf("Failed to clean invalid movies: %v", err)
		stats.TotalErrors++
	}

	// 2. 清理无效的演员记录
	if err := j.cleanInvalidActors(ctx, stats); err != nil {
		logger.Errorf("Failed to clean invalid actors: %v", err)
		stats.TotalErrors++
	}

	// 3. 清理无效的磁力链接
	if err := j.cleanInvalidMagnets(ctx, stats); err != nil {
		logger.Errorf("Failed to clean invalid magnets: %v", err)
		stats.TotalErrors++
	}

	// 4. 更新磁力链接评分
	if err := j.updateMagnetScores(ctx, stats); err != nil {
		logger.Errorf("Failed to update magnet scores: %v", err)
		stats.TotalErrors++
	}

	// 5. 修复关联关系
	if err := j.fixRelations(ctx, stats); err != nil {
		logger.Errorf("Failed to fix relations: %v", err)
		stats.TotalErrors++
	}

	// 6. 清理孤儿记录
	if err := j.cleanOrphanRecords(ctx, stats); err != nil {
		logger.Errorf("Failed to clean orphan records: %v", err)
		stats.TotalErrors++
	}

	// 7. 优化数据库索引
	if err := j.optimizeIndexes(ctx, stats); err != nil {
		logger.Errorf("Failed to optimize indexes: %v", err)
		stats.TotalErrors++
	}

	return stats, nil
}

// cleanInvalidMovies 清理无效的影片记录
func (j *JAVMaintenanceJob) cleanInvalidMovies(ctx context.Context, stats *JAVMaintenanceStats) error {
	logger.Info("Starting invalid movies cleanup")

	// 清理条件：
	// 1. 采集状态为失败且超过30天未更新
	// 2. 没有任何有效信息的影片记录
	// 3. 重复的影片记录

	cutoffTime := time.Now().AddDate(0, 0, -30) // 30天前

	// 获取长期失败的影片
	failedMovies, err := j.repo.JAVMovie().GetByScrapingStatusAndTime(
		model.JAVScrapingStatusFailed,
		cutoffTime,
	)
	if err != nil {
		return fmt.Errorf("failed to get failed movies: %w", err)
	}

	cleaned := 0
	for _, movie := range failedMovies {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// 检查是否有关联的下载任务或其他重要数据
		hasImportantData, err := j.checkMovieHasImportantData(movie.ID)
		if err != nil {
			logger.Errorf("Failed to check important data for movie %s: %v", movie.Code, err)
			continue
		}

		if !hasImportantData {
			// 删除影片记录
			if err := j.repo.JAVMovie().Delete(movie.ID); err != nil {
				logger.Errorf("Failed to delete movie %s: %v", movie.Code, err)
				continue
			}
			cleaned++
			logger.Infof("Cleaned invalid movie: %s", movie.Code)
		}
	}

	stats.CleanedMovies = cleaned
	logger.Infof("Cleaned %d invalid movies", cleaned)
	return nil
}

// checkMovieHasImportantData 检查影片是否有重要数据
func (j *JAVMaintenanceJob) checkMovieHasImportantData(movieID uint) (bool, error) {
	// 检查是否有下载记录
	downloads, err := j.javService.GetMovieDownloads(movieID)
	if err != nil && err != repository.ErrNotFound {
		return false, err
	}
	if len(downloads) > 0 {
		return true, nil // 有下载记录
	}

	// 检查是否有用户收藏或评分
	// 这里可以添加更多检查逻辑

	return false, nil
}

// cleanInvalidActors 清理无效的演员记录
func (j *JAVMaintenanceJob) cleanInvalidActors(ctx context.Context, stats *JAVMaintenanceStats) error {
	logger.Info("Starting invalid actors cleanup")

	// 获取没有关联影片的演员
	orphanActors, err := j.repo.JAVActor().GetOrphanActors()
	if err != nil {
		return fmt.Errorf("failed to get orphan actors: %w", err)
	}

	cleaned := 0
	for _, actor := range orphanActors {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// 删除孤儿演员记录
		if err := j.repo.JAVActor().Delete(actor.ID); err != nil {
			logger.Errorf("Failed to delete orphan actor %s: %v", actor.Name, err)
			continue
		}
		cleaned++
	}

	stats.CleanedActors = cleaned
	logger.Infof("Cleaned %d orphan actors", cleaned)
	return nil
}

// cleanInvalidMagnets 清理无效的磁力链接
func (j *JAVMaintenanceJob) cleanInvalidMagnets(ctx context.Context, stats *JAVMaintenanceStats) error {
	logger.Info("Starting invalid magnets cleanup")

	// 清理条件：
	// 1. 评分过低的磁力链接
	// 2. 重复的磁力链接
	// 3. 无效的磁力链接格式

	// 获取低分磁力链接
	lowScoreMagnets, err := j.repo.JAVMagnet().GetByScoreRange(0, 30) // 评分0-30的磁力链接
	if err != nil {
		return fmt.Errorf("failed to get low score magnets: %w", err)
	}

	cleaned := 0
	for _, magnet := range lowScoreMagnets {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// 检查是否有关联的下载任务
		hasDownloads, err := j.checkMagnetHasDownloads(magnet.ID)
		if err != nil {
			logger.Errorf("Failed to check downloads for magnet %d: %v", magnet.ID, err)
			continue
		}

		if !hasDownloads {
			// 删除低分磁力链接
			if err := j.repo.JAVMagnet().Delete(magnet.ID); err != nil {
				logger.Errorf("Failed to delete low score magnet %d: %v", magnet.ID, err)
				continue
			}
			cleaned++
		}
	}

	stats.CleanedMagnets = cleaned
	logger.Infof("Cleaned %d invalid magnets", cleaned)
	return nil
}

// checkMagnetHasDownloads 检查磁力链接是否有下载记录
func (j *JAVMaintenanceJob) checkMagnetHasDownloads(magnetID uint) (bool, error) {
	// 这里需要实现检查逻辑
	// 暂时返回false，表示没有下载记录
	return false, nil
}

// updateMagnetScores 更新磁力链接评分
func (j *JAVMaintenanceJob) updateMagnetScores(ctx context.Context, stats *JAVMaintenanceStats) error {
	logger.Info("Starting magnet scores update")

	// 获取需要更新评分的磁力链接（评分为0或很久未更新的）
	cutoffTime := time.Now().AddDate(0, 0, -7) // 7天前
	magnetsToUpdate, err := j.repo.JAVMagnet().GetForScoreUpdate(cutoffTime, 50)
	if err != nil {
		return fmt.Errorf("failed to get magnets for score update: %w", err)
	}

	updated := 0
	for _, magnet := range magnetsToUpdate {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// 重新计算评分
		newScore := j.calculateMagnetScore(magnet)
		if newScore != magnet.Score {
			magnet.Score = newScore
			if err := j.repo.JAVMagnet().Update(magnet); err != nil {
				logger.Errorf("Failed to update magnet score %d: %v", magnet.ID, err)
				continue
			}
			updated++
		}
	}

	stats.UpdatedScores = updated
	logger.Infof("Updated %d magnet scores", updated)
	return nil
}

// calculateMagnetScore 计算磁力链接评分
func (j *JAVMaintenanceJob) calculateMagnetScore(magnet *model.JAVMagnet) float64 {
	// 这里实现评分计算逻辑
	// 基于文件大小、清晰度、字幕、种子数等因素
	score := 50.0 // 基础分

	// 根据清晰度加分
	switch magnet.Quality {
	case model.JAVMagnetQuality4K:
		score += 30
	case model.JAVMagnetQuality1080p:
		score += 20
	case model.JAVMagnetQuality720p:
		score += 10
	}

	// 根据字幕加分
	if magnet.HasSubtitle {
		score += 15
		if magnet.SubtitleLanguage == model.JAVMagnetSubtitleLanguageChinese {
			score += 5 // 中文字幕额外加分
		}
	}

	// 根据种子数加分
	if magnet.Seeders > 0 {
		score += float64(magnet.Seeders) * 0.1
		if score > 100 {
			score = 100 // 最高100分
		}
	}

	// 根据文件大小合理性加分
	if magnet.FileSize > 0 {
		// 根据清晰度判断文件大小是否合理
		expectedSize := j.getExpectedFileSize(magnet.Quality)
		if magnet.FileSize >= int64(float64(expectedSize)*0.8) && magnet.FileSize <= expectedSize*2 {
			score += 5 // 文件大小合理
		}
	}

	return score
}

// getExpectedFileSize 获取预期文件大小
func (j *JAVMaintenanceJob) getExpectedFileSize(quality model.JAVMagnetQuality) int64 {
	switch quality {
	case model.JAVMagnetQuality4K:
		return 8 * 1024 * 1024 * 1024 // 8GB
	case model.JAVMagnetQuality1080p:
		return 3 * 1024 * 1024 * 1024 // 3GB
	case model.JAVMagnetQuality720p:
		return 1536 * 1024 * 1024 // 1.5GB
	default:
		return 1024 * 1024 * 1024 // 1GB
	}
}

// fixRelations 修复关联关系
func (j *JAVMaintenanceJob) fixRelations(ctx context.Context, stats *JAVMaintenanceStats) error {
	logger.Info("Starting relations fix")

	// 修复影片-演员关联关系
	fixed, err := j.fixMovieActorRelations(ctx)
	if err != nil {
		return fmt.Errorf("failed to fix movie-actor relations: %w", err)
	}

	stats.FixedRelations = fixed
	logger.Infof("Fixed %d relations", fixed)
	return nil
}

// fixMovieActorRelations 修复影片-演员关联关系
func (j *JAVMaintenanceJob) fixMovieActorRelations(ctx context.Context) (int, error) {
	// 这里可以实现修复逻辑，例如：
	// 1. 删除指向不存在记录的关联
	// 2. 修复重复的关联关系
	// 3. 补充缺失的关联关系

	// 暂时返回0，表示没有修复任何关系
	return 0, nil
}

// cleanOrphanRecords 清理孤儿记录
func (j *JAVMaintenanceJob) cleanOrphanRecords(ctx context.Context, stats *JAVMaintenanceStats) error {
	logger.Info("Starting orphan records cleanup")

	cleaned := 0

	// TODO: 清理孤儿的影片-演员关联记录
	// 这个功能需要更复杂的关联表操作，暂时跳过

	stats.CleanedOrphanRecords = cleaned
	logger.Infof("Cleaned %d orphan records", cleaned)
	return nil
}

// optimizeIndexes 优化数据库索引
func (j *JAVMaintenanceJob) optimizeIndexes(ctx context.Context, stats *JAVMaintenanceStats) error {
	logger.Info("Starting database indexes optimization")

	// 这里可以实现数据库索引优化逻辑
	// 例如：重建索引、更新统计信息等

	// 暂时返回成功，实际实现中可以调用数据库优化命令
	stats.OptimizedIndexes = 1
	logger.Info("Database indexes optimization completed")
	return nil
}

// GetName 获取任务名称
func (j *JAVMaintenanceJob) GetName() string {
	return "jav_maintenance"
}

// GetDescription 获取任务描述
func (j *JAVMaintenanceJob) GetDescription() string {
	return "JAV data maintenance job - cleans invalid data and optimizes database"
}