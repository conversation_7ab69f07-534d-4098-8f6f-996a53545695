package scheduler

import (
	"context"
	"fmt"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/internal/repository"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/cron"
	"magnet-downloader/pkg/logger"
)

// JAVDownloadJob JAV下载监控定时任务
type JAVDownloadJob struct {
	repo               repository.Repository
	javService         service.JAVService
	javDownloadService service.JAVDownloadService
	taskService        service.TaskService
	config             *JAVDownloadJobConfig
}

// JAVDownloadJobConfig JAV下载任务配置
type JAVDownloadJobConfig struct {
	Enabled              bool          `json:"enabled"`                // 是否启用任务
	AutoCreateDownloads  bool          `json:"auto_create_downloads"`  // 是否自动创建下载任务
	MaxDownloadsPerRun   int           `json:"max_downloads_per_run"`  // 每次运行最大下载数
	MaxRetries           int           `json:"max_retries"`            // 最大重试次数
	RetryDelay           time.Duration `json:"retry_delay"`            // 重试延迟
	MaxProcessTime       time.Duration `json:"max_process_time"`       // 最大处理时间
	MinMagnetScore       float64       `json:"min_magnet_score"`       // 最小磁力链接评分
	PreferQuality        string        `json:"prefer_quality"`         // 首选质量
	PreferSubtitle       bool          `json:"prefer_subtitle"`        // 是否优先字幕
	AutoUpload           bool          `json:"auto_upload"`            // 是否自动上传
	CleanupCompleted     bool          `json:"cleanup_completed"`      // 是否清理已完成的下载
	CleanupThreshold     time.Duration `json:"cleanup_threshold"`      // 清理阈值
	MonitorStuckTasks    bool          `json:"monitor_stuck_tasks"`    // 是否监控卡住的任务
	StuckTaskThreshold   time.Duration `json:"stuck_task_threshold"`   // 卡住任务阈值
}

// NewJAVDownloadJob 创建JAV下载监控任务
func NewJAVDownloadJob(repo repository.Repository, javService service.JAVService, javDownloadService service.JAVDownloadService, taskService service.TaskService, config *JAVDownloadJobConfig) cron.Job {
	if config == nil {
		config = &JAVDownloadJobConfig{
			Enabled:              true,
			AutoCreateDownloads:  false, // 默认不自动创建下载
			MaxDownloadsPerRun:   5,
			MaxRetries:           3,
			RetryDelay:           10 * time.Minute,
			MaxProcessTime:       20 * time.Minute,
			MinMagnetScore:       70.0,
			PreferQuality:        "1080p",
			PreferSubtitle:       true,
			AutoUpload:           false,
			CleanupCompleted:     true,
			CleanupThreshold:     7 * 24 * time.Hour, // 7天
			MonitorStuckTasks:    true,
			StuckTaskThreshold:   2 * time.Hour, // 2小时
		}
	}

	return &JAVDownloadJob{
		repo:               repo,
		javService:         javService,
		javDownloadService: javDownloadService,
		taskService:        taskService,
		config:             config,
	}
}

// Execute 执行JAV下载监控任务
func (j *JAVDownloadJob) Execute(ctx context.Context) error {
	if !j.config.Enabled {
		logger.Debug("JAV download job is disabled, skipping execution")
		return nil
	}

	logger.Info("Starting JAV download monitoring job")
	startTime := time.Now()

	// 创建带超时的上下文
	jobCtx, cancel := context.WithTimeout(ctx, j.config.MaxProcessTime)
	defer cancel()

	// 执行下载监控任务
	stats, err := j.executeDownloadTasks(jobCtx)
	if err != nil {
		logger.Errorf("JAV download job failed: %v", err)
		return err
	}

	duration := time.Since(startTime)
	logger.Infof("JAV download job completed in %v: %s", duration, stats.String())

	return nil
}

// JAVDownloadStats 下载统计信息
type JAVDownloadStats struct {
	ProcessedDownloads   int `json:"processed_downloads"`
	CreatedDownloads     int `json:"created_downloads"`
	CompletedDownloads   int `json:"completed_downloads"`
	FailedDownloads      int `json:"failed_downloads"`
	RetriedDownloads     int `json:"retried_downloads"`
	CleanedDownloads     int `json:"cleaned_downloads"`
	StuckTasksFixed      int `json:"stuck_tasks_fixed"`
	TotalErrors          int `json:"total_errors"`
}

// String 返回统计信息的字符串表示
func (s *JAVDownloadStats) String() string {
	return fmt.Sprintf("Processed: %d downloads (%d created, %d completed, %d failed, %d retried), %d cleaned, %d stuck tasks fixed, %d errors",
		s.ProcessedDownloads, s.CreatedDownloads, s.CompletedDownloads, s.FailedDownloads,
		s.RetriedDownloads, s.CleanedDownloads, s.StuckTasksFixed, s.TotalErrors)
}

// executeDownloadTasks 执行下载任务
func (j *JAVDownloadJob) executeDownloadTasks(ctx context.Context) (*JAVDownloadStats, error) {
	stats := &JAVDownloadStats{}

	// 1. 自动创建下载任务（如果启用）
	if j.config.AutoCreateDownloads {
		if err := j.createAutoDownloads(ctx, stats); err != nil {
			logger.Errorf("Failed to create auto downloads: %v", err)
			stats.TotalErrors++
		}
	}

	// 2. 监控现有下载任务状态
	if err := j.monitorDownloadStatus(ctx, stats); err != nil {
		logger.Errorf("Failed to monitor download status: %v", err)
		stats.TotalErrors++
	}

	// 3. 重试失败的下载任务
	if err := j.retryFailedDownloads(ctx, stats); err != nil {
		logger.Errorf("Failed to retry failed downloads: %v", err)
		stats.TotalErrors++
	}

	// 4. 监控卡住的任务
	if j.config.MonitorStuckTasks {
		if err := j.monitorStuckTasks(ctx, stats); err != nil {
			logger.Errorf("Failed to monitor stuck tasks: %v", err)
			stats.TotalErrors++
		}
	}

	// 5. 清理已完成的下载
	if j.config.CleanupCompleted {
		if err := j.cleanupCompletedDownloads(ctx, stats); err != nil {
			logger.Errorf("Failed to cleanup completed downloads: %v", err)
			stats.TotalErrors++
		}
	}

	return stats, nil
}

// createAutoDownloads 自动创建下载任务
func (j *JAVDownloadJob) createAutoDownloads(ctx context.Context, stats *JAVDownloadStats) error {
	logger.Info("Starting auto download creation")

	// 获取有高质量磁力链接但未下载的影片
	movies, err := j.getMoviesForAutoDownload(ctx)
	if err != nil {
		return fmt.Errorf("failed to get movies for auto download: %w", err)
	}

	if len(movies) == 0 {
		logger.Info("No movies found for auto download")
		return nil
	}

	logger.Infof("Found %d movies for auto download", len(movies))

	created := 0
	for _, movie := range movies {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		if created >= j.config.MaxDownloadsPerRun {
			logger.Infof("Reached max downloads per run limit (%d)", j.config.MaxDownloadsPerRun)
			break
		}

		if err := j.createDownloadForMovie(ctx, movie); err != nil {
			logger.Errorf("Failed to create download for movie %s: %v", movie.Code, err)
			stats.TotalErrors++
			continue
		}

		created++
		stats.CreatedDownloads++
	}

	logger.Infof("Created %d auto downloads", created)
	return nil
}

// getMoviesForAutoDownload 获取适合自动下载的影片
func (j *JAVDownloadJob) getMoviesForAutoDownload(ctx context.Context) ([]*model.JAVMovie, error) {
	// 查询条件：
	// 1. 有高质量磁力链接（评分 >= MinMagnetScore）
	// 2. 尚未创建下载任务
	// 3. 采集状态为完成
	// 4. 最近更新（避免下载太老的影片）

	// 这里需要实现复杂的查询逻辑，暂时返回空列表
	// 实际实现中需要：
	// 1. 查询有高质量磁力链接的影片
	// 2. 排除已有下载任务的影片
	// 3. 按某种优先级排序（评分、发布时间等）

	return []*model.JAVMovie{}, nil
}

// createDownloadForMovie 为影片创建下载任务
func (j *JAVDownloadJob) createDownloadForMovie(ctx context.Context, movie *model.JAVMovie) error {
	// 获取最佳磁力链接
	magnet, err := j.javService.GetBestMagnet(movie.ID)
	if err != nil {
		return fmt.Errorf("failed to get best magnet for movie %s: %w", movie.Code, err)
	}

	if magnet.Score < j.config.MinMagnetScore {
		return fmt.Errorf("best magnet score (%.1f) below threshold (%.1f) for movie %s",
			magnet.Score, j.config.MinMagnetScore, movie.Code)
	}

	// 创建下载请求
	req := &service.CreateJAVDownloadRequest{
		MovieCode: movie.Code,
		MagnetID:  &magnet.ID,
		Options: &service.JAVDownloadOptions{
			DownloadOptions: &service.DownloadOptions{
				Quality:        j.config.PreferQuality,
				PreferSubtitle: j.config.PreferSubtitle,
				AutoUpload:     j.config.AutoUpload,
				Priority:       3, // 自动下载使用中等优先级
			},
		},
	}

	// 创建下载任务（使用系统用户ID）
	systemUserID := uint(1) // 假设系统用户ID为1
	_, err = j.javDownloadService.CreateJAVDownload(systemUserID, req)
	if err != nil {
		return fmt.Errorf("failed to create download task for movie %s: %w", movie.Code, err)
	}

	logger.Infof("Created auto download for movie %s (magnet score: %.1f)", movie.Code, magnet.Score)
	return nil
}

// monitorDownloadStatus 监控下载任务状态
func (j *JAVDownloadJob) monitorDownloadStatus(ctx context.Context, stats *JAVDownloadStats) error {
	logger.Info("Starting download status monitoring")

	// 获取活跃的下载任务
	activeDownloads, err := j.getActiveDownloads(ctx)
	if err != nil {
		return fmt.Errorf("failed to get active downloads: %w", err)
	}

	if len(activeDownloads) == 0 {
		logger.Info("No active downloads to monitor")
		return nil
	}

	logger.Infof("Monitoring %d active downloads", len(activeDownloads))

	for _, download := range activeDownloads {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		if err := j.updateDownloadStatus(ctx, download, stats); err != nil {
			logger.Errorf("Failed to update download status for %s: %v", download.Movie.Code, err)
			stats.TotalErrors++
		}

		stats.ProcessedDownloads++
	}

	return nil
}

// getActiveDownloads 获取活跃的下载任务
func (j *JAVDownloadJob) getActiveDownloads(ctx context.Context) ([]*service.JAVDownloadResponse, error) {
	// 获取状态为进行中、等待中的下载任务
	req := &service.ListJAVDownloadsRequest{
		Status:   "downloading,pending,queued", // 多个状态
		Page:     1,
		PageSize: 100, // 限制数量
	}

	downloads, _, err := j.javDownloadService.ListJAVDownloads(req)
	if err != nil {
		return nil, fmt.Errorf("failed to list active downloads: %w", err)
	}

	return downloads, nil
}

// updateDownloadStatus 更新下载状态
func (j *JAVDownloadJob) updateDownloadStatus(ctx context.Context, download *service.JAVDownloadResponse, stats *JAVDownloadStats) error {
	// 检查关联的下载任务状态
	if download.DownloadTask == nil {
		return nil // 没有关联任务
	}

	task, err := j.taskService.GetTask(download.DownloadTask.ID)
	if err != nil {
		return fmt.Errorf("failed to get download task: %w", err)
	}

	// 根据任务状态更新下载状态
	switch task.Status {
	case model.TaskStatusCompleted:
		if download.Status != "completed" {
			// 更新下载状态为完成
			if err := j.updateDownloadStatusInDB(download.ID, "completed"); err != nil {
				return fmt.Errorf("failed to update download status to completed: %w", err)
			}
			stats.CompletedDownloads++
			logger.Infof("Download completed for movie %s", download.Movie.Code)
		}

	case model.TaskStatusFailed:
		if download.Status != "failed" {
			// 更新下载状态为失败
			if err := j.updateDownloadStatusInDB(download.ID, "failed"); err != nil {
				return fmt.Errorf("failed to update download status to failed: %w", err)
			}
			stats.FailedDownloads++
			logger.Infof("Download failed for movie %s", download.Movie.Code)
		}

	case model.TaskStatusRunning:
		if download.Status != "downloading" {
			// 更新下载状态为下载中
			if err := j.updateDownloadStatusInDB(download.ID, "downloading"); err != nil {
				return fmt.Errorf("failed to update download status to downloading: %w", err)
			}
		}
	}

	return nil
}

// updateDownloadStatusInDB 在数据库中更新下载状态
func (j *JAVDownloadJob) updateDownloadStatusInDB(downloadID uint, status string) error {
	// 这里需要实现更新下载状态的逻辑
	// 由于我们没有直接的更新方法，这里先记录日志
	logger.Infof("Would update download %d status to %s", downloadID, status)
	return nil
}

// retryFailedDownloads 重试失败的下载任务
func (j *JAVDownloadJob) retryFailedDownloads(ctx context.Context, stats *JAVDownloadStats) error {
	logger.Info("Starting failed download retry")

	// 获取最近失败的下载任务
	failedDownloads, err := j.getFailedDownloads(ctx)
	if err != nil {
		return fmt.Errorf("failed to get failed downloads: %w", err)
	}

	if len(failedDownloads) == 0 {
		logger.Info("No failed downloads to retry")
		return nil
	}

	logger.Infof("Found %d failed downloads to retry", len(failedDownloads))

	retried := 0
	for _, download := range failedDownloads {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		if retried >= j.config.MaxDownloadsPerRun {
			break
		}

		if err := j.retryDownload(ctx, download); err != nil {
			logger.Errorf("Failed to retry download for movie %s: %v", download.Movie.Code, err)
			stats.TotalErrors++
			continue
		}

		retried++
		stats.RetriedDownloads++
	}

	logger.Infof("Retried %d failed downloads", retried)
	return nil
}

// getFailedDownloads 获取失败的下载任务
func (j *JAVDownloadJob) getFailedDownloads(ctx context.Context) ([]*service.JAVDownloadResponse, error) {
	// 获取最近失败的下载任务（24小时内）
	req := &service.ListJAVDownloadsRequest{
		Status:   "failed",
		Page:     1,
		PageSize: 20, // 限制重试数量
	}

	downloads, _, err := j.javDownloadService.ListJAVDownloads(req)
	if err != nil {
		return nil, fmt.Errorf("failed to list failed downloads: %w", err)
	}

	// 过滤出可以重试的下载（检查重试延迟时间）
	var retryableDownloads []*service.JAVDownloadResponse
	for _, download := range downloads {
		// 检查是否已过重试延迟时间
		if time.Since(download.UpdatedAt) >= j.config.RetryDelay {
			retryableDownloads = append(retryableDownloads, download)
		}
	}

	return retryableDownloads, nil
}

// retryDownload 重试下载
func (j *JAVDownloadJob) retryDownload(ctx context.Context, download *service.JAVDownloadResponse) error {
	// 重新创建下载任务
	req := &service.CreateJAVDownloadRequest{
		MovieCode: download.Movie.Code,
		MagnetID:  &download.MagnetID,
		Options: &service.JAVDownloadOptions{
			DownloadOptions: &service.DownloadOptions{
				Quality:        j.config.PreferQuality,
				PreferSubtitle: j.config.PreferSubtitle,
				AutoUpload:     j.config.AutoUpload,
				Priority:       2, // 重试任务使用较高优先级
			},
		},
	}

	// 使用原用户ID重新创建
	_, err := j.javDownloadService.CreateJAVDownload(download.UserID, req)
	if err != nil {
		return fmt.Errorf("failed to recreate download task: %w", err)
	}

	logger.Infof("Retried download for movie %s", download.Movie.Code)
	return nil
}

// monitorStuckTasks 监控卡住的任务
func (j *JAVDownloadJob) monitorStuckTasks(ctx context.Context, stats *JAVDownloadStats) error {
	logger.Info("Starting stuck task monitoring")

	// 获取可能卡住的任务（运行时间超过阈值）
	stuckTasks, err := j.taskService.GetStuckTasks(j.config.StuckTaskThreshold)
	if err != nil {
		return fmt.Errorf("failed to get stuck tasks: %w", err)
	}

	if len(stuckTasks) == 0 {
		logger.Info("No stuck tasks found")
		return nil
	}

	logger.Infof("Found %d potentially stuck tasks", len(stuckTasks))

	for _, task := range stuckTasks {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		if err := j.handleStuckTask(ctx, task); err != nil {
			logger.Errorf("Failed to handle stuck task %d: %v", task.ID, err)
			stats.TotalErrors++
			continue
		}

		stats.StuckTasksFixed++
	}

	return nil
}

// handleStuckTask 处理卡住的任务
func (j *JAVDownloadJob) handleStuckTask(ctx context.Context, task *model.DownloadTask) error {
	logger.Infof("Handling stuck task %d (running for %v)", task.ID, time.Since(task.UpdatedAt))

	// 尝试重启任务
	if err := j.taskService.RestartTask(task.ID); err != nil {
		// 如果重启失败，标记为失败
		if err := j.taskService.FailTask(task.ID); err != nil {
			return fmt.Errorf("failed to fail stuck task: %w", err)
		}
		logger.Infof("Marked stuck task %d as failed", task.ID)
	} else {
		logger.Infof("Restarted stuck task %d", task.ID)
	}

	return nil
}

// cleanupCompletedDownloads 清理已完成的下载
func (j *JAVDownloadJob) cleanupCompletedDownloads(ctx context.Context, stats *JAVDownloadStats) error {
	logger.Info("Starting completed download cleanup")

	// 获取需要清理的已完成下载（超过清理阈值）
	cutoffTime := time.Now().Add(-j.config.CleanupThreshold)
	completedDownloads, err := j.getCompletedDownloadsForCleanup(ctx, cutoffTime)
	if err != nil {
		return fmt.Errorf("failed to get completed downloads for cleanup: %w", err)
	}

	if len(completedDownloads) == 0 {
		logger.Info("No completed downloads to cleanup")
		return nil
	}

	logger.Infof("Found %d completed downloads to cleanup", len(completedDownloads))

	for _, download := range completedDownloads {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		if err := j.cleanupDownload(ctx, download); err != nil {
			logger.Errorf("Failed to cleanup download %d: %v", download.ID, err)
			stats.TotalErrors++
			continue
		}

		stats.CleanedDownloads++
	}

	return nil
}

// getCompletedDownloadsForCleanup 获取需要清理的已完成下载
func (j *JAVDownloadJob) getCompletedDownloadsForCleanup(ctx context.Context, cutoffTime time.Time) ([]*service.JAVDownloadResponse, error) {
	// 这里需要实现查询逻辑，获取完成时间早于cutoffTime的下载
	// 暂时返回空列表
	return []*service.JAVDownloadResponse{}, nil
}

// cleanupDownload 清理下载
func (j *JAVDownloadJob) cleanupDownload(ctx context.Context, download *service.JAVDownloadResponse) error {
	// 清理下载记录（可以选择删除或标记为已清理）
	// 这里只记录日志，实际实现中可以删除数据库记录
	logger.Infof("Would cleanup download %d for movie %s", download.ID, download.Movie.Code)
	return nil
}

// GetName 获取任务名称
func (j *JAVDownloadJob) GetName() string {
	return "jav_download_monitor"
}

// GetDescription 获取任务描述
func (j *JAVDownloadJob) GetDescription() string {
	return "JAV download monitoring job - manages download tasks and monitors their status"
}

// IsEnabled 检查任务是否启用
func (j *JAVDownloadJob) IsEnabled() bool {
	return j.config.Enabled
}

// GetConfig 获取任务配置
func (j *JAVDownloadJob) GetConfig() *JAVDownloadJobConfig {
	return j.config
}

// UpdateConfig 更新任务配置
func (j *JAVDownloadJob) UpdateConfig(config *JAVDownloadJobConfig) {
	j.config = config
}