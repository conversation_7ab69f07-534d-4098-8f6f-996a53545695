package scheduler

import (
	"fmt"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/repository"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/cron"
	"magnet-downloader/pkg/logger"
)

// Scheduler 任务调度器
type Scheduler struct {
	manager  *cron.Manager
	config   *config.Config
	repo     repository.Repository
	services *service.Services
}

// NewScheduler 创建任务调度器
func NewScheduler(cfg *config.Config, repo repository.Repository, services *service.Services) (*Scheduler, error) {
	manager, err := cron.NewManager(cfg.Scheduler.Timezone)
	if err != nil {
		return nil, fmt.Errorf("failed to create cron manager: %w", err)
	}

	return &Scheduler{
		manager:  manager,
		config:   cfg,
		repo:     repo,
		services: services,
	}, nil
}

// Start 启动调度器
func (s *Scheduler) Start() error {
	logger.Info("Starting task scheduler")

	// 注册默认任务
	if err := s.registerDefaultJobs(); err != nil {
		return fmt.Errorf("failed to register default jobs: %w", err)
	}

	// 启动调度器
	s.manager.Start()
	
	logger.Info("Task scheduler started successfully")
	return nil
}

// Stop 停止调度器
func (s *Scheduler) Stop() {
	logger.Info("Stopping task scheduler")
	s.manager.Stop()
	logger.Info("Task scheduler stopped")
}

// registerDefaultJobs 注册默认任务
func (s *Scheduler) registerDefaultJobs() error {
	// 任务清理作业 - 每天凌晨2点执行
	taskCleanupJob := NewTaskCleanupJob(s.repo)
	if err := s.manager.AddJob("0 0 2 * * *", taskCleanupJob); err != nil {
		return fmt.Errorf("failed to add task cleanup job: %w", err)
	}

	// 系统统计作业 - 每小时执行一次
	systemStatsJob := NewSystemStatsJob(s.services)
	if err := s.manager.AddJob("0 0 * * * *", systemStatsJob); err != nil {
		return fmt.Errorf("failed to add system stats job: %w", err)
	}

	// 任务重试作业 - 每15分钟执行一次
	taskRetryJob := NewTaskRetryJob(s.services.Task)
	if err := s.manager.AddJob("0 */15 * * * *", taskRetryJob); err != nil {
		return fmt.Errorf("failed to add task retry job: %w", err)
	}

	// 配置缓存刷新作业 - 每6小时执行一次
	configCacheRefreshJob := NewConfigCacheRefreshJob(s.services.Config)
	if err := s.manager.AddJob("0 0 */6 * * *", configCacheRefreshJob); err != nil {
		return fmt.Errorf("failed to add config cache refresh job: %w", err)
	}

	// 健康检查作业 - 每5分钟执行一次
	healthCheckJob := NewHealthCheckJob(s.services)
	if err := s.manager.AddJob("0 */5 * * * *", healthCheckJob); err != nil {
		return fmt.Errorf("failed to add health check job: %w", err)
	}

	// 数据库维护作业 - 每周日凌晨3点执行
	dbMaintenanceJob := NewDatabaseMaintenanceJob(s.repo)
	if err := s.manager.AddJob("0 0 3 * * 0", dbMaintenanceJob); err != nil {
		return fmt.Errorf("failed to add database maintenance job: %w", err)
	}

	// 注册JAV相关任务
	if err := s.registerJAVJobs(); err != nil {
		return fmt.Errorf("failed to register JAV jobs: %w", err)
	}

	logger.Info("Default scheduled jobs registered successfully")
	return nil
}

// registerJAVJobs 注册JAV相关任务
func (s *Scheduler) registerJAVJobs() error {
	// 检查JAV功能是否启用
	if !s.config.JAV.Enabled {
		logger.Info("JAV functionality is disabled, skipping JAV job registration")
		return nil
	}

	logger.Info("Registering JAV scheduled jobs")

	// JAV数据采集作业 - 每小时执行一次
	if s.config.JAV.Scraping.Enabled {
		javScrapingConfig := &JAVScrapingJobConfig{
			Enabled:           true,
			BatchSize:         s.config.JAV.Scraping.BatchSize,
			MaxRetries:        s.config.JAV.Scraping.MaxRetries,
			RetryDelay:        5 * time.Minute,
			UpdateExisting:    s.config.JAV.Scraping.AutoMerge,
			UpdateThreshold:   7 * 24 * time.Hour, // 7天
			MaxProcessTime:    30 * time.Minute,
			SkipRecentMovies:  true,
			RecentThreshold:   24 * time.Hour, // 24小时
		}

		javScrapingJob := NewJAVScrapingJob(s.repo, s.services.JAV, s.services.JAVScraper, javScrapingConfig)
		if err := s.manager.AddJob("0 0 * * * *", javScrapingJob); err != nil {
			return fmt.Errorf("failed to add JAV scraping job: %w", err)
		}
		logger.Info("JAV scraping job registered (every hour)")
	}

	// JAV下载监控作业 - 每30分钟执行一次
	if s.config.JAV.Download.Enabled {
		javDownloadConfig := &JAVDownloadJobConfig{
			Enabled:              true,
			AutoCreateDownloads:  s.config.JAV.Download.AutoStart,
			MaxDownloadsPerRun:   s.config.JAV.Download.MaxConcurrent,
			MaxRetries:           3,
			RetryDelay:           10 * time.Minute,
			MaxProcessTime:       20 * time.Minute,
			MinMagnetScore:       70.0,
			PreferQuality:        s.config.JAV.Download.PreferQuality,
			PreferSubtitle:       s.config.JAV.Download.PreferSubtitle,
			AutoUpload:           s.config.JAV.Download.AutoUpload,
			CleanupCompleted:     s.config.JAV.Download.CleanAfterUpload,
			CleanupThreshold:     7 * 24 * time.Hour, // 7天
			MonitorStuckTasks:    true,
			StuckTaskThreshold:   2 * time.Hour, // 2小时
		}

		javDownloadJob := NewJAVDownloadJob(s.repo, s.services.JAV, s.services.JAVDownload, s.services.Task, javDownloadConfig)
		if err := s.manager.AddJob("0 */30 * * * *", javDownloadJob); err != nil {
			return fmt.Errorf("failed to add JAV download job: %w", err)
		}
		logger.Info("JAV download monitoring job registered (every 30 minutes)")
	}

	// JAV数据维护作业 - 每天凌晨4点执行
	javMaintenanceJob := NewJAVMaintenanceJob(s.repo, s.services.JAV)
	if err := s.manager.AddJob("0 0 4 * * *", javMaintenanceJob); err != nil {
		return fmt.Errorf("failed to add JAV maintenance job: %w", err)
	}
	logger.Info("JAV maintenance job registered (daily at 4 AM)")

	logger.Info("JAV scheduled jobs registered successfully")
	return nil
}

// AddJob 添加自定义任务
func (s *Scheduler) AddJob(spec string, job cron.Job) error {
	return s.manager.AddJob(spec, job)
}

// RemoveJob 移除任务
func (s *Scheduler) RemoveJob(jobName string) error {
	return s.manager.RemoveJob(jobName)
}

// GetJobs 获取所有任务信息
func (s *Scheduler) GetJobs() []map[string]interface{} {
	return s.manager.GetJobs()
}

// GetJobStats 获取指定任务统计信息
func (s *Scheduler) GetJobStats(jobName string) (map[string]interface{}, error) {
	return s.manager.GetJobStats(jobName)
}

// IsRunning 检查调度器是否运行中
func (s *Scheduler) IsRunning() bool {
	return s.manager.IsRunning()
}

// ValidateCronSpec 验证Cron表达式
func (s *Scheduler) ValidateCronSpec(spec string) error {
	return cron.ValidateCronSpec(spec)
}

// GetTimezone 获取时区
func (s *Scheduler) GetTimezone() string {
	return s.manager.GetTimezone().String()
}
