package scheduler

import (
	"context"
	"fmt"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/internal/repository"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/cron"
	"magnet-downloader/pkg/logger"
)

// JAVScrapingJob JAV数据采集定时任务
type JAVScrapingJob struct {
	repo        repository.Repository
	javService  service.JAVService
	scraperService service.JAVScraperService
	config      *JAVScrapingJobConfig
}

// JAVScrapingJobConfig JAV采集任务配置
type JAVScrapingJobConfig struct {
	Enabled           bool          `json:"enabled"`             // 是否启用任务
	BatchSize         int           `json:"batch_size"`          // 批量处理大小
	MaxRetries        int           `json:"max_retries"`         // 最大重试次数
	RetryDelay        time.Duration `json:"retry_delay"`         // 重试延迟
	UpdateExisting    bool          `json:"update_existing"`     // 是否更新现有数据
	UpdateThreshold   time.Duration `json:"update_threshold"`    // 更新阈值（多久未更新的数据需要重新采集）
	MaxProcessTime    time.Duration `json:"max_process_time"`    // 最大处理时间
	SkipRecentMovies  bool          `json:"skip_recent_movies"`  // 是否跳过最近采集的影片
	RecentThreshold   time.Duration `json:"recent_threshold"`    // 最近采集阈值
}

// NewJAVScrapingJob 创建JAV数据采集任务
func NewJAVScrapingJob(repo repository.Repository, javService service.JAVService, scraperService service.JAVScraperService, config *JAVScrapingJobConfig) cron.Job {
	if config == nil {
		config = &JAVScrapingJobConfig{
			Enabled:           true,
			BatchSize:         10,
			MaxRetries:        3,
			RetryDelay:        5 * time.Minute,
			UpdateExisting:    true,
			UpdateThreshold:   7 * 24 * time.Hour, // 7天
			MaxProcessTime:    30 * time.Minute,
			SkipRecentMovies:  true,
			RecentThreshold:   24 * time.Hour, // 24小时
		}
	}

	return &JAVScrapingJob{
		repo:           repo,
		javService:     javService,
		scraperService: scraperService,
		config:         config,
	}
}

// Execute 执行JAV数据采集任务
func (j *JAVScrapingJob) Execute(ctx context.Context) error {
	if !j.config.Enabled {
		logger.Debug("JAV scraping job is disabled, skipping execution")
		return nil
	}

	logger.Info("Starting JAV scraping job")
	startTime := time.Now()

	// 创建带超时的上下文
	jobCtx, cancel := context.WithTimeout(ctx, j.config.MaxProcessTime)
	defer cancel()

	// 执行采集任务
	stats, err := j.executeScrapingTasks(jobCtx)
	if err != nil {
		logger.Errorf("JAV scraping job failed: %v", err)
		return err
	}

	duration := time.Since(startTime)
	logger.Infof("JAV scraping job completed in %v: %s", duration, stats.String())

	return nil
}

// JAVScrapingStats 采集统计信息
type JAVScrapingStats struct {
	ProcessedMovies   int `json:"processed_movies"`
	NewMovies         int `json:"new_movies"`
	UpdatedMovies     int `json:"updated_movies"`
	FailedMovies      int `json:"failed_movies"`
	SkippedMovies     int `json:"skipped_movies"`
	ProcessedActors   int `json:"processed_actors"`
	ProcessedMagnets  int `json:"processed_magnets"`
	TotalErrors       int `json:"total_errors"`
}

// String 返回统计信息的字符串表示
func (s *JAVScrapingStats) String() string {
	return fmt.Sprintf("Processed: %d movies (%d new, %d updated, %d failed, %d skipped), %d actors, %d magnets, %d errors",
		s.ProcessedMovies, s.NewMovies, s.UpdatedMovies, s.FailedMovies, s.SkippedMovies,
		s.ProcessedActors, s.ProcessedMagnets, s.TotalErrors)
}

// executeScrapingTasks 执行采集任务
func (j *JAVScrapingJob) executeScrapingTasks(ctx context.Context) (*JAVScrapingStats, error) {
	stats := &JAVScrapingStats{}

	// 1. 采集新影片数据
	if err := j.scrapeNewMovies(ctx, stats); err != nil {
		return stats, fmt.Errorf("failed to scrape new movies: %w", err)
	}

	// 2. 更新现有影片数据
	if j.config.UpdateExisting {
		if err := j.updateExistingMovies(ctx, stats); err != nil {
			logger.Errorf("Failed to update existing movies: %v", err)
			stats.TotalErrors++
		}
	}

	// 3. 重试失败的采集任务
	if err := j.retryFailedScrapings(ctx, stats); err != nil {
		logger.Errorf("Failed to retry failed scrapings: %v", err)
		stats.TotalErrors++
	}

	return stats, nil
}

// scrapeNewMovies 采集新影片数据
func (j *JAVScrapingJob) scrapeNewMovies(ctx context.Context, stats *JAVScrapingStats) error {
	logger.Info("Starting to scrape new movies")

	// 获取最新的影片代码列表（这里可以从各个数据源获取最新发布的影片列表）
	// 为了演示，我们先实现一个简单的逻辑：检查数据库中缺失的影片
	
	// 获取需要采集的影片代码
	movieCodes, err := j.getMovieCodesToScrape(ctx)
	if err != nil {
		return fmt.Errorf("failed to get movie codes to scrape: %w", err)
	}

	if len(movieCodes) == 0 {
		logger.Info("No new movies to scrape")
		return nil
	}

	logger.Infof("Found %d movies to scrape", len(movieCodes))

	// 分批处理
	for i := 0; i < len(movieCodes); i += j.config.BatchSize {
		select {
		case <-ctx.Done():
			logger.Warn("JAV scraping job cancelled due to context timeout")
			return ctx.Err()
		default:
		}

		end := i + j.config.BatchSize
		if end > len(movieCodes) {
			end = len(movieCodes)
		}

		batch := movieCodes[i:end]
		if err := j.processBatch(ctx, batch, stats); err != nil {
			logger.Errorf("Failed to process batch %d-%d: %v", i, end-1, err)
			stats.TotalErrors++
		}

		// 批次间休息，避免对数据源造成过大压力
		if i+j.config.BatchSize < len(movieCodes) {
			time.Sleep(2 * time.Second)
		}
	}

	return nil
}

// getMovieCodesToScrape 获取需要采集的影片代码
func (j *JAVScrapingJob) getMovieCodesToScrape(ctx context.Context) ([]string, error) {
	// 这里可以实现多种策略：
	// 1. 从数据源获取最新发布的影片列表
	// 2. 从预定义的影片代码列表中获取
	// 3. 基于某种算法生成可能的影片代码
	
	// 为了演示，我们实现一个简单的策略：
	// 获取最近失败的采集任务，重新尝试
	
	var movieCodes []string
	
	// 获取采集状态为失败或待处理的影片
	var movies []*model.JAVMovie
	
	// 获取待处理的影片
	pendingMovies, err := j.repo.JAVMovie().GetByScrapingStatus(model.JAVScrapingStatusPending, 25)
	if err != nil {
		return nil, fmt.Errorf("failed to get pending movies: %w", err)
	}
	movies = append(movies, pendingMovies...)
	
	// 获取失败的影片
	failedMovies, err := j.repo.JAVMovie().GetByScrapingStatus(model.JAVScrapingStatusFailed, 25)
	if err != nil {
		return nil, fmt.Errorf("failed to get failed movies: %w", err)
	}
	movies = append(movies, failedMovies...)

	for _, movie := range movies {
		// 跳过最近采集过的影片
		if j.config.SkipRecentMovies && movie.UpdatedAt.After(time.Now().Add(-j.config.RecentThreshold)) {
			continue
		}
		movieCodes = append(movieCodes, movie.Code)
	}

	// 如果没有待采集的影片，可以尝试采集一些新的影片代码
	if len(movieCodes) == 0 {
		// 这里可以实现获取最新影片代码的逻辑
		// 例如：从JavBus首页获取最新发布的影片
		newCodes, err := j.getLatestMovieCodes(ctx)
		if err != nil {
			logger.Errorf("Failed to get latest movie codes: %v", err)
		} else {
			movieCodes = append(movieCodes, newCodes...)
		}
	}

	return movieCodes, nil
}

// getLatestMovieCodes 获取最新影片代码
func (j *JAVScrapingJob) getLatestMovieCodes(ctx context.Context) ([]string, error) {
	// 这里可以实现从各个数据源获取最新影片代码的逻辑
	// 为了演示，返回一些示例代码
	
	// 实际实现中，可以：
	// 1. 调用JavBus API获取最新影片
	// 2. 解析RSS feed
	// 3. 从其他数据源获取
	
	return []string{}, nil // 暂时返回空列表
}

// processBatch 处理一批影片代码
func (j *JAVScrapingJob) processBatch(ctx context.Context, movieCodes []string, stats *JAVScrapingStats) error {
	logger.Infof("Processing batch of %d movies", len(movieCodes))

	for _, code := range movieCodes {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		if err := j.processMovie(ctx, code, stats); err != nil {
			logger.Errorf("Failed to process movie %s: %v", code, err)
			stats.FailedMovies++
			stats.TotalErrors++
		}
		
		stats.ProcessedMovies++
		
		// 影片间休息，避免请求过于频繁
		time.Sleep(1 * time.Second)
	}

	return nil
}

// processMovie 处理单个影片
func (j *JAVScrapingJob) processMovie(ctx context.Context, code string, stats *JAVScrapingStats) error {
	// 检查影片是否已存在
	existingMovie, err := j.repo.JAVMovie().GetByCode(code)
	if err != nil && err != repository.ErrNotFound {
		return fmt.Errorf("failed to check existing movie: %w", err)
	}

	// 如果影片已存在且最近更新过，跳过
	if existingMovie != nil && j.config.SkipRecentMovies {
		if existingMovie.UpdatedAt.After(time.Now().Add(-j.config.RecentThreshold)) {
			stats.SkippedMovies++
			return nil
		}
	}

	// 执行采集
	result, err := j.scraperService.ScrapeMovieByCode(code)
	if err != nil {
		return fmt.Errorf("failed to scrape movie %s: %w", code, err)
	}

	if !result.Success {
		return fmt.Errorf("scraping failed for movie %s: %s", code, result.Error)
	}

	// 统计信息
	if existingMovie == nil {
		stats.NewMovies++
	} else {
		stats.UpdatedMovies++
	}

	if result.MovieInfo != nil {
		// JAVMovieResponse 没有直接的 Actors 和 Magnets 字段
		// 这些统计信息可以从其他地方获取
		stats.ProcessedActors += result.MovieInfo.ActorCount
		stats.ProcessedMagnets += result.MovieInfo.MagnetCount
	}

	return nil
}

// updateExistingMovies 更新现有影片数据
func (j *JAVScrapingJob) updateExistingMovies(ctx context.Context, stats *JAVScrapingStats) error {
	logger.Info("Starting to update existing movies")

	// 获取需要更新的影片（超过更新阈值的影片）
	cutoffTime := time.Now().Add(-j.config.UpdateThreshold)
	movies, err := j.repo.JAVMovie().GetByUpdateTime(cutoffTime, 20) // 限制数量
	if err != nil {
		return fmt.Errorf("failed to get movies for update: %w", err)
	}

	if len(movies) == 0 {
		logger.Info("No movies need updating")
		return nil
	}

	logger.Infof("Found %d movies to update", len(movies))

	for _, movie := range movies {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		if err := j.processMovie(ctx, movie.Code, stats); err != nil {
			logger.Errorf("Failed to update movie %s: %v", movie.Code, err)
			stats.FailedMovies++
			stats.TotalErrors++
		}

		// 更新间休息
		time.Sleep(2 * time.Second)
	}

	return nil
}

// retryFailedScrapings 重试失败的采集任务
func (j *JAVScrapingJob) retryFailedScrapings(ctx context.Context, stats *JAVScrapingStats) error {
	logger.Info("Starting to retry failed scrapings")

	// 获取失败的采集任务
	failedMovies, err := j.repo.JAVMovie().GetFailedScrapings(10) // 限制数量
	if err != nil {
		return fmt.Errorf("failed to get failed scrapings: %w", err)
	}

	if len(failedMovies) == 0 {
		logger.Info("No failed scrapings to retry")
		return nil
	}

	logger.Infof("Found %d failed scrapings to retry", len(failedMovies))

	for _, movie := range failedMovies {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		if err := j.processMovie(ctx, movie.Code, stats); err != nil {
			logger.Errorf("Failed to retry scraping for movie %s: %v", movie.Code, err)
			stats.FailedMovies++
			stats.TotalErrors++
		}

		// 重试间休息
		time.Sleep(3 * time.Second)
	}

	return nil
}

// GetName 获取任务名称
func (j *JAVScrapingJob) GetName() string {
	return "jav_scraping"
}

// GetDescription 获取任务描述
func (j *JAVScrapingJob) GetDescription() string {
	return "JAV data scraping job - collects movie metadata from multiple sources"
}

// IsEnabled 检查任务是否启用
func (j *JAVScrapingJob) IsEnabled() bool {
	return j.config.Enabled
}

// GetConfig 获取任务配置
func (j *JAVScrapingJob) GetConfig() *JAVScrapingJobConfig {
	return j.config
}

// UpdateConfig 更新任务配置
func (j *JAVScrapingJob) UpdateConfig(config *JAVScrapingJobConfig) {
	j.config = config
}