package routes

import (
	"magnet-downloader/internal/api/handler"
	"magnet-downloader/internal/api/middleware"

	"github.com/gin-gonic/gin"
)

// SetupJAVRoutes 设置JAV相关路由
func SetupJAVRoutes(rg *gin.RouterGroup, javHandler *handler.JAVHandler) {
	// JAV主路由组
	jav := rg.Group("/jav")
	{
		// 影片相关路由
		movies := jav.Group("/movies")
		{
			// 获取最新影片 - 公开接口
			movies.GET("/latest", javHandler.GetLatestMovies)
			
			// 获取热门影片 - 公开接口
			movies.GET("/popular", javHandler.GetPopularMovies)
			
			// 搜索影片 - 需要认证
			movies.GET("/search", middleware.AuthMiddleware(""), javHandler.SearchMovies)
			
			// 获取影片详情 - 需要认证
			movies.GET("/:code", middleware.AuthRequired(), javHandler.GetMovie)
			
			// 获取影片磁力链接 - 需要认证
			movies.GET("/:code/magnets", middleware.AuthRequired(), javHandler.GetMovieMagnets)
			
			// 获取最佳磁力链接 - 需要认证
			movies.GET("/:code/best-magnet", middleware.AuthRequired(), javHandler.GetBestMagnet)
		}

		// 演员相关路由
		actors := jav.Group("/actors")
		{
			// 获取热门演员 - 公开接口
			actors.GET("/popular", javHandler.GetPopularActors)
			
			// 搜索演员 - 需要认证
			actors.GET("/search", middleware.AuthRequired(), javHandler.SearchActors)
			
			// 获取演员详情 - 需要认证
			actors.GET("/:id", middleware.AuthRequired(), javHandler.GetActor)
			
			// 获取演员影片 - 需要认证
			actors.GET("/:id/movies", middleware.AuthRequired(), javHandler.GetActorMovies)
		}

		// 分类相关路由
		genres := jav.Group("/genres")
		{
			// 获取所有分类 - 公开接口
			genres.GET("", javHandler.GetAllGenres)
			
			// 获取热门分类 - 公开接口
			genres.GET("/popular", javHandler.GetPopularGenres)
			
			// 获取分类影片 - 需要认证
			genres.GET("/:id/movies", middleware.AuthRequired(), javHandler.GetGenreMovies)
		}

		// 磁力链接相关路由
		magnets := jav.Group("/magnets")
		{
			// 根据清晰度获取磁力链接 - 需要认证
			magnets.GET("/quality/:quality", middleware.AuthRequired(), javHandler.GetMagnetsByQuality)
		}

		// 数据采集相关路由 - 需要管理员权限
		scraping := jav.Group("/scraping")
		scraping.Use(middleware.AuthRequired(), middleware.AdminRequired())
		{
			// 触发单个影片采集
			scraping.POST("/trigger", javHandler.TriggerScraping)
			
			// 批量采集
			scraping.POST("/batch", javHandler.BatchScraping)
			
			// 获取采集统计
			scraping.GET("/stats", javHandler.GetScrapingStats)
		}

		// 下载相关路由 - 需要认证
		downloads := jav.Group("/downloads")
		downloads.Use(middleware.AuthRequired())
		{
			// 创建下载任务
			downloads.POST("", javHandler.CreateDownload)
			
			// 获取下载任务列表
			downloads.GET("", javHandler.ListDownloads)
		}

		// 统计相关路由
		stats := jav.Group("/stats")
		{
			// 获取JAV统计信息 - 公开接口
			stats.GET("", javHandler.GetStats)
		}
	}
}

// SetupJAVAdminRoutes 设置JAV管理员路由
func SetupJAVAdminRoutes(rg *gin.RouterGroup, javHandler *handler.JAVHandler) {
	// JAV管理路由组 - 需要管理员权限
	admin := rg.Group("/admin/jav")
	admin.Use(middleware.AuthRequired(), middleware.AdminRequired())
	{
		// 影片管理
		movies := admin.Group("/movies")
		{
			// 批量操作
			movies.POST("/batch/scrape", javHandler.BatchScraping)
		}

		// 数据采集管理
		scraping := admin.Group("/scraping")
		{
			// 获取详细采集统计
			scraping.GET("/stats/detailed", javHandler.GetScrapingStats)
		}
	}
}