package handler

import (
	"strconv"
	"strings"

	"magnet-downloader/internal/api/middleware"
	"magnet-downloader/internal/service"

	"github.com/gin-gonic/gin"
)

// JAVHandler JAV API处理器
type JAVHandler struct {
	javService        service.JAVService
	javScraperService service.JAVScraperService
	javDownloadService service.JAVDownloadService
}

// NewJAVHandler 创建JAV处理器
func NewJAVHandler(javService service.JAVService, javScraperService service.JAVScraperService, javDownloadService service.JAVDownloadService) *JAVHandler {
	return &JAVHandler{
		javService:         javService,
		javScraperService:  javScraperService,
		javDownloadService: javDownloadService,
	}
}

// ===== 影片相关API =====

// GetMovie 获取影片详情
// @Summary 获取影片详情
// @Description 根据影片番号获取详细信息，包括演员、分类、磁力链接等
// @Tags JAV
// @Produce json
// @Param code path string true "影片番号"
// @Success 200 {object} middleware.APIResponse{data=service.JAVMovieDetailsResponse}
// @Failure 400 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/movies/{code} [get]
func (h *JAVHandler) GetMovie(c *gin.Context) {
	code := strings.TrimSpace(c.Param("code"))
	if code == "" {
		middleware.BadRequestResponse(c, "影片番号不能为空", "")
		return
	}

	// 转换为大写，标准化番号格式
	code = strings.ToUpper(code)

	movie, err := h.javService.GetMovieByCode(code)
	if err != nil {
		middleware.NotFoundResponse(c, "影片未找到")
		return
	}

	// 获取影片详情
	details, err := h.javService.GetMovieDetails(movie.ID)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "获取影片详情失败", err.Error())
		return
	}

	middleware.SuccessResponse(c, details)
}

// SearchMovies 搜索影片
// @Summary 搜索影片
// @Description 根据关键词、工作室、演员等条件搜索影片
// @Tags JAV
// @Produce json
// @Param keyword query string false "搜索关键词"
// @Param studio query string false "工作室名称"
// @Param actor_name query string false "演员姓名"
// @Param genre_name query string false "分类名称"
// @Param year query int false "发布年份"
// @Param rating_min query number false "最低评分"
// @Param rating_max query number false "最高评分"
// @Param has_subtitle query bool false "是否有字幕"
// @Param sort_by query string false "排序字段" Enums(created_at,release_date,rating,duration)
// @Param sort_desc query bool false "是否降序排列"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} middleware.APIResponse{data=service.SearchMoviesResponse}
// @Failure 400 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/movies/search [get]
func (h *JAVHandler) SearchMovies(c *gin.Context) {
	var req service.SearchMoviesRequest

	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	// 参数验证
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	result, err := h.javService.SearchMovies(&req)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "搜索失败", err.Error())
		return
	}

	middleware.SuccessResponse(c, result)
}

// GetLatestMovies 获取最新影片
// @Summary 获取最新影片
// @Description 获取最新发布的影片列表
// @Tags JAV
// @Produce json
// @Param limit query int false "返回数量" default(20)
// @Success 200 {object} middleware.APIResponse{data=[]service.JAVMovieResponse}
// @Failure 400 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/movies/latest [get]
func (h *JAVHandler) GetLatestMovies(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 20
	}

	movies, err := h.javService.GetLatestMovies(limit)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "获取最新影片失败", err.Error())
		return
	}

	middleware.SuccessResponse(c, movies)
}

// GetPopularMovies 获取热门影片
// @Summary 获取热门影片
// @Description 获取热门影片列表，按评分和观看次数排序
// @Tags JAV
// @Produce json
// @Param limit query int false "返回数量" default(20)
// @Success 200 {object} middleware.APIResponse{data=[]service.JAVMovieResponse}
// @Failure 400 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/movies/popular [get]
func (h *JAVHandler) GetPopularMovies(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 20
	}

	movies, err := h.javService.GetPopularMovies(limit)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "获取热门影片失败", err.Error())
		return
	}

	middleware.SuccessResponse(c, movies)
}

// GetMovieMagnets 获取影片磁力链接
// @Summary 获取影片磁力链接
// @Description 获取指定影片的所有磁力链接，按评分排序
// @Tags JAV
// @Produce json
// @Param code path string true "影片番号"
// @Success 200 {object} middleware.APIResponse{data=[]service.JAVMagnetResponse}
// @Failure 400 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/movies/{code}/magnets [get]
func (h *JAVHandler) GetMovieMagnets(c *gin.Context) {
	code := strings.TrimSpace(c.Param("code"))
	if code == "" {
		middleware.BadRequestResponse(c, "影片番号不能为空", "")
		return
	}

	code = strings.ToUpper(code)

	movie, err := h.javService.GetMovieByCode(code)
	if err != nil {
		middleware.NotFoundResponse(c, "影片未找到")
		return
	}

	magnets, err := h.javService.GetMovieMagnets(movie.ID)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "获取磁力链接失败", err.Error())
		return
	}

	middleware.SuccessResponse(c, magnets)
}

// GetBestMagnet 获取最佳磁力链接
// @Summary 获取最佳磁力链接
// @Description 获取指定影片的最佳磁力链接（评分最高）
// @Tags JAV
// @Produce json
// @Param code path string true "影片番号"
// @Success 200 {object} middleware.APIResponse{data=service.JAVMagnetResponse}
// @Failure 400 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/movies/{code}/best-magnet [get]
func (h *JAVHandler) GetBestMagnet(c *gin.Context) {
	code := strings.TrimSpace(c.Param("code"))
	if code == "" {
		middleware.BadRequestResponse(c, "影片番号不能为空", "")
		return
	}

	code = strings.ToUpper(code)

	movie, err := h.javService.GetMovieByCode(code)
	if err != nil {
		middleware.NotFoundResponse(c, "影片未找到")
		return
	}

	magnet, err := h.javService.GetBestMagnet(movie.ID)
	if err != nil {
		middleware.NotFoundResponse(c, "未找到磁力链接")
		return
	}

	middleware.SuccessResponse(c, magnet)
}

// ===== 演员相关API =====

// GetActor 获取演员详情
// @Summary 获取演员详情
// @Description 根据演员ID获取详细信息
// @Tags JAV
// @Produce json
// @Param id path int true "演员ID"
// @Success 200 {object} middleware.APIResponse{data=service.JAVActorResponse}
// @Failure 400 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/actors/{id} [get]
func (h *JAVHandler) GetActor(c *gin.Context) {
	actorID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "无效的演员ID", err.Error())
		return
	}

	actor, err := h.javService.GetActorByID(uint(actorID))
	if err != nil {
		middleware.NotFoundResponse(c, "演员未找到")
		return
	}

	middleware.SuccessResponse(c, actor)
}

// SearchActors 搜索演员
// @Summary 搜索演员
// @Description 根据姓名、身高、血型等条件搜索演员
// @Tags JAV
// @Produce json
// @Param keyword query string false "搜索关键词"
// @Param height_min query int false "最低身高"
// @Param height_max query int false "最高身高"
// @Param blood_type query string false "血型"
// @Param debut_year query int false "出道年份"
// @Param sort_by query string false "排序字段" Enums(name,height,debut_date)
// @Param sort_desc query bool false "是否降序排列"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} middleware.APIResponse{data=service.SearchActorsResponse}
// @Failure 400 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/actors/search [get]
func (h *JAVHandler) SearchActors(c *gin.Context) {
	var req service.SearchActorsRequest

	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	// 参数验证
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	result, err := h.javService.SearchActors(&req)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "搜索失败", err.Error())
		return
	}

	middleware.SuccessResponse(c, result)
}

// GetPopularActors 获取热门演员
// @Summary 获取热门演员
// @Description 获取热门演员列表，按影片数量排序
// @Tags JAV
// @Produce json
// @Param limit query int false "返回数量" default(20)
// @Success 200 {object} middleware.APIResponse{data=[]service.JAVActorResponse}
// @Failure 400 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/actors/popular [get]
func (h *JAVHandler) GetPopularActors(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 20
	}

	actors, err := h.javService.GetPopularActors(limit)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "获取热门演员失败", err.Error())
		return
	}

	middleware.SuccessResponse(c, actors)
}

// GetActorMovies 获取演员影片
// @Summary 获取演员影片
// @Description 获取指定演员的影片列表
// @Tags JAV
// @Produce json
// @Param id path int true "演员ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} middleware.APIResponse{data=service.SearchMoviesResponse}
// @Failure 400 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/actors/{id}/movies [get]
func (h *JAVHandler) GetActorMovies(c *gin.Context) {
	actorID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "无效的演员ID", err.Error())
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize

	movies, total, err := h.javService.GetActorMovies(uint(actorID), offset, pageSize)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "获取演员影片失败", err.Error())
		return
	}

	result := &service.SearchMoviesResponse{
		Movies:   movies,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		HasMore:  int64(page*pageSize) < total,
	}

	middleware.SuccessResponse(c, result)
}

// ===== 分类相关API =====

// GetAllGenres 获取所有分类
// @Summary 获取所有分类
// @Description 获取所有影片分类列表
// @Tags JAV
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=[]service.JAVGenreResponse}
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/genres [get]
func (h *JAVHandler) GetAllGenres(c *gin.Context) {
	genres, err := h.javService.GetAllGenres()
	if err != nil {
		middleware.InternalServerErrorResponse(c, "获取分类失败", err.Error())
		return
	}

	middleware.SuccessResponse(c, genres)
}

// GetPopularGenres 获取热门分类
// @Summary 获取热门分类
// @Description 获取热门分类列表，按影片数量排序
// @Tags JAV
// @Produce json
// @Param limit query int false "返回数量" default(20)
// @Success 200 {object} middleware.APIResponse{data=[]service.JAVGenreResponse}
// @Failure 400 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/genres/popular [get]
func (h *JAVHandler) GetPopularGenres(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 20
	}

	genres, err := h.javService.GetPopularGenres(limit)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "获取热门分类失败", err.Error())
		return
	}

	middleware.SuccessResponse(c, genres)
}

// GetGenreMovies 获取分类影片
// @Summary 获取分类影片
// @Description 获取指定分类下的影片列表
// @Tags JAV
// @Produce json
// @Param id path int true "分类ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} middleware.APIResponse{data=service.SearchMoviesResponse}
// @Failure 400 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/genres/{id}/movies [get]
func (h *JAVHandler) GetGenreMovies(c *gin.Context) {
	genreID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "无效的分类ID", err.Error())
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize

	movies, total, err := h.javService.GetGenreMovies(uint(genreID), offset, pageSize)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "获取分类影片失败", err.Error())
		return
	}

	result := &service.SearchMoviesResponse{
		Movies:   movies,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		HasMore:  int64(page*pageSize) < total,
	}

	middleware.SuccessResponse(c, result)
}

// ===== 磁力链接相关API =====

// GetMagnetsByQuality 根据清晰度获取磁力链接
// @Summary 根据清晰度获取磁力链接
// @Description 获取指定清晰度的磁力链接列表
// @Tags JAV
// @Produce json
// @Param quality path string true "清晰度" Enums(720p,1080p,4K)
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} middleware.APIResponse{data=service.SearchMoviesResponse}
// @Failure 400 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/magnets/quality/{quality} [get]
func (h *JAVHandler) GetMagnetsByQuality(c *gin.Context) {
	quality := strings.TrimSpace(c.Param("quality"))
	if quality == "" {
		middleware.BadRequestResponse(c, "清晰度不能为空", "")
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize

	magnets, total, err := h.javService.GetMagnetsByQuality(quality, offset, pageSize)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "获取磁力链接失败", err.Error())
		return
	}

	result := map[string]interface{}{
		"magnets":   magnets,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
		"has_more":  int64(page*pageSize) < total,
	}

	middleware.SuccessResponse(c, result)
}

// ===== 数据采集相关API =====

// TriggerScraping 触发数据采集
// @Summary 触发数据采集
// @Description 手动触发指定影片的数据采集
// @Tags JAV
// @Accept json
// @Produce json
// @Param request body service.TriggerScrapingRequest true "采集请求"
// @Success 200 {object} middleware.APIResponse{data=service.ScrapeMovieResponse}
// @Failure 400 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/scraping/trigger [post]
func (h *JAVHandler) TriggerScraping(c *gin.Context) {
	var req struct {
		Code string `json:"code" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	code := strings.ToUpper(strings.TrimSpace(req.Code))

	result, err := h.javScraperService.ScrapeMovieByCode(code)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "采集失败", err.Error())
		return
	}

	middleware.SuccessResponse(c, result)
}

// BatchScraping 批量数据采集
// @Summary 批量数据采集
// @Description 批量采集多个影片的数据
// @Tags JAV
// @Accept json
// @Produce json
// @Param request body service.BatchScrapingRequest true "批量采集请求"
// @Success 200 {object} middleware.APIResponse{data=service.BatchScrapeResponse}
// @Failure 400 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/scraping/batch [post]
func (h *JAVHandler) BatchScraping(c *gin.Context) {
	var req struct {
		Codes []string `json:"codes" binding:"required,min=1,max=10"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	// 标准化番号格式
	for i, code := range req.Codes {
		req.Codes[i] = strings.ToUpper(strings.TrimSpace(code))
	}

	result, err := h.javScraperService.BatchScrapeMovies(req.Codes)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "批量采集失败", err.Error())
		return
	}

	middleware.SuccessResponse(c, result)
}

// ===== 下载相关API =====

// CreateDownload 创建下载任务
// @Summary 创建下载任务
// @Description 为指定影片创建下载任务
// @Tags JAV
// @Accept json
// @Produce json
// @Param request body service.CreateJAVDownloadRequest true "下载请求"
// @Success 201 {object} middleware.APIResponse{data=service.JAVDownloadResponse}
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/downloads [post]
func (h *JAVHandler) CreateDownload(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		middleware.UnauthorizedResponse(c, "用户未认证")
		return
	}

	var req service.CreateJAVDownloadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	// 标准化番号格式
	req.MovieCode = strings.ToUpper(strings.TrimSpace(req.MovieCode))

	download, err := h.javDownloadService.CreateJAVDownload(userID, &req)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "创建下载任务失败", err.Error())
		return
	}

	middleware.CreatedResponse(c, download)
}

// ListDownloads 获取下载任务列表
// @Summary 获取下载任务列表
// @Description 获取用户的JAV下载任务列表
// @Tags JAV
// @Produce json
// @Param status query string false "任务状态"
// @Param movie_code query string false "影片番号"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} middleware.APIResponse{data=[]service.JAVDownloadResponse}
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/downloads [get]
func (h *JAVHandler) ListDownloads(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		middleware.UnauthorizedResponse(c, "用户未认证")
		return
	}

	var req service.ListJAVDownloadsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	// 设置用户ID
	req.UserID = userID

	// 参数验证
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	downloads, total, err := h.javDownloadService.ListJAVDownloads(&req)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "获取下载任务失败", err.Error())
		return
	}

	result := map[string]interface{}{
		"downloads": downloads,
		"total":     total,
		"page":      req.Page,
		"page_size": req.PageSize,
		"has_more":  int64(req.Page*req.PageSize) < total,
	}

	middleware.SuccessResponse(c, result)
}

// ===== 统计相关API =====

// GetStats 获取JAV统计信息
// @Summary 获取JAV统计信息
// @Description 获取影片、演员、磁力链接等统计信息
// @Tags JAV
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=service.JAVStatsResponse}
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/stats [get]
func (h *JAVHandler) GetStats(c *gin.Context) {
	stats, err := h.javService.GetJAVStats()
	if err != nil {
		middleware.InternalServerErrorResponse(c, "获取统计信息失败", err.Error())
		return
	}

	middleware.SuccessResponse(c, stats)
}

// GetScrapingStats 获取采集统计信息
// @Summary 获取采集统计信息
// @Description 获取数据采集的统计信息
// @Tags JAV
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=service.ScrapingStatsResponse}
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/jav/scraping/stats [get]
func (h *JAVHandler) GetScrapingStats(c *gin.Context) {
	stats, err := h.javService.GetScrapingStats()
	if err != nil {
		middleware.InternalServerErrorResponse(c, "获取采集统计失败", err.Error())
		return
	}

	middleware.SuccessResponse(c, stats)
}