package repository

import (
	"fmt"
	"strings"
	"time"

	"magnet-downloader/internal/model"

	"gorm.io/gorm"
)

// javMovieRepository JAV影片仓库实现
type javMovieRepository struct {
	db *gorm.DB
}

// NewJAVMovieRepository 创建JAV影片仓库
func NewJAVMovieRepository(db *gorm.DB) JAVMovieRepository {
	return &javMovieRepository{db: db}
}

// Create 创建影片
func (r *javMovieRepository) Create(movie *model.JAVMovie) error {
	return r.db.Create(movie).Error
}

// GetByID 根据ID获取影片
func (r *javMovieRepository) GetByID(id uint) (*model.JAVMovie, error) {
	var movie model.JAVMovie
	err := r.db.First(&movie, id).Error
	if err != nil {
		return nil, err
	}
	return &movie, nil
}

// Update 更新影片
func (r *javMovieRepository) Update(movie *model.JAVMovie) error {
	return r.db.Save(movie).Error
}

// Delete 删除影片
func (r *javMovieRepository) Delete(id uint) error {
	return r.db.Delete(&model.JAVMovie{}, id).Error
}

// List 获取影片列表
func (r *javMovieRepository) List(offset, limit int, filters map[string]interface{}) ([]*model.JAVMovie, int64, error) {
	var movies []*model.JAVMovie
	var total int64

	query := r.db.Model(&model.JAVMovie{})

	// 应用过滤条件
	for key, value := range filters {
		switch key {
		case "status":
			query = query.Where("scraping_status = ?", value)
		case "source":
			query = query.Where("scraping_source = ?", value)
		case "studio":
			query = query.Where("studio LIKE ?", "%"+fmt.Sprintf("%v", value)+"%")
		case "year":
			query = query.Where("EXTRACT(YEAR FROM release_date) = ?", value)
		case "has_subtitle":
			query = query.Where("has_subtitle = ?", value)
		case "rating_min":
			query = query.Where("rating >= ?", value)
		case "rating_max":
			query = query.Where("rating <= ?", value)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	err := query.Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&movies).Error

	if err != nil {
		return nil, 0, err
	}

	return movies, total, nil
}

// GetByCode 根据番号获取影片
func (r *javMovieRepository) GetByCode(code string) (*model.JAVMovie, error) {
	var movie model.JAVMovie
	err := r.db.Where("code = ?", strings.ToUpper(code)).First(&movie).Error
	if err != nil {
		return nil, err
	}
	return &movie, nil
}

// Search 搜索影片
func (r *javMovieRepository) Search(keyword string, offset, limit int) ([]*model.JAVMovie, int64, error) {
	var movies []*model.JAVMovie
	var total int64

	keyword = strings.TrimSpace(keyword)
	if keyword == "" {
		return r.List(offset, limit, nil)
	}

	// 构建搜索查询
	searchPattern := "%" + keyword + "%"
	query := r.db.Model(&model.JAVMovie{}).Where(
		"code LIKE ? OR title LIKE ? OR title_en LIKE ? OR studio LIKE ?",
		searchPattern, searchPattern, searchPattern, searchPattern,
	)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据，按相关性排序
	err := query.Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&movies).Error

	if err != nil {
		return nil, 0, err
	}

	return movies, total, nil
}

// GetByStudio 根据制作公司获取影片
func (r *javMovieRepository) GetByStudio(studio string, offset, limit int) ([]*model.JAVMovie, int64, error) {
	var movies []*model.JAVMovie
	var total int64

	query := r.db.Model(&model.JAVMovie{}).Where("studio LIKE ?", "%"+studio+"%")

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	err := query.Order("release_date DESC").
		Offset(offset).
		Limit(limit).
		Find(&movies).Error

	if err != nil {
		return nil, 0, err
	}

	return movies, total, nil
}

// GetByActor 根据演员获取影片
func (r *javMovieRepository) GetByActor(actorID uint, offset, limit int) ([]*model.JAVMovie, int64, error) {
	var movies []*model.JAVMovie
	var total int64

	// 通过关联表查询
	query := r.db.Model(&model.JAVMovie{}).
		Joins("JOIN jav_movie_actors ON jav_movies.id = jav_movie_actors.movie_id").
		Where("jav_movie_actors.actor_id = ?", actorID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	err := query.Order("jav_movies.release_date DESC").
		Offset(offset).
		Limit(limit).
		Find(&movies).Error

	if err != nil {
		return nil, 0, err
	}

	return movies, total, nil
}

// GetByGenre 根据分类获取影片
func (r *javMovieRepository) GetByGenre(genreID uint, offset, limit int) ([]*model.JAVMovie, int64, error) {
	var movies []*model.JAVMovie
	var total int64

	// 通过关联表查询
	query := r.db.Model(&model.JAVMovie{}).
		Joins("JOIN jav_movie_genres ON jav_movies.id = jav_movie_genres.movie_id").
		Where("jav_movie_genres.genre_id = ?", genreID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	err := query.Order("jav_movies.release_date DESC").
		Offset(offset).
		Limit(limit).
		Find(&movies).Error

	if err != nil {
		return nil, 0, err
	}

	return movies, total, nil
}

// CountByStatus 根据状态统计影片数量
func (r *javMovieRepository) CountByStatus(status model.JAVScrapingStatus) (int64, error) {
	var count int64
	err := r.db.Model(&model.JAVMovie{}).Where("scraping_status = ?", status).Count(&count).Error
	return count, err
}

// CountBySource 根据数据源统计影片数量
func (r *javMovieRepository) CountBySource(source model.JAVScrapingSource) (int64, error) {
	var count int64
	err := r.db.Model(&model.JAVMovie{}).Where("scraping_source = ?", source).Count(&count).Error
	return count, err
}

// GetLatestMovies 获取最新影片
func (r *javMovieRepository) GetLatestMovies(limit int) ([]*model.JAVMovie, error) {
	var movies []*model.JAVMovie
	err := r.db.Where("scraping_status = ?", model.JAVScrapingStatusCompleted).
		Order("created_at DESC").
		Limit(limit).
		Find(&movies).Error
	return movies, err
}

// GetPopularMovies 获取热门影片（按评分排序）
func (r *javMovieRepository) GetPopularMovies(limit int) ([]*model.JAVMovie, error) {
	var movies []*model.JAVMovie
	err := r.db.Where("scraping_status = ? AND rating > 0", model.JAVScrapingStatusCompleted).
		Order("rating DESC, view_count DESC").
		Limit(limit).
		Find(&movies).Error
	return movies, err
}

// BatchUpdateStatus 批量更新状态
func (r *javMovieRepository) BatchUpdateStatus(ids []uint, status model.JAVScrapingStatus) error {
	return r.db.Model(&model.JAVMovie{}).
		Where("id IN ?", ids).
		Update("scraping_status", status).Error
}

// BatchDelete 批量删除
func (r *javMovieRepository) BatchDelete(ids []uint) error {
	return r.db.Delete(&model.JAVMovie{}, ids).Error
}

// GetWithActors 获取影片及其演员信息
func (r *javMovieRepository) GetWithActors(id uint) (*model.JAVMovie, error) {
	var movie model.JAVMovie
	err := r.db.Preload("Actors").First(&movie, id).Error
	if err != nil {
		return nil, err
	}
	return &movie, nil
}

// GetWithGenres 获取影片及其分类信息
func (r *javMovieRepository) GetWithGenres(id uint) (*model.JAVMovie, error) {
	var movie model.JAVMovie
	err := r.db.Preload("Genres").First(&movie, id).Error
	if err != nil {
		return nil, err
	}
	return &movie, nil
}

// GetWithMagnets 获取影片及其磁力链接
func (r *javMovieRepository) GetWithMagnets(id uint) (*model.JAVMovie, error) {
	var movie model.JAVMovie
	err := r.db.Preload("Magnets", func(db *gorm.DB) *gorm.DB {
		return db.Order("score DESC, file_size DESC")
	}).First(&movie, id).Error
	if err != nil {
		return nil, err
	}
	return &movie, nil
}

// GetWithAll 获取影片及其所有关联信息
func (r *javMovieRepository) GetWithAll(id uint) (*model.JAVMovie, error) {
	var movie model.JAVMovie
	err := r.db.Preload("Actors").
		Preload("Genres").
		Preload("Magnets", func(db *gorm.DB) *gorm.DB {
			return db.Order("score DESC, file_size DESC")
		}).
		First(&movie, id).Error
	if err != nil {
		return nil, err
	}
	return &movie, nil
}

// GetByScrapingStatusAndTime 根据采集状态和时间获取影片
func (r *javMovieRepository) GetByScrapingStatusAndTime(status model.JAVScrapingStatus, before time.Time) ([]*model.JAVMovie, error) {
	var movies []*model.JAVMovie
	err := r.db.Where("scraping_status = ? AND updated_at < ?", status, before).
		Order("updated_at ASC").
		Find(&movies).Error
	return movies, err
}

// GetByScrapingStatus 根据采集状态获取影片
func (r *javMovieRepository) GetByScrapingStatus(status model.JAVScrapingStatus, limit int) ([]*model.JAVMovie, error) {
	var movies []*model.JAVMovie
	err := r.db.Where("scraping_status = ?", status).
		Order("updated_at ASC").
		Limit(limit).
		Find(&movies).Error
	return movies, err
}

// GetByUpdateTime 根据更新时间获取影片
func (r *javMovieRepository) GetByUpdateTime(before time.Time, limit int) ([]*model.JAVMovie, error) {
	var movies []*model.JAVMovie
	err := r.db.Where("updated_at < ?", before).
		Order("updated_at ASC").
		Limit(limit).
		Find(&movies).Error
	return movies, err
}

// GetFailedScrapings 获取采集失败的影片
func (r *javMovieRepository) GetFailedScrapings(limit int) ([]*model.JAVMovie, error) {
	var movies []*model.JAVMovie
	err := r.db.Where("scraping_status = ?", model.JAVScrapingStatusFailed).
		Order("updated_at ASC").
		Limit(limit).
		Find(&movies).Error
	return movies, err
}