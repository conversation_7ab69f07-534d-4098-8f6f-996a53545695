package repository

import (
	"gorm.io/gorm"
)

// repository 仓库管理器实现
type repository struct {
	db              *gorm.DB
	taskRepo        TaskRepository
	userRepo        UserRepository
	configRepo      ConfigRepository
	javMovieRepo    JAVMovieRepository
	javActorRepo    JAVActorRepository
	javMagnetRepo   JAVMagnetRepository
	javGenreRepo    JAVGenreRepository
}

// NewRepository 创建仓库管理器
func NewRepository(db *gorm.DB) Repository {
	return &repository{
		db:            db,
		taskRepo:      NewTaskRepository(db),
		userRepo:      NewUserRepository(db),
		configRepo:    NewConfigRepository(db),
		javMovieRepo:  NewJAVMovieRepository(db),
		javActorRepo:  NewJAVActorRepository(db),
		javMagnetRepo: NewJAVMagnetRepository(db),
		javGenreRepo:  NewJAVGenreRepository(db),
	}
}

// Task 获取任务仓库
func (r *repository) Task() TaskRepository {
	return r.taskRepo
}

// User 获取用户仓库
func (r *repository) User() UserRepository {
	return r.userRepo
}

// Config 获取配置仓库
func (r *repository) Config() ConfigRepository {
	return r.configRepo
}

// JAVMovie 获取JAV影片仓库
func (r *repository) JAVMovie() JAVMovieRepository {
	return r.javMovieRepo
}

// JAVActor 获取JAV演员仓库
func (r *repository) JAVActor() JAVActorRepository {
	return r.javActorRepo
}

// JAVMagnet 获取JAV磁力链接仓库
func (r *repository) JAVMagnet() JAVMagnetRepository {
	return r.javMagnetRepo
}

// JAVGenre 获取JAV分类仓库
func (r *repository) JAVGenre() JAVGenreRepository {
	return r.javGenreRepo
}

// Transaction 执行事务
func (r *repository) Transaction(fn func(Repository) error) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 创建事务仓库
		txRepo := &repository{
			db:            tx,
			taskRepo:      NewTaskRepository(tx),
			userRepo:      NewUserRepository(tx),
			configRepo:    NewConfigRepository(tx),
			javMovieRepo:  NewJAVMovieRepository(tx),
			javActorRepo:  NewJAVActorRepository(tx),
			javMagnetRepo: NewJAVMagnetRepository(tx),
			javGenreRepo:  NewJAVGenreRepository(tx),
		}
		return fn(txRepo)
	})
}

// Health 健康检查
func (r *repository) Health() bool {
	sqlDB, err := r.db.DB()
	if err != nil {
		return false
	}
	return sqlDB.Ping() == nil
}

// GetDB 获取数据库实例
func (r *repository) GetDB() *gorm.DB {
	return r.db
}
