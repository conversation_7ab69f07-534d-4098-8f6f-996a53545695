package repository

import (
	"errors"
	"magnet-downloader/internal/model"
	"time"

	"gorm.io/gorm"
)

// 常用错误定义
var (
	ErrNotFound = errors.New("record not found")
)

// TaskRepository 任务仓库接口
type TaskRepository interface {
	// 基础CRUD操作
	Create(task *model.DownloadTask) error
	GetByID(id uint) (*model.DownloadTask, error)
	Update(task *model.DownloadTask) error
	Delete(id uint) error

	// 查询操作
	List(offset, limit int, filters map[string]interface{}) ([]*model.DownloadTask, int64, error)
	GetByUserID(userID uint, offset, limit int) ([]*model.DownloadTask, int64, error)
	GetByStatus(status model.TaskStatus, offset, limit int) ([]*model.DownloadTask, int64, error)
	GetByAria2GID(gid string) (*model.DownloadTask, error)

	// 统计操作
	CountByStatus(status model.TaskStatus) (int64, error)
	CountByUserID(userID uint) (int64, error)
	GetActiveTasksByUser(userID uint) ([]*model.DownloadTask, error)

	// 批量操作
	BatchUpdateStatus(ids []uint, status model.TaskStatus) error
	BatchDelete(ids []uint) error

	// 清理操作
	CleanCompletedTasks(days int) error
	GetByStatusAndTime(status model.TaskStatus, before time.Time) ([]*model.DownloadTask, error)
}

// UserRepository 用户仓库接口
type UserRepository interface {
	// 基础CRUD操作
	Create(user *model.User) error
	GetByID(id uint) (*model.User, error)
	Update(user *model.User) error
	Delete(id uint) error

	// 查询操作
	List(offset, limit int, filters map[string]interface{}) ([]*model.User, int64, error)
	GetByUsername(username string) (*model.User, error)
	GetByEmail(email string) (*model.User, error)

	// 认证相关
	Authenticate(username, password string) (*model.User, error)
	UpdateLoginInfo(userID uint) error

	// 统计操作
	CountByRole(role model.UserRole) (int64, error)
	CountByStatus(status model.UserStatus) (int64, error)

	// 批量操作
	BatchUpdateStatus(ids []uint, status model.UserStatus) error
}

// ConfigRepository 配置仓库接口
type ConfigRepository interface {
	// 基础CRUD操作
	Create(config *model.SystemConfig) error
	GetByID(id uint) (*model.SystemConfig, error)
	Update(config *model.SystemConfig) error
	Delete(id uint) error

	// 查询操作
	List(offset, limit int, filters map[string]interface{}) ([]*model.SystemConfig, int64, error)
	GetByKey(key string) (*model.SystemConfig, error)
	GetByCategory(category string) ([]*model.SystemConfig, error)
	GetPublicConfigs() ([]*model.SystemConfig, error)

	// 批量操作
	BatchUpdate(configs []*model.SystemConfig) error
	BatchDelete(keys []string) error

	// 缓存操作
	GetCachedValue(key string) (interface{}, bool)
	SetCachedValue(key string, value interface{})
	ClearCache()
}

// JAVMovieRepository JAV影片仓库接口
type JAVMovieRepository interface {
	// 基础CRUD操作
	Create(movie *model.JAVMovie) error
	GetByID(id uint) (*model.JAVMovie, error)
	Update(movie *model.JAVMovie) error
	Delete(id uint) error

	// 查询操作
	List(offset, limit int, filters map[string]interface{}) ([]*model.JAVMovie, int64, error)
	GetByCode(code string) (*model.JAVMovie, error)
	Search(keyword string, offset, limit int) ([]*model.JAVMovie, int64, error)
	GetByStudio(studio string, offset, limit int) ([]*model.JAVMovie, int64, error)
	GetByActor(actorID uint, offset, limit int) ([]*model.JAVMovie, int64, error)
	GetByGenre(genreID uint, offset, limit int) ([]*model.JAVMovie, int64, error)

	// 统计操作
	CountByStatus(status model.JAVScrapingStatus) (int64, error)
	CountBySource(source model.JAVScrapingSource) (int64, error)
	GetLatestMovies(limit int) ([]*model.JAVMovie, error)
	GetPopularMovies(limit int) ([]*model.JAVMovie, error)
	
	// 维护操作
	GetByScrapingStatusAndTime(status model.JAVScrapingStatus, before time.Time) ([]*model.JAVMovie, error)
	GetByScrapingStatus(status model.JAVScrapingStatus, limit int) ([]*model.JAVMovie, error)
	GetByUpdateTime(before time.Time, limit int) ([]*model.JAVMovie, error)
	GetFailedScrapings(limit int) ([]*model.JAVMovie, error)

	// 批量操作
	BatchUpdateStatus(ids []uint, status model.JAVScrapingStatus) error
	BatchDelete(ids []uint) error

	// 关联操作
	GetWithActors(id uint) (*model.JAVMovie, error)
	GetWithGenres(id uint) (*model.JAVMovie, error)
	GetWithMagnets(id uint) (*model.JAVMovie, error)
	GetWithAll(id uint) (*model.JAVMovie, error)
}

// JAVActorRepository JAV演员仓库接口
type JAVActorRepository interface {
	// 基础CRUD操作
	Create(actor *model.JAVActor) error
	GetByID(id uint) (*model.JAVActor, error)
	Update(actor *model.JAVActor) error
	Delete(id uint) error

	// 查询操作
	List(offset, limit int, filters map[string]interface{}) ([]*model.JAVActor, int64, error)
	GetByName(name string) (*model.JAVActor, error)
	Search(keyword string, offset, limit int) ([]*model.JAVActor, int64, error)
	GetByNameEn(nameEn string) (*model.JAVActor, error)

	// 统计操作
	CountByGender(gender string) (int64, error)
	GetPopularActors(limit int) ([]*model.JAVActor, error)

	// 关联操作
	GetWithMovies(id uint) (*model.JAVActor, error)
	GetMoviesByActor(actorID uint, offset, limit int) ([]*model.JAVMovie, int64, error)

	// 批量操作
	BatchCreate(actors []*model.JAVActor) error
	BatchUpdate(actors []*model.JAVActor) error
	
	// 维护操作
	GetOrphanActors() ([]*model.JAVActor, error)
}

// JAVMagnetRepository JAV磁力链接仓库接口
type JAVMagnetRepository interface {
	// 基础CRUD操作
	Create(magnet *model.JAVMagnet) error
	GetByID(id uint) (*model.JAVMagnet, error)
	Update(magnet *model.JAVMagnet) error
	Delete(id uint) error

	// 查询操作
	List(offset, limit int, filters map[string]interface{}) ([]*model.JAVMagnet, int64, error)
	GetByMovieID(movieID uint) ([]*model.JAVMagnet, error)
	GetByMagnetURL(magnetURL string) (*model.JAVMagnet, error)
	GetBestMagnetByMovie(movieID uint) (*model.JAVMagnet, error)

	// 筛选操作
	GetByQuality(quality string, offset, limit int) ([]*model.JAVMagnet, int64, error)
	GetBySubtitle(hasSubtitle bool, offset, limit int) ([]*model.JAVMagnet, int64, error)
	GetByScore(minScore float64, offset, limit int) ([]*model.JAVMagnet, int64, error)

	// 统计操作
	CountByMovieID(movieID uint) (int64, error)
	CountByQuality(quality string) (int64, error)
	GetAverageScoreByMovie(movieID uint) (float64, error)

	// 批量操作
	BatchCreate(magnets []*model.JAVMagnet) error
	BatchUpdateScore(ids []uint, score float64) error
	BatchDelete(ids []uint) error

	// 清理操作
	CleanInvalidMagnets() error
	CleanDuplicateMagnets() error
	
	// 维护操作
	GetByScoreRange(minScore, maxScore float64) ([]*model.JAVMagnet, error)
	GetForScoreUpdate(before time.Time, limit int) ([]*model.JAVMagnet, error)
}

// JAVGenreRepository JAV分类仓库接口
type JAVGenreRepository interface {
	// 基础CRUD操作
	Create(genre *model.JAVGenre) error
	GetByID(id uint) (*model.JAVGenre, error)
	Update(genre *model.JAVGenre) error
	Delete(id uint) error

	// 查询操作
	List(offset, limit int, filters map[string]interface{}) ([]*model.JAVGenre, int64, error)
	GetByName(name string) (*model.JAVGenre, error)
	GetByNameEn(nameEn string) (*model.JAVGenre, error)
	Search(keyword string, offset, limit int) ([]*model.JAVGenre, int64, error)

	// 统计操作
	GetPopularGenres(limit int) ([]*model.JAVGenre, error)
	CountMoviesByGenre(genreID uint) (int64, error)

	// 关联操作
	GetWithMovies(id uint) (*model.JAVGenre, error)
	GetMoviesByGenre(genreID uint, offset, limit int) ([]*model.JAVMovie, int64, error)

	// 批量操作
	BatchCreate(genres []*model.JAVGenre) error
}

// Repository 仓库集合接口
type Repository interface {
	Task() TaskRepository
	User() UserRepository
	Config() ConfigRepository
	JAVMovie() JAVMovieRepository
	JAVActor() JAVActorRepository
	JAVMagnet() JAVMagnetRepository
	JAVGenre() JAVGenreRepository

	// 事务支持
	Transaction(fn func(Repository) error) error

	// 健康检查
	Health() bool

	// 获取数据库实例
	GetDB() *gorm.DB
}
