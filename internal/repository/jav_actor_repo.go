package repository

import (
	"strings"

	"magnet-downloader/internal/model"

	"gorm.io/gorm"
)

// javActorRepository JAV演员仓库实现
type javActorRepository struct {
	db *gorm.DB
}

// NewJAVActorRepository 创建JAV演员仓库
func NewJAVActorRepository(db *gorm.DB) JAVActorRepository {
	return &javActorRepository{db: db}
}

// Create 创建演员
func (r *javActorRepository) Create(actor *model.JAVActor) error {
	return r.db.Create(actor).Error
}

// GetByID 根据ID获取演员
func (r *javActorRepository) GetByID(id uint) (*model.JAVActor, error) {
	var actor model.JAVActor
	err := r.db.First(&actor, id).Error
	if err != nil {
		return nil, err
	}
	return &actor, nil
}

// Update 更新演员
func (r *javActorRepository) Update(actor *model.JAVActor) error {
	return r.db.Save(actor).Error
}

// Delete 删除演员
func (r *javActorRepository) Delete(id uint) error {
	return r.db.Delete(&model.JAVActor{}, id).Error
}

// List 获取演员列表
func (r *javActorRepository) List(offset, limit int, filters map[string]interface{}) ([]*model.JAVActor, int64, error) {
	var actors []*model.JAVActor
	var total int64

	query := r.db.Model(&model.JAVActor{})

	// 应用过滤条件
	for key, value := range filters {
		switch key {
		case "gender":
			query = query.Where("gender = ?", value)
		case "nationality":
			query = query.Where("nationality = ?", value)
		case "age_min":
			query = query.Where("age >= ?", value)
		case "age_max":
			query = query.Where("age <= ?", value)
		case "height_min":
			query = query.Where("height >= ?", value)
		case "height_max":
			query = query.Where("height <= ?", value)
		case "debut_year":
			query = query.Where("EXTRACT(YEAR FROM debut_date) = ?", value)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	err := query.Order("name ASC").
		Offset(offset).
		Limit(limit).
		Find(&actors).Error

	if err != nil {
		return nil, 0, err
	}

	return actors, total, nil
}

// GetByName 根据姓名获取演员
func (r *javActorRepository) GetByName(name string) (*model.JAVActor, error) {
	var actor model.JAVActor
	err := r.db.Where("name = ?", name).First(&actor).Error
	if err != nil {
		return nil, err
	}
	return &actor, nil
}

// Search 搜索演员
func (r *javActorRepository) Search(keyword string, offset, limit int) ([]*model.JAVActor, int64, error) {
	var actors []*model.JAVActor
	var total int64

	keyword = strings.TrimSpace(keyword)
	if keyword == "" {
		return r.List(offset, limit, nil)
	}

	// 构建搜索查询
	searchPattern := "%" + keyword + "%"
	query := r.db.Model(&model.JAVActor{}).Where(
		"name LIKE ? OR name_en LIKE ? OR name_jp LIKE ?",
		searchPattern, searchPattern, searchPattern,
	)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据，按相关性排序
	err := query.Order("name ASC").
		Offset(offset).
		Limit(limit).
		Find(&actors).Error

	if err != nil {
		return nil, 0, err
	}

	return actors, total, nil
}

// GetByNameEn 根据英文姓名获取演员
func (r *javActorRepository) GetByNameEn(nameEn string) (*model.JAVActor, error) {
	var actor model.JAVActor
	err := r.db.Where("name_en = ?", nameEn).First(&actor).Error
	if err != nil {
		return nil, err
	}
	return &actor, nil
}

// CountByGender 根据性别统计演员数量
func (r *javActorRepository) CountByGender(gender string) (int64, error) {
	var count int64
	err := r.db.Model(&model.JAVActor{}).Where("gender = ?", gender).Count(&count).Error
	return count, err
}

// GetPopularActors 获取热门演员（按影片数量排序）
func (r *javActorRepository) GetPopularActors(limit int) ([]*model.JAVActor, error) {
	var actors []*model.JAVActor
	
	// 使用子查询统计每个演员的影片数量
	err := r.db.Model(&model.JAVActor{}).
		Select("jav_actors.*, COUNT(jav_movie_actors.movie_id) as movie_count").
		Joins("LEFT JOIN jav_movie_actors ON jav_actors.id = jav_movie_actors.actor_id").
		Group("jav_actors.id").
		Order("movie_count DESC, jav_actors.name ASC").
		Limit(limit).
		Find(&actors).Error
	
	return actors, err
}

// GetWithMovies 获取演员及其影片信息
func (r *javActorRepository) GetWithMovies(id uint) (*model.JAVActor, error) {
	var actor model.JAVActor
	err := r.db.Preload("Movies", func(db *gorm.DB) *gorm.DB {
		return db.Order("release_date DESC")
	}).First(&actor, id).Error
	if err != nil {
		return nil, err
	}
	return &actor, nil
}

// GetMoviesByActor 获取演员的影片列表
func (r *javActorRepository) GetMoviesByActor(actorID uint, offset, limit int) ([]*model.JAVMovie, int64, error) {
	var movies []*model.JAVMovie
	var total int64

	// 通过关联表查询
	query := r.db.Model(&model.JAVMovie{}).
		Joins("JOIN jav_movie_actors ON jav_movies.id = jav_movie_actors.movie_id").
		Where("jav_movie_actors.actor_id = ?", actorID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	err := query.Order("jav_movies.release_date DESC").
		Offset(offset).
		Limit(limit).
		Find(&movies).Error

	if err != nil {
		return nil, 0, err
	}

	return movies, total, nil
}

// BatchCreate 批量创建演员
func (r *javActorRepository) BatchCreate(actors []*model.JAVActor) error {
	if len(actors) == 0 {
		return nil
	}
	
	// 使用批量插入，提高性能
	return r.db.CreateInBatches(actors, 100).Error
}

// BatchUpdate 批量更新演员
func (r *javActorRepository) BatchUpdate(actors []*model.JAVActor) error {
	if len(actors) == 0 {
		return nil
	}

	// 在事务中执行批量更新
	return r.db.Transaction(func(tx *gorm.DB) error {
		for _, actor := range actors {
			if err := tx.Save(actor).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// GetOrphanActors 获取孤立的演员（没有关联影片的演员）
func (r *javActorRepository) GetOrphanActors() ([]*model.JAVActor, error) {
	var actors []*model.JAVActor
	err := r.db.Where("id NOT IN (SELECT DISTINCT actor_id FROM jav_movie_actors)").
		Find(&actors).Error
	return actors, err
}