package repository

import (
	"strings"

	"magnet-downloader/internal/model"

	"gorm.io/gorm"
)

// javGenreRepository JAV分类仓库实现
type javGenreRepository struct {
	db *gorm.DB
}

// NewJAVGenreRepository 创建JAV分类仓库
func NewJAVGenreRepository(db *gorm.DB) JAVGenreRepository {
	return &javGenreRepository{db: db}
}

// Create 创建分类
func (r *javGenreRepository) Create(genre *model.JAVGenre) error {
	return r.db.Create(genre).Error
}

// GetByID 根据ID获取分类
func (r *javGenreRepository) GetByID(id uint) (*model.JAVGenre, error) {
	var genre model.JAVGenre
	err := r.db.First(&genre, id).Error
	if err != nil {
		return nil, err
	}
	return &genre, nil
}

// Update 更新分类
func (r *javGenreRepository) Update(genre *model.JAVGenre) error {
	return r.db.Save(genre).Error
}

// Delete 删除分类
func (r *javGenreRepository) Delete(id uint) error {
	return r.db.Delete(&model.JAVGenre{}, id).Error
}

// List 获取分类列表
func (r *javGenreRepository) List(offset, limit int, filters map[string]interface{}) ([]*model.JAVGenre, int64, error) {
	var genres []*model.JAVGenre
	var total int64

	query := r.db.Model(&model.JAVGenre{})

	// 应用过滤条件
	for key, value := range filters {
		switch key {
		case "category":
			query = query.Where("category = ?", value)
		case "is_popular":
			if value.(bool) {
				// 根据影片数量判断是否热门
				query = query.Where("movie_count > 0")
			}
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	err := query.Order("name ASC").
		Offset(offset).
		Limit(limit).
		Find(&genres).Error

	if err != nil {
		return nil, 0, err
	}

	return genres, total, nil
}

// GetByName 根据名称获取分类
func (r *javGenreRepository) GetByName(name string) (*model.JAVGenre, error) {
	var genre model.JAVGenre
	err := r.db.Where("name = ?", name).First(&genre).Error
	if err != nil {
		return nil, err
	}
	return &genre, nil
}

// GetByNameEn 根据英文名称获取分类
func (r *javGenreRepository) GetByNameEn(nameEn string) (*model.JAVGenre, error) {
	var genre model.JAVGenre
	err := r.db.Where("name_en = ?", nameEn).First(&genre).Error
	if err != nil {
		return nil, err
	}
	return &genre, nil
}

// Search 搜索分类
func (r *javGenreRepository) Search(keyword string, offset, limit int) ([]*model.JAVGenre, int64, error) {
	var genres []*model.JAVGenre
	var total int64

	keyword = strings.TrimSpace(keyword)
	if keyword == "" {
		return r.List(offset, limit, nil)
	}

	// 构建搜索查询
	searchPattern := "%" + keyword + "%"
	query := r.db.Model(&model.JAVGenre{}).Where(
		"name LIKE ? OR name_en LIKE ? OR name_jp LIKE ? OR description LIKE ?",
		searchPattern, searchPattern, searchPattern, searchPattern,
	)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据，按相关性排序
	err := query.Order("name ASC").
		Offset(offset).
		Limit(limit).
		Find(&genres).Error

	if err != nil {
		return nil, 0, err
	}

	return genres, total, nil
}

// GetPopularGenres 获取热门分类（按影片数量排序）
func (r *javGenreRepository) GetPopularGenres(limit int) ([]*model.JAVGenre, error) {
	var genres []*model.JAVGenre
	
	// 使用子查询统计每个分类的影片数量
	err := r.db.Model(&model.JAVGenre{}).
		Select("jav_genres.*, COUNT(jav_movie_genres.movie_id) as movie_count").
		Joins("LEFT JOIN jav_movie_genres ON jav_genres.id = jav_movie_genres.genre_id").
		Group("jav_genres.id").
		Order("movie_count DESC, jav_genres.name ASC").
		Limit(limit).
		Find(&genres).Error
	
	return genres, err
}

// CountMoviesByGenre 统计分类下的影片数量
func (r *javGenreRepository) CountMoviesByGenre(genreID uint) (int64, error) {
	var count int64
	err := r.db.Model(&model.JAVMovie{}).
		Joins("JOIN jav_movie_genres ON jav_movies.id = jav_movie_genres.movie_id").
		Where("jav_movie_genres.genre_id = ?", genreID).
		Count(&count).Error
	return count, err
}

// GetWithMovies 获取分类及其影片信息
func (r *javGenreRepository) GetWithMovies(id uint) (*model.JAVGenre, error) {
	var genre model.JAVGenre
	err := r.db.Preload("Movies", func(db *gorm.DB) *gorm.DB {
		return db.Order("release_date DESC")
	}).First(&genre, id).Error
	if err != nil {
		return nil, err
	}
	return &genre, nil
}

// GetMoviesByGenre 获取分类下的影片列表
func (r *javGenreRepository) GetMoviesByGenre(genreID uint, offset, limit int) ([]*model.JAVMovie, int64, error) {
	var movies []*model.JAVMovie
	var total int64

	// 通过关联表查询
	query := r.db.Model(&model.JAVMovie{}).
		Joins("JOIN jav_movie_genres ON jav_movies.id = jav_movie_genres.movie_id").
		Where("jav_movie_genres.genre_id = ?", genreID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	err := query.Order("jav_movies.release_date DESC").
		Offset(offset).
		Limit(limit).
		Find(&movies).Error

	if err != nil {
		return nil, 0, err
	}

	return movies, total, nil
}

// BatchCreate 批量创建分类
func (r *javGenreRepository) BatchCreate(genres []*model.JAVGenre) error {
	if len(genres) == 0 {
		return nil
	}
	
	// 使用批量插入，提高性能
	return r.db.CreateInBatches(genres, 100).Error
}