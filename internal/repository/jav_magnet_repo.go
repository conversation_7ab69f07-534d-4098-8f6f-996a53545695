package repository

import (
	"fmt"
	"time"

	"magnet-downloader/internal/model"

	"gorm.io/gorm"
)

// javMagnetRepository JAV磁力链接仓库实现
type javMagnetRepository struct {
	db *gorm.DB
}

// NewJAVMagnetRepository 创建JAV磁力链接仓库
func NewJAVMagnetRepository(db *gorm.DB) JAVMagnetRepository {
	return &javMagnetRepository{db: db}
}

// Create 创建磁力链接
func (r *javMagnetRepository) Create(magnet *model.JAVMagnet) error {
	return r.db.Create(magnet).Error
}

// GetByID 根据ID获取磁力链接
func (r *javMagnetRepository) GetByID(id uint) (*model.JAVMagnet, error) {
	var magnet model.JAVMagnet
	err := r.db.First(&magnet, id).Error
	if err != nil {
		return nil, err
	}
	return &magnet, nil
}

// Update 更新磁力链接
func (r *javMagnetRepository) Update(magnet *model.JAVMagnet) error {
	return r.db.Save(magnet).Error
}

// Delete 删除磁力链接
func (r *javMagnetRepository) Delete(id uint) error {
	return r.db.Delete(&model.JAVMagnet{}, id).Error
}

// List 获取磁力链接列表
func (r *javMagnetRepository) List(offset, limit int, filters map[string]interface{}) ([]*model.JAVMagnet, int64, error) {
	var magnets []*model.JAVMagnet
	var total int64

	query := r.db.Model(&model.JAVMagnet{})

	// 应用过滤条件
	for key, value := range filters {
		switch key {
		case "movie_id":
			query = query.Where("movie_id = ?", value)
		case "quality":
			query = query.Where("quality = ?", value)
		case "has_subtitle":
			query = query.Where("has_subtitle = ?", value)
		case "subtitle_language":
			query = query.Where("subtitle_language = ?", value)
		case "source":
			query = query.Where("source LIKE ?", "%"+fmt.Sprintf("%v", value)+"%")
		case "min_size":
			query = query.Where("file_size >= ?", value)
		case "max_size":
			query = query.Where("file_size <= ?", value)
		case "min_score":
			query = query.Where("score >= ?", value)
		case "min_seeders":
			query = query.Where("seeders >= ?", value)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据，按评分和文件大小排序
	err := query.Order("score DESC, file_size DESC").
		Offset(offset).
		Limit(limit).
		Find(&magnets).Error

	if err != nil {
		return nil, 0, err
	}

	return magnets, total, nil
}

// GetByMovieID 根据影片ID获取磁力链接
func (r *javMagnetRepository) GetByMovieID(movieID uint) ([]*model.JAVMagnet, error) {
	var magnets []*model.JAVMagnet
	err := r.db.Where("movie_id = ?", movieID).
		Order("score DESC, file_size DESC").
		Find(&magnets).Error
	return magnets, err
}

// GetByMagnetURL 根据磁力链接URL获取记录
func (r *javMagnetRepository) GetByMagnetURL(magnetURL string) (*model.JAVMagnet, error) {
	var magnet model.JAVMagnet
	err := r.db.Where("magnet_url = ?", magnetURL).First(&magnet).Error
	if err != nil {
		return nil, err
	}
	return &magnet, nil
}

// GetBestMagnetByMovie 获取影片的最佳磁力链接
func (r *javMagnetRepository) GetBestMagnetByMovie(movieID uint) (*model.JAVMagnet, error) {
	var magnet model.JAVMagnet
	err := r.db.Where("movie_id = ?", movieID).
		Order("score DESC, file_size DESC").
		First(&magnet).Error
	if err != nil {
		return nil, err
	}
	return &magnet, nil
}

// GetByQuality 根据清晰度获取磁力链接
func (r *javMagnetRepository) GetByQuality(quality string, offset, limit int) ([]*model.JAVMagnet, int64, error) {
	var magnets []*model.JAVMagnet
	var total int64

	query := r.db.Model(&model.JAVMagnet{}).Where("quality = ?", quality)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	err := query.Order("score DESC, file_size DESC").
		Offset(offset).
		Limit(limit).
		Find(&magnets).Error

	if err != nil {
		return nil, 0, err
	}

	return magnets, total, nil
}

// GetBySubtitle 根据字幕获取磁力链接
func (r *javMagnetRepository) GetBySubtitle(hasSubtitle bool, offset, limit int) ([]*model.JAVMagnet, int64, error) {
	var magnets []*model.JAVMagnet
	var total int64

	query := r.db.Model(&model.JAVMagnet{}).Where("has_subtitle = ?", hasSubtitle)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	err := query.Order("score DESC, file_size DESC").
		Offset(offset).
		Limit(limit).
		Find(&magnets).Error

	if err != nil {
		return nil, 0, err
	}

	return magnets, total, nil
}

// GetByScore 根据评分获取磁力链接
func (r *javMagnetRepository) GetByScore(minScore float64, offset, limit int) ([]*model.JAVMagnet, int64, error) {
	var magnets []*model.JAVMagnet
	var total int64

	query := r.db.Model(&model.JAVMagnet{}).Where("score >= ?", minScore)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	err := query.Order("score DESC, file_size DESC").
		Offset(offset).
		Limit(limit).
		Find(&magnets).Error

	if err != nil {
		return nil, 0, err
	}

	return magnets, total, nil
}

// CountByMovieID 统计影片的磁力链接数量
func (r *javMagnetRepository) CountByMovieID(movieID uint) (int64, error) {
	var count int64
	err := r.db.Model(&model.JAVMagnet{}).Where("movie_id = ?", movieID).Count(&count).Error
	return count, err
}

// CountByQuality 根据清晰度统计磁力链接数量
func (r *javMagnetRepository) CountByQuality(quality string) (int64, error) {
	var count int64
	err := r.db.Model(&model.JAVMagnet{}).Where("quality = ?", quality).Count(&count).Error
	return count, err
}

// GetAverageScoreByMovie 获取影片磁力链接的平均评分
func (r *javMagnetRepository) GetAverageScoreByMovie(movieID uint) (float64, error) {
	var avgScore float64
	err := r.db.Model(&model.JAVMagnet{}).
		Where("movie_id = ?", movieID).
		Select("AVG(score)").
		Scan(&avgScore).Error
	return avgScore, err
}

// BatchCreate 批量创建磁力链接
func (r *javMagnetRepository) BatchCreate(magnets []*model.JAVMagnet) error {
	if len(magnets) == 0 {
		return nil
	}
	
	// 使用批量插入，提高性能
	return r.db.CreateInBatches(magnets, 100).Error
}

// BatchUpdateScore 批量更新评分
func (r *javMagnetRepository) BatchUpdateScore(ids []uint, score float64) error {
	return r.db.Model(&model.JAVMagnet{}).
		Where("id IN ?", ids).
		Update("score", score).Error
}

// BatchDelete 批量删除
func (r *javMagnetRepository) BatchDelete(ids []uint) error {
	return r.db.Delete(&model.JAVMagnet{}, ids).Error
}

// CleanInvalidMagnets 清理无效磁力链接
func (r *javMagnetRepository) CleanInvalidMagnets() error {
	// 删除文件大小为0或磁力链接为空的记录
	return r.db.Where("file_size = 0 OR magnet_url = '' OR magnet_url IS NULL").
		Delete(&model.JAVMagnet{}).Error
}

// CleanDuplicateMagnets 清理重复磁力链接
func (r *javMagnetRepository) CleanDuplicateMagnets() error {
	// 使用子查询删除重复的磁力链接，保留ID最小的记录
	return r.db.Exec(`
		DELETE FROM jav_magnets 
		WHERE id NOT IN (
			SELECT MIN(id) 
			FROM jav_magnets 
			GROUP BY magnet_url
		)
	`).Error
}

// GetByScoreRange 根据评分范围获取磁力链接
func (r *javMagnetRepository) GetByScoreRange(minScore, maxScore float64) ([]*model.JAVMagnet, error) {
	var magnets []*model.JAVMagnet
	err := r.db.Where("score >= ? AND score <= ?", minScore, maxScore).
		Order("score DESC").
		Find(&magnets).Error
	return magnets, err
}

// GetForScoreUpdate 获取需要更新评分的磁力链接
func (r *javMagnetRepository) GetForScoreUpdate(before time.Time, limit int) ([]*model.JAVMagnet, error) {
	var magnets []*model.JAVMagnet
	err := r.db.Where("updated_at < ?", before).
		Order("updated_at ASC").
		Limit(limit).
		Find(&magnets).Error
	return magnets, err
}