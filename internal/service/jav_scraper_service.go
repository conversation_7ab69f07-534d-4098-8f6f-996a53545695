package service

import (
	"fmt"
	"sync"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/internal/repository"
	"magnet-downloader/pkg/javscraper"
	"magnet-downloader/pkg/logger"
)

// javScraperService JAV数据采集服务实现
type javScraperService struct {
	repo    repository.Repository
	config  *javscraper.Config
	mu      sync.RWMutex
}

// NewJAVScraperService 创建JAV采集服务
func NewJAVScraperService(repo repository.Repository, config *javscraper.Config) JAVScraperService {
	return &javScraperService{
		repo:   repo,
		config: config,
	}
}

// ===== 单个影片采集 =====

// ScrapeMovieByCode 采集单个影片
func (s *javScraperService) ScrapeMovieByCode(code string) (*ScrapeMovieResponse, error) {
	startTime := time.Now()

	// 暂时返回一个模拟的成功响应
	// 实际实现需要集成具体的爬虫逻辑
	response := &ScrapeMovieResponse{
		Success:      true,
		Source:       "javbus",
		Duration:     time.Since(startTime),
		Confidence:   0.8,
		ActorsFound:  1,
		GenresFound:  2,
		MagnetsFound: 3,
	}

	logger.Infof("模拟采集影片: %s, 耗时: %v", code, response.Duration)
	return response, nil
}

// ScrapeMovieFromAllSources 从所有数据源采集影片
func (s *javScraperService) ScrapeMovieFromAllSources(code string) (*MultiSourceScrapeResponse, error) {
	startTime := time.Now()

	// 暂时返回一个模拟的响应
	sources := s.getEnabledSources()
	sourceResults := make([]*ScrapeMovieResponse, 0, len(sources))

	for _, source := range sources {
		result, _ := s.ScrapeMovieByCode(code)
		result.Source = source
		sourceResults = append(sourceResults, result)
	}

	response := &MultiSourceScrapeResponse{
		MovieCode:      code,
		Sources:        sourceResults,
		TotalSources:   len(sources),
		SuccessSources: len(sources),
		TotalDuration:  time.Since(startTime),
		MergeSuccess:   true,
		BestSource:     "javbus",
	}

	return response, nil
}

// ===== 批量采集 =====

// BatchScrapeMovies 批量采集影片
func (s *javScraperService) BatchScrapeMovies(codes []string) (*BatchScrapeResponse, error) {
	startTime := time.Now()
	
	response := &BatchScrapeResponse{
		TotalCodes: len(codes),
		Results:    make([]*ScrapeMovieResponse, 0, len(codes)),
	}

	// 并发采集
	var wg sync.WaitGroup
	var mu sync.Mutex
	semaphore := make(chan struct{}, 5) // 限制并发数

	for _, code := range codes {
		wg.Add(1)
		go func(movieCode string) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			result, err := s.ScrapeMovieByCode(movieCode)
			
			mu.Lock()
			if err != nil {
				response.FailureCount++
				response.Errors = append(response.Errors, fmt.Sprintf("%s: %v", movieCode, err))
			} else if result.Success {
				response.SuccessCount++
			} else {
				response.FailureCount++
				if result.Error != "" {
					response.Errors = append(response.Errors, fmt.Sprintf("%s: %s", movieCode, result.Error))
				}
			}
			response.Results = append(response.Results, result)
			mu.Unlock()
		}(code)
	}

	wg.Wait()
	response.TotalDuration = time.Since(startTime)

	logger.Infof("批量采集完成: 总数%d, 成功%d, 失败%d, 耗时%v", 
		response.TotalCodes, response.SuccessCount, response.FailureCount, response.TotalDuration)

	return response, nil
}

// ScrapeLatestMovies 采集最新影片
func (s *javScraperService) ScrapeLatestMovies(source string, limit int) (*BatchScrapeResponse, error) {
	// 这里需要根据具体的数据源实现获取最新影片列表的逻辑
	// 暂时返回空结果
	return &BatchScrapeResponse{
		TotalCodes:   0,
		SuccessCount: 0,
		FailureCount: 0,
		Results:      []*ScrapeMovieResponse{},
	}, nil
}

// ===== 采集任务管理 =====

// CreateScrapeTask 创建采集任务
func (s *javScraperService) CreateScrapeTask(req *CreateScrapeTaskRequest) (*ScrapeTaskResponse, error) {
	// 创建采集任务记录
	task := &model.JAVScrapeTask{
		MovieCodes:   req.MovieCodes,
		Sources:      req.Sources,
		Status:       model.JAVScrapingStatusPending,
		Priority:     req.Priority,
		AutoMerge:    req.AutoMerge,
		AutoRetry:    req.AutoRetry,
		MaxRetries:   req.MaxRetries,
		TotalMovies:  len(req.MovieCodes),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// 保存任务（这里需要扩展Repository支持采集任务）
	// err := s.repo.JAVScrapeTask().Create(task)
	// if err != nil {
	//     return nil, fmt.Errorf("创建采集任务失败: %w", err)
	// }

	// 异步执行采集任务
	go s.executeScrapeTask(task)

	return s.convertScrapeTaskToResponse(task), nil
}

// GetScrapeTask 获取采集任务
func (s *javScraperService) GetScrapeTask(taskID uint) (*ScrapeTaskResponse, error) {
	// 这里需要从数据库获取任务信息
	// task, err := s.repo.JAVScrapeTask().GetByID(taskID)
	// if err != nil {
	//     return nil, fmt.Errorf("获取采集任务失败: %w", err)
	// }
	// return s.convertScrapeTaskToResponse(task), nil
	
	return nil, fmt.Errorf("功能暂未实现")
}

// ListScrapeTasks 列出采集任务
func (s *javScraperService) ListScrapeTasks(req *ListScrapeTasksRequest) ([]*ScrapeTaskResponse, int64, error) {
	// 这里需要从数据库查询任务列表
	return nil, 0, fmt.Errorf("功能暂未实现")
}

// CancelScrapeTask 取消采集任务
func (s *javScraperService) CancelScrapeTask(taskID uint) error {
	// 这里需要实现任务取消逻辑
	return fmt.Errorf("功能暂未实现")
}

// ===== 数据融合 =====

// MergeMovieData 融合影片数据
func (s *javScraperService) MergeMovieData(movieID uint) (*MergeDataResponse, error) {
	startTime := time.Now()

	// 获取现有影片数据
	movie, err := s.repo.JAVMovie().GetByID(movieID)
	if err != nil {
		return nil, fmt.Errorf("获取影片失败: %w", err)
	}

	// 从所有数据源重新采集
	multiResult, err := s.ScrapeMovieFromAllSources(movie.Code)
	if err != nil {
		return &MergeDataResponse{
			Success:  false,
			MovieID:  movieID,
			Error:    err.Error(),
			Duration: time.Since(startTime),
		}, nil
	}

	response := &MergeDataResponse{
		Success:     multiResult.MergeSuccess,
		MovieID:     movieID,
		SourcesUsed: make([]string, 0),
		Duration:    time.Since(startTime),
	}

	// 收集成功的数据源
	for _, source := range multiResult.Sources {
		if source.Success {
			response.SourcesUsed = append(response.SourcesUsed, source.Source)
		}
	}

	if multiResult.MergedResult != nil {
		response.MergedMovie = multiResult.MergedResult
		response.Confidence = multiResult.MergedResult.Rating // 这里可以用其他指标表示可信度
	}

	return response, nil
}

// RefreshMovieData 刷新影片数据
func (s *javScraperService) RefreshMovieData(movieID uint) (*RefreshDataResponse, error) {
	startTime := time.Now()

	// 获取现有影片数据
	movie, err := s.repo.JAVMovie().GetByID(movieID)
	if err != nil {
		return nil, fmt.Errorf("获取影片失败: %w", err)
	}

	// 重新采集数据
	scrapeResult, err := s.ScrapeMovieByCode(movie.Code)
	if err != nil {
		return &RefreshDataResponse{
			Success:  false,
			MovieID:  movieID,
			Error:    err.Error(),
			Duration: time.Since(startTime),
		}, nil
	}

	response := &RefreshDataResponse{
		Success:  scrapeResult.Success,
		MovieID:  movieID,
		Duration: time.Since(startTime),
	}

	if scrapeResult.Success && scrapeResult.MovieInfo != nil {
		response.UpdatedMovie = scrapeResult.MovieInfo
		// 这里可以计算实际的变更数量
		response.ChangesCount = 1
	}

	return response, nil
}

// ===== 采集配置 =====

// UpdateScrapingConfig 更新采集配置
func (s *javScraperService) UpdateScrapingConfig(config *ScrapingConfigRequest) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 更新配置
	s.config.Sources.JavBus.Enabled = config.JavBusEnabled
	s.config.Sources.Javinizer.Enabled = config.JavinizerEnabled
	s.config.Sources.JavSP.Enabled = config.JavSPEnabled
	s.config.Timeout = config.Timeout
	s.config.RateLimit = config.RateLimit
	s.config.MaxRetries = config.MaxRetries

	// 配置已更新，无需重新初始化

	logger.Infof("采集配置已更新")
	return nil
}

// GetScrapingConfig 获取采集配置
func (s *javScraperService) GetScrapingConfig() (*ScrapingConfigResponse, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return &ScrapingConfigResponse{
		ScrapingConfigRequest: &ScrapingConfigRequest{
			JavBusEnabled:    s.config.Sources.JavBus.Enabled,
			JavinizerEnabled: s.config.Sources.Javinizer.Enabled,
			JavSPEnabled:     s.config.Sources.JavSP.Enabled,
			Timeout:          s.config.Timeout,
			RateLimit:        s.config.RateLimit,
			MaxRetries:       s.config.MaxRetries,
		},
		UpdatedAt: time.Now(),
	}, nil
}

// TestScrapingSource 测试数据源
func (s *javScraperService) TestScrapingSource(source string) (*SourceTestResponse, error) {
	startTime := time.Now()
	testCode := "TEST-001" // 使用测试番号

	result, err := s.scrapeFromSource(testCode, source)
	
	response := &SourceTestResponse{
		Source:       source,
		Available:    err == nil && result.Success,
		ResponseTime: time.Since(startTime),
		TestCode:     testCode,
	}

	if err != nil {
		response.Error = err.Error()
	} else if result.Success {
		response.TestResult = result
	} else {
		response.Error = result.Error
	}

	return response, nil
}

// ===== 采集统计 =====

// GetScrapingStatistics 获取采集统计
func (s *javScraperService) GetScrapingStatistics() (*ScrapingStatisticsResponse, error) {
	// 获取基础统计
	totalScrapes := int64(0)
	successfulScrapes, _ := s.repo.JAVMovie().CountByStatus(model.JAVScrapingStatusCompleted)
	failedScrapes, _ := s.repo.JAVMovie().CountByStatus(model.JAVScrapingStatusFailed)
	totalScrapes = successfulScrapes + failedScrapes

	var successRate float64
	if totalScrapes > 0 {
		successRate = float64(successfulScrapes) / float64(totalScrapes) * 100
	}

	// 获取数据源统计
	sourceStats := make(map[string]*SourceStats)
	sources := s.getEnabledSources()
	for _, source := range sources {
		sourceCount, _ := s.repo.JAVMovie().CountBySource(model.JAVScrapingSource(source))
		sourceStats[source] = &SourceStats{
			Source:       source,
			ScrapeCount:  sourceCount,
			SuccessCount: sourceCount, // 简化处理
			SuccessRate:  100.0,
		}
	}

	return &ScrapingStatisticsResponse{
		ScrapingStatsResponse: &ScrapingStatsResponse{
			TotalScrapes:      totalScrapes,
			SuccessfulScrapes: successfulScrapes,
			FailedScrapes:     failedScrapes,
			SuccessRate:       successRate,
			SourceStats:       sourceStats,
		},
		TotalDataSources: len(sources),
		ActiveSources:    sources,
		LastScrapeTime:   time.Now(),
	}, nil
}

// GetSourceStatistics 获取数据源统计
func (s *javScraperService) GetSourceStatistics(source string) (*SourceStatisticsResponse, error) {
	scrapeCount, _ := s.repo.JAVMovie().CountBySource(model.JAVScrapingSource(source))
	
	return &SourceStatisticsResponse{
		Source:          source,
		TotalScrapes:    scrapeCount,
		SuccessCount:    scrapeCount, // 简化处理
		SuccessRate:     100.0,
		LastScrapeTime:  time.Now(),
		IsHealthy:       true,
	}, nil
}

// ===== 辅助方法 =====

// getEnabledSources 获取启用的数据源
func (s *javScraperService) getEnabledSources() []string {
	var sources []string
	if s.config.Sources.JavBus.Enabled {
		sources = append(sources, "javbus")
	}
	if s.config.Sources.Javinizer.Enabled {
		sources = append(sources, "javinizer")
	}
	if s.config.Sources.JavSP.Enabled {
		sources = append(sources, "javsp")
	}
	return sources
}

// scrapeFromSource 从指定数据源采集
func (s *javScraperService) scrapeFromSource(code, source string) (*ScrapeMovieResponse, error) {
	// 这里需要根据具体的数据源实现采集逻辑
	// 暂时使用通用的采集方法
	return s.ScrapeMovieByCode(code)
}

// convertAndSaveMovie 转换并保存影片数据（暂时简化）
func (s *javScraperService) convertAndSaveMovie(movieInfo interface{}, source string) (*model.JAVMovie, error) {
	// 暂时返回一个模拟的影片对象
	movie := &model.JAVMovie{
		Code:           "TEST-001",
		Title:          "测试影片",
		ScrapingStatus: model.JAVScrapingStatusCompleted,
		ScrapingSource: model.JAVScrapingSource(source),
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
	return movie, nil
}

// convertAndSaveMergedMovie 转换并保存融合后的影片数据（暂时简化）
func (s *javScraperService) convertAndSaveMergedMovie(mergedResult interface{}) (*model.JAVMovie, error) {
	// 暂时返回一个模拟的影片对象
	movie := &model.JAVMovie{
		Code:           "MERGED-001",
		Title:          "融合影片",
		ScrapingStatus: model.JAVScrapingStatusCompleted,
		ScrapingSource: model.JAVScrapingSourceMerged,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
	return movie, nil
}

// 这些方法已被简化，暂时不需要复杂的转换逻辑

// convertToMergerMovieInfo 转换为融合器的影片信息格式（暂时简化）
func (s *javScraperService) convertToMergerMovieInfo(movieInfo *JAVMovieResponse) interface{} {
	// 暂时返回空接口，实际实现需要具体的融合器类型
	return nil
}

// convertMovieToResponse 转换影片模型为响应格式
func (s *javScraperService) convertMovieToResponse(movie *model.JAVMovie) *JAVMovieResponse {
	if movie == nil {
		return nil
	}

	return &JAVMovieResponse{
		ID:             movie.ID,
		Code:           movie.Code,
		Title:          movie.Title,
		TitleEn:        movie.TitleEn,
		Studio:         movie.Studio,
		ReleaseDate:    movie.ReleaseDate,
		Duration:       movie.Duration,
		Rating:         movie.Rating,
		Plot:           movie.Plot,
		PlotEn:         movie.PlotEn,
		CoverURL:       movie.CoverURL,
		PosterURL:      movie.PosterURL,
		ScrapingStatus: movie.ScrapingStatus,
		ScrapingSource: movie.ScrapingSource,
		CreatedAt:      movie.CreatedAt,
		UpdatedAt:      movie.UpdatedAt,
	}
}

// convertScrapeTaskToResponse 转换采集任务为响应格式
func (s *javScraperService) convertScrapeTaskToResponse(task *model.JAVScrapeTask) *ScrapeTaskResponse {
	return &ScrapeTaskResponse{
		ID:              task.ID,
		MovieCodes:      task.MovieCodes,
		Sources:         task.Sources,
		Status:          task.Status,
		TotalMovies:     task.TotalMovies,
		CompletedMovies: task.CompletedMovies,
		FailedMovies:    task.FailedMovies,
		CreatedAt:       task.CreatedAt,
		UpdatedAt:       task.UpdatedAt,
	}
}

// findBestResult 找到最佳采集结果
func (s *javScraperService) findBestResult(results []*ScrapeMovieResponse) *ScrapeMovieResponse {
	var best *ScrapeMovieResponse
	var bestScore float64

	for _, result := range results {
		if !result.Success {
			continue
		}

		// 计算评分（可信度 + 数据完整性）
		score := result.Confidence
		if result.ActorsFound > 0 {
			score += 0.1
		}
		if result.GenresFound > 0 {
			score += 0.1
		}
		if result.MagnetsFound > 0 {
			score += 0.2
		}

		if score > bestScore {
			bestScore = score
			best = result
		}
	}

	return best
}

// executeScrapeTask 执行采集任务
func (s *javScraperService) executeScrapeTask(task *model.JAVScrapeTask) {
	// 这里实现异步采集任务的执行逻辑
	logger.Infof("开始执行采集任务: %d, 影片数量: %d", task.ID, task.TotalMovies)
	
	// 更新任务状态为进行中
	task.Status = model.JAVScrapingStatusInProgress
	task.UpdatedAt = time.Now()
	
	// 执行批量采集
	batchResult, err := s.BatchScrapeMovies(task.MovieCodes)
	if err != nil {
		task.Status = model.JAVScrapingStatusFailed
		task.Error = err.Error()
	} else {
		task.CompletedMovies = batchResult.SuccessCount
		task.FailedMovies = batchResult.FailureCount
		if batchResult.FailureCount == 0 {
			task.Status = model.JAVScrapingStatusCompleted
		} else {
			task.Status = model.JAVScrapingStatusPartiallyCompleted
		}
	}
	
	task.UpdatedAt = time.Now()
	logger.Infof("采集任务完成: %d, 成功: %d, 失败: %d", task.ID, task.CompletedMovies, task.FailedMovies)
}