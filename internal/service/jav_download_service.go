package service

import (
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/internal/repository"
	"magnet-downloader/pkg/logger"
)

// javDownloadService JAV下载管理服务实现
type javDownloadService struct {
	repo            repository.Repository
	downloadService DownloadService
	autoUploadService AutoUploadService
}

// NewJAVDownloadService 创建JAV下载服务
func NewJAVDownloadService(repo repository.Repository, downloadService DownloadService, autoUploadService AutoUploadService) JAVDownloadService {
	return &javDownloadService{
		repo:              repo,
		downloadService:   downloadService,
		autoUploadService: autoUploadService,
	}
}

// ===== 下载任务创建 =====

// CreateJAVDownload 创建JAV下载任务
func (s *javDownloadService) CreateJAVDownload(userID uint, req *CreateJAVDownloadRequest) (*JAVDownloadResponse, error) {
	// 根据影片番号获取影片信息
	movie, err := s.repo.JAVMovie().GetByCode(req.MovieCode)
	if err != nil {
		return nil, fmt.Errorf("获取影片信息失败: %w", err)
	}

	// 选择磁力链接
	var magnet *model.JAVMagnet
	if req.MagnetID != nil {
		// 使用指定的磁力链接
		magnet, err = s.repo.JAVMagnet().GetByID(*req.MagnetID)
		if err != nil {
			return nil, fmt.Errorf("获取磁力链接失败: %w", err)
		}
		
		// 验证磁力链接属于该影片
		if magnet.MovieID != movie.ID {
			return nil, fmt.Errorf("磁力链接不属于该影片")
		}
	} else {
		// 自动选择最佳磁力链接
		magnet, err = s.repo.JAVMagnet().GetBestMagnetByMovie(movie.ID)
		if err != nil {
			return nil, fmt.Errorf("获取最佳磁力链接失败: %w", err)
		}
	}

	// 构建下载路径
	savePath := s.buildDownloadPath(movie, magnet, req.Options)

	// 创建下载任务
	downloadTask, err := s.downloadService.CreateDownloadTask(
		userID,
		magnet.MagnetURL,
		s.buildTaskName(movie, magnet),
		savePath,
	)
	if err != nil {
		return nil, fmt.Errorf("创建下载任务失败: %w", err)
	}

	// 更新影片的下载任务关联
	movie.DownloadTaskID = &downloadTask.ID
	err = s.repo.JAVMovie().Update(movie)
	if err != nil {
		logger.Errorf("更新影片下载任务关联失败: %v", err)
	}

	// 如果启用自动上传，设置上传配置
	if req.Options != nil && req.Options.AutoUpload {
		err = s.setupAutoUpload(downloadTask.ID, req.Options.UploadPlatform)
		if err != nil {
			logger.Errorf("设置自动上传失败: %v", err)
		}
	}

	// 构建响应
	response := &JAVDownloadResponse{
		ID:           downloadTask.ID,
		UserID:       userID,
		MovieID:      movie.ID,
		MagnetID:     magnet.ID,
		Movie:        s.convertMovieToResponse(movie),
		Magnet:       s.convertMagnetToResponse(magnet),
		DownloadTask: downloadTask,
		Status:       downloadTask.Status,
		Options:      req.Options,
		CreatedAt:    downloadTask.CreatedAt,
		UpdatedAt:    downloadTask.UpdatedAt,
	}

	logger.Infof("创建JAV下载任务成功: 用户%d, 影片%s, 任务%d", userID, movie.Code, downloadTask.ID)
	return response, nil
}

// CreateDownloadFromMovie 从影片创建下载任务
func (s *javDownloadService) CreateDownloadFromMovie(userID uint, movieID uint, options *JAVDownloadOptions) (*JAVDownloadResponse, error) {
	// 获取影片信息
	movie, err := s.repo.JAVMovie().GetByID(movieID)
	if err != nil {
		return nil, fmt.Errorf("获取影片信息失败: %w", err)
	}

	// 构建创建请求
	req := &CreateJAVDownloadRequest{
		MovieCode: movie.Code,
		Options:   options,
	}

	return s.CreateJAVDownload(userID, req)
}

// ===== 下载任务管理 =====

// GetJAVDownload 获取JAV下载任务
func (s *javDownloadService) GetJAVDownload(downloadID uint) (*JAVDownloadResponse, error) {
	// 获取下载任务
	downloadTask, err := s.downloadService.GetDownloadTask(downloadID)
	if err != nil {
		return nil, fmt.Errorf("获取下载任务失败: %w", err)
	}

	// 查找关联的影片
	movie, err := s.findMovieByDownloadTask(downloadTask.ID)
	if err != nil {
		logger.Errorf("查找关联影片失败: %v", err)
	}

	// 查找关联的磁力链接
	var magnet *model.JAVMagnet
	if movie != nil {
		magnet, err = s.findMagnetByURL(movie.ID, downloadTask.MagnetURI)
		if err != nil {
			logger.Errorf("查找关联磁力链接失败: %v", err)
		}
	}

	// 构建响应
	response := &JAVDownloadResponse{
		ID:           downloadTask.ID,
		UserID:       downloadTask.UserID,
		DownloadTask: downloadTask,
		Status:       downloadTask.Status,
		Progress:     downloadTask.Progress,
		Speed:        downloadTask.DownloadSpeed,
		ETA:          0, // ETA需要转换处理
		CreatedAt:    downloadTask.CreatedAt,
		UpdatedAt:    downloadTask.UpdatedAt,
	}

	if movie != nil {
		response.MovieID = movie.ID
		response.Movie = s.convertMovieToResponse(movie)
	}

	if magnet != nil {
		response.MagnetID = magnet.ID
		response.Magnet = s.convertMagnetToResponse(magnet)
	}

	return response, nil
}

// ListJAVDownloads 列出JAV下载任务
func (s *javDownloadService) ListJAVDownloads(req *ListJAVDownloadsRequest) ([]*JAVDownloadResponse, int64, error) {
	// 参数验证
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	offset := (req.Page - 1) * req.PageSize

	// 获取下载任务列表
	var downloadTasks []*model.DownloadTask
	var total int64
	var err error

	if req.UserID > 0 {
		downloadTasks, total, err = s.downloadService.GetUserDownloads(req.UserID, offset, req.PageSize)
	} else if req.Status != "" {
		downloadTasks, total, err = s.downloadService.GetDownloadsByStatus(req.Status, offset, req.PageSize)
	} else {
		// 这里需要扩展DownloadService支持通用列表查询
		return nil, 0, fmt.Errorf("需要指定用户ID或状态")
	}

	if err != nil {
		return nil, 0, fmt.Errorf("获取下载任务列表失败: %w", err)
	}

	// 转换为JAV下载响应
	responses := make([]*JAVDownloadResponse, 0, len(downloadTasks))
	for _, task := range downloadTasks {
		// 检查是否为JAV下载任务
		if !s.isJAVDownloadTask(task) {
			continue
		}

		response, err := s.GetJAVDownload(task.ID)
		if err != nil {
			logger.Errorf("获取JAV下载任务详情失败: %v", err)
			continue
		}

		// 应用过滤条件
		if req.MovieCode != "" && (response.Movie == nil || !strings.Contains(response.Movie.Code, req.MovieCode)) {
			continue
		}

		responses = append(responses, response)
	}

	return responses, total, nil
}

// UpdateJAVDownload 更新JAV下载任务
func (s *javDownloadService) UpdateJAVDownload(downloadID uint, req *UpdateJAVDownloadRequest) (*JAVDownloadResponse, error) {
	// 获取当前下载任务
	response, err := s.GetJAVDownload(downloadID)
	if err != nil {
		return nil, err
	}

	// 更新选项
	if req.Options != nil {
		response.Options = req.Options
	}

	// 更新优先级
	if req.Priority != nil {
		// 这里需要扩展DownloadService支持优先级更新
		logger.Infof("更新下载任务优先级: %d -> %d", downloadID, *req.Priority)
	}

	response.UpdatedAt = time.Now()
	return response, nil
}

// DeleteJAVDownload 删除JAV下载任务
func (s *javDownloadService) DeleteJAVDownload(downloadID uint) error {
	// 获取下载任务信息
	response, err := s.GetJAVDownload(downloadID)
	if err != nil {
		return err
	}

	// 取消下载任务
	err = s.downloadService.CancelDownload(downloadID)
	if err != nil {
		logger.Errorf("取消下载任务失败: %v", err)
	}

	// 清除影片的下载任务关联
	if response.Movie != nil {
		movie, err := s.repo.JAVMovie().GetByID(response.MovieID)
		if err == nil && movie.DownloadTaskID != nil && *movie.DownloadTaskID == downloadID {
			movie.DownloadTaskID = nil
			err = s.repo.JAVMovie().Update(movie)
			if err != nil {
				logger.Errorf("清除影片下载任务关联失败: %v", err)
			}
		}
	}

	logger.Infof("删除JAV下载任务: %d", downloadID)
	return nil
}

// ===== 下载控制 =====

// StartJAVDownload 开始JAV下载
func (s *javDownloadService) StartJAVDownload(downloadID uint) error {
	err := s.downloadService.StartDownload(downloadID)
	if err != nil {
		return fmt.Errorf("开始下载失败: %w", err)
	}

	logger.Infof("开始JAV下载: %d", downloadID)
	return nil
}

// PauseJAVDownload 暂停JAV下载
func (s *javDownloadService) PauseJAVDownload(downloadID uint) error {
	err := s.downloadService.PauseDownload(downloadID)
	if err != nil {
		return fmt.Errorf("暂停下载失败: %w", err)
	}

	logger.Infof("暂停JAV下载: %d", downloadID)
	return nil
}

// ResumeJAVDownload 恢复JAV下载
func (s *javDownloadService) ResumeJAVDownload(downloadID uint) error {
	err := s.downloadService.ResumeDownload(downloadID)
	if err != nil {
		return fmt.Errorf("恢复下载失败: %w", err)
	}

	logger.Infof("恢复JAV下载: %d", downloadID)
	return nil
}

// CancelJAVDownload 取消JAV下载
func (s *javDownloadService) CancelJAVDownload(downloadID uint) error {
	err := s.downloadService.CancelDownload(downloadID)
	if err != nil {
		return fmt.Errorf("取消下载失败: %w", err)
	}

	logger.Infof("取消JAV下载: %d", downloadID)
	return nil
}

// RetryJAVDownload 重试JAV下载
func (s *javDownloadService) RetryJAVDownload(downloadID uint) error {
	err := s.downloadService.RetryDownload(downloadID)
	if err != nil {
		return fmt.Errorf("重试下载失败: %w", err)
	}

	logger.Infof("重试JAV下载: %d", downloadID)
	return nil
}

// ===== 批量操作 =====

// BatchStartJAVDownloads 批量开始JAV下载
func (s *javDownloadService) BatchStartJAVDownloads(downloadIDs []uint) (*BatchJAVDownloadResponse, error) {
	response := &BatchJAVDownloadResponse{}

	for _, id := range downloadIDs {
		err := s.StartJAVDownload(id)
		if err != nil {
			response.FailureCount++
			response.Errors = append(response.Errors, fmt.Sprintf("下载%d: %v", id, err))
		} else {
			response.SuccessCount++
		}
	}

	logger.Infof("批量开始JAV下载: 成功%d, 失败%d", response.SuccessCount, response.FailureCount)
	return response, nil
}

// BatchPauseJAVDownloads 批量暂停JAV下载
func (s *javDownloadService) BatchPauseJAVDownloads(downloadIDs []uint) (*BatchJAVDownloadResponse, error) {
	response := &BatchJAVDownloadResponse{}

	for _, id := range downloadIDs {
		err := s.PauseJAVDownload(id)
		if err != nil {
			response.FailureCount++
			response.Errors = append(response.Errors, fmt.Sprintf("下载%d: %v", id, err))
		} else {
			response.SuccessCount++
		}
	}

	logger.Infof("批量暂停JAV下载: 成功%d, 失败%d", response.SuccessCount, response.FailureCount)
	return response, nil
}

// BatchCancelJAVDownloads 批量取消JAV下载
func (s *javDownloadService) BatchCancelJAVDownloads(downloadIDs []uint) (*BatchJAVDownloadResponse, error) {
	response := &BatchJAVDownloadResponse{}

	for _, id := range downloadIDs {
		err := s.CancelJAVDownload(id)
		if err != nil {
			response.FailureCount++
			response.Errors = append(response.Errors, fmt.Sprintf("下载%d: %v", id, err))
		} else {
			response.SuccessCount++
		}
	}

	logger.Infof("批量取消JAV下载: 成功%d, 失败%d", response.SuccessCount, response.FailureCount)
	return response, nil
}

// ===== 下载完成处理 =====

// OnJAVDownloadCompleted 处理JAV下载完成事件
func (s *javDownloadService) OnJAVDownloadCompleted(downloadID uint) error {
	// 获取下载任务信息
	response, err := s.GetJAVDownload(downloadID)
	if err != nil {
		return fmt.Errorf("获取下载任务失败: %w", err)
	}

	// 更新影片状态
	if response.Movie != nil {
		movie, err := s.repo.JAVMovie().GetByID(response.MovieID)
		if err == nil {
			// 可以在这里添加下载完成后的状态更新逻辑
			logger.Infof("影片下载完成: %s", movie.Code)
		}
	}

	// 如果启用自动上传，触发上传
	if response.Options != nil && response.Options.AutoUpload {
		err = s.TriggerAutoUpload(downloadID)
		if err != nil {
			logger.Errorf("触发自动上传失败: %v", err)
		}
	}

	logger.Infof("JAV下载完成处理: %d", downloadID)
	return nil
}

// TriggerAutoUpload 触发自动上传
func (s *javDownloadService) TriggerAutoUpload(downloadID uint) error {
	// 获取下载任务
	downloadTask, err := s.downloadService.GetDownloadTask(downloadID)
	if err != nil {
		return fmt.Errorf("获取下载任务失败: %w", err)
	}

	// 触发自动上传
	if s.autoUploadService != nil {
		// 这里需要根据AutoUploadService的具体接口调用上传方法
		logger.Infof("触发自动上传: 任务%d, 任务名%s", downloadID, downloadTask.TaskName)
	}

	return nil
}

// ===== 下载统计 =====

// GetJAVDownloadStats 获取JAV下载统计
func (s *javDownloadService) GetJAVDownloadStats() (*JAVDownloadStatsResponse, error) {
	// 获取基础下载统计
	downloadStats, err := s.downloadService.GetDownloadStats()
	if err != nil {
		return nil, fmt.Errorf("获取下载统计失败: %w", err)
	}

	// 构建JAV下载统计
	stats := &JAVDownloadStatsResponse{
		TotalDownloads:     downloadStats.TotalTasks,
		ActiveDownloads:    downloadStats.ActiveTasks,
		CompletedDownloads: downloadStats.CompletedTasks,
		FailedDownloads:    downloadStats.FailedTasks,
		TotalSize:          0, // 需要单独计算
		AvgDownloadTime:    0, // 需要单独计算
		PopularQualities:   make(map[string]int64),
		DailyDownloads:     make(map[string]int64),
	}

	if stats.TotalDownloads > 0 {
		stats.SuccessRate = float64(stats.CompletedDownloads) / float64(stats.TotalDownloads) * 100
	}

	return stats, nil
}

// GetUserJAVDownloadStats 获取用户JAV下载统计
func (s *javDownloadService) GetUserJAVDownloadStats(userID uint) (*UserJAVDownloadStatsResponse, error) {
	// 获取用户下载统计
	userStats, err := s.downloadService.GetUserStats(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户下载统计失败: %w", err)
	}

	// 构建用户JAV下载统计
	stats := &UserJAVDownloadStatsResponse{
		UserID:             userID,
		TotalDownloads:     userStats.TotalTasks,
		CompletedDownloads: userStats.CompletedTasks,
		FailedDownloads:    userStats.FailedTasks,
		TotalSize:          0, // 需要单独计算
		FavoriteQualities:  []string{},
		RecentDownloads:    []*JAVDownloadResponse{},
	}

	if stats.TotalDownloads > 0 {
		stats.SuccessRate = float64(stats.CompletedDownloads) / float64(stats.TotalDownloads) * 100
	}

	return stats, nil
}

// ===== 辅助方法 =====

// buildDownloadPath 构建下载路径
func (s *javDownloadService) buildDownloadPath(movie *model.JAVMovie, magnet *model.JAVMagnet, options *JAVDownloadOptions) string {
	basePath := "/downloads/jav" // 默认路径
	
	if options != nil && options.DownloadPath != "" {
		basePath = options.DownloadPath
	}

	if options != nil && options.CreateFolder {
		folderName := movie.Code
		if options.FolderName != "" {
			folderName = options.FolderName
		}
		return filepath.Join(basePath, folderName)
	}

	return basePath
}

// buildTaskName 构建任务名称
func (s *javDownloadService) buildTaskName(movie *model.JAVMovie, magnet *model.JAVMagnet) string {
	return fmt.Sprintf("[JAV] %s - %s", movie.Code, movie.Title)
}

// setupAutoUpload 设置自动上传
func (s *javDownloadService) setupAutoUpload(downloadID uint, platform string) error {
	// 这里需要根据AutoUploadService的具体接口设置自动上传
	logger.Infof("设置自动上传: 任务%d, 平台%s", downloadID, platform)
	return nil
}

// isJAVDownloadTask 检查是否为JAV下载任务
func (s *javDownloadService) isJAVDownloadTask(task *model.DownloadTask) bool {
	// 通过任务名称或其他标识判断是否为JAV下载任务
	if strings.Contains(task.TaskName, "[JAV]") || strings.Contains(task.TaskName, "JAV") {
		return true
	}
	
	// 检查是否有关联的影片
	movie, err := s.findMovieByDownloadTask(task.ID)
	return err == nil && movie != nil
}

// findMovieByDownloadTask 根据下载任务查找影片
func (s *javDownloadService) findMovieByDownloadTask(downloadTaskID uint) (*model.JAVMovie, error) {
	// 查找关联的影片
	movies, _, err := s.repo.JAVMovie().List(0, 1000, map[string]interface{}{
		"download_task_id": downloadTaskID,
	})
	if err != nil || len(movies) == 0 {
		return nil, fmt.Errorf("未找到关联影片")
	}

	return movies[0], nil
}

// findMagnetByURL 根据磁力链接URL查找磁力记录
func (s *javDownloadService) findMagnetByURL(movieID uint, magnetURL string) (*model.JAVMagnet, error) {
	magnet, err := s.repo.JAVMagnet().GetByMagnetURL(magnetURL)
	if err != nil {
		return nil, err
	}

	if magnet.MovieID != movieID {
		return nil, fmt.Errorf("磁力链接不属于该影片")
	}

	return magnet, nil
}

// convertMovieToResponse 转换影片模型为响应格式
func (s *javDownloadService) convertMovieToResponse(movie *model.JAVMovie) *JAVMovieResponse {
	if movie == nil {
		return nil
	}

	return &JAVMovieResponse{
		ID:             movie.ID,
		Code:           movie.Code,
		Title:          movie.Title,
		TitleEn:        movie.TitleEn,
		Studio:         movie.Studio,
		ReleaseDate:    movie.ReleaseDate,
		Duration:       movie.Duration,
		Rating:         movie.Rating,
		Plot:           movie.Plot,
		PlotEn:         movie.PlotEn,
		CoverURL:       movie.CoverURL,
		PosterURL:      movie.PosterURL,
		ScrapingStatus: movie.ScrapingStatus,
		ScrapingSource: movie.ScrapingSource,
		CreatedAt:      movie.CreatedAt,
		UpdatedAt:      movie.UpdatedAt,
	}
}

// convertMagnetToResponse 转换磁力链接模型为响应格式
func (s *javDownloadService) convertMagnetToResponse(magnet *model.JAVMagnet) *JAVMagnetResponse {
	if magnet == nil {
		return nil
	}

	uploadDate := time.Time{}
	if magnet.UploadDate != nil {
		uploadDate = *magnet.UploadDate
	}

	return &JAVMagnetResponse{
		ID:               magnet.ID,
		MovieID:          magnet.MovieID,
		MagnetURL:        magnet.MagnetURL,
		FileName:         magnet.FileName,
		FileSize:         magnet.FileSize,
		FileSizeFormatted: formatBytes(magnet.FileSize),
		Quality:          string(magnet.Quality),
		HasSubtitle:      magnet.HasSubtitle,
		SubtitleLanguage: string(magnet.SubtitleLanguage),
		Source:           magnet.Source,
		Uploader:         magnet.Uploader,
		Seeders:          magnet.Seeders,
		Leechers:         magnet.Leechers,
		Score:            magnet.Score,
		UploadDate:       uploadDate,
		CreatedAt:        magnet.CreatedAt,
		UpdatedAt:        magnet.UpdatedAt,
	}
}