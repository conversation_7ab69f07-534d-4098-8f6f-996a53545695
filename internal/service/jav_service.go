package service

import (
	"fmt"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/internal/repository"
	"magnet-downloader/pkg/logger"
)

// javService JAV业务服务实现
type javService struct {
	repo repository.Repository
}

// NewJAVService 创建JAV服务
func NewJAVService(repo repository.Repository) JAVService {
	return &javService{
		repo: repo,
	}
}

// ===== 影片管理 =====

// GetMovieByCode 根据番号获取影片
func (s *javService) GetMovieByCode(code string) (*JAVMovieResponse, error) {
	movie, err := s.repo.JAVMovie().GetByCode(code)
	if err != nil {
		return nil, fmt.Errorf("获取影片失败: %w", err)
	}

	return s.convertMovieToResponse(movie), nil
}

// SearchMovies 搜索影片
func (s *javService) SearchMovies(req *SearchMoviesRequest) (*SearchMoviesResponse, error) {
	// 参数验证
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	offset := (req.Page - 1) * req.PageSize

	var movies []*model.JAVMovie
	var total int64
	var err error

	// 根据搜索条件选择查询方法
	if req.Keyword != "" {
		movies, total, err = s.repo.JAVMovie().Search(req.Keyword, offset, req.PageSize)
	} else if req.Studio != "" {
		movies, total, err = s.repo.JAVMovie().GetByStudio(req.Studio, offset, req.PageSize)
	} else {
		// 构建过滤条件
		filters := make(map[string]interface{})
		if req.Year > 0 {
			filters["year"] = req.Year
		}
		if req.RatingMin > 0 {
			filters["rating_min"] = req.RatingMin
		}
		if req.RatingMax > 0 {
			filters["rating_max"] = req.RatingMax
		}
		if req.HasSubtitle != nil {
			filters["has_subtitle"] = *req.HasSubtitle
		}

		movies, total, err = s.repo.JAVMovie().List(offset, req.PageSize, filters)
	}

	if err != nil {
		return nil, fmt.Errorf("搜索影片失败: %w", err)
	}

	// 转换为响应格式
	movieResponses := make([]*JAVMovieResponse, len(movies))
	for i, movie := range movies {
		movieResponses[i] = s.convertMovieToResponse(movie)
	}

	return &SearchMoviesResponse{
		Movies:   movieResponses,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		HasMore:  int64(req.Page*req.PageSize) < total,
	}, nil
}

// GetLatestMovies 获取最新影片
func (s *javService) GetLatestMovies(limit int) ([]*JAVMovieResponse, error) {
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	movies, err := s.repo.JAVMovie().GetLatestMovies(limit)
	if err != nil {
		return nil, fmt.Errorf("获取最新影片失败: %w", err)
	}

	responses := make([]*JAVMovieResponse, len(movies))
	for i, movie := range movies {
		responses[i] = s.convertMovieToResponse(movie)
	}

	return responses, nil
}

// GetPopularMovies 获取热门影片
func (s *javService) GetPopularMovies(limit int) ([]*JAVMovieResponse, error) {
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	movies, err := s.repo.JAVMovie().GetPopularMovies(limit)
	if err != nil {
		return nil, fmt.Errorf("获取热门影片失败: %w", err)
	}

	responses := make([]*JAVMovieResponse, len(movies))
	for i, movie := range movies {
		responses[i] = s.convertMovieToResponse(movie)
	}

	return responses, nil
}

// ===== 影片详情 =====

// GetMovieDetails 获取影片详情
func (s *javService) GetMovieDetails(movieID uint) (*JAVMovieDetailsResponse, error) {
	movie, err := s.repo.JAVMovie().GetWithAll(movieID)
	if err != nil {
		return nil, fmt.Errorf("获取影片详情失败: %w", err)
	}

	response := &JAVMovieDetailsResponse{
		JAVMovieResponse: s.convertMovieToResponse(movie),
		Actors:           make([]*JAVActorResponse, len(movie.Actors)),
		Genres:           make([]*JAVGenreResponse, len(movie.Genres)),
		Magnets:          make([]*JAVMagnetResponse, len(movie.Magnets)),
	}

	// 转换演员信息
	for i, actor := range movie.Actors {
		response.Actors[i] = s.convertActorToResponse(&actor)
	}

	// 转换分类信息
	for i, genre := range movie.Genres {
		response.Genres[i] = s.convertGenreToResponse(&genre)
	}

	// 转换磁力链接信息
	for i, magnet := range movie.Magnets {
		response.Magnets[i] = s.convertMagnetToResponse(&magnet)
	}

	return response, nil
}

// GetMovieActors 获取影片演员
func (s *javService) GetMovieActors(movieID uint) ([]*JAVActorResponse, error) {
	movie, err := s.repo.JAVMovie().GetWithActors(movieID)
	if err != nil {
		return nil, fmt.Errorf("获取影片演员失败: %w", err)
	}

	responses := make([]*JAVActorResponse, len(movie.Actors))
	for i, actor := range movie.Actors {
		responses[i] = s.convertActorToResponse(&actor)
	}

	return responses, nil
}

// GetMovieGenres 获取影片分类
func (s *javService) GetMovieGenres(movieID uint) ([]*JAVGenreResponse, error) {
	movie, err := s.repo.JAVMovie().GetWithGenres(movieID)
	if err != nil {
		return nil, fmt.Errorf("获取影片分类失败: %w", err)
	}

	responses := make([]*JAVGenreResponse, len(movie.Genres))
	for i, genre := range movie.Genres {
		responses[i] = s.convertGenreToResponse(&genre)
	}

	return responses, nil
}

// GetMovieMagnets 获取影片磁力链接
func (s *javService) GetMovieMagnets(movieID uint) ([]*JAVMagnetResponse, error) {
	magnets, err := s.repo.JAVMagnet().GetByMovieID(movieID)
	if err != nil {
		return nil, fmt.Errorf("获取影片磁力链接失败: %w", err)
	}

	responses := make([]*JAVMagnetResponse, len(magnets))
	for i, magnet := range magnets {
		responses[i] = s.convertMagnetToResponse(magnet)
	}

	return responses, nil
}

// ===== 演员管理 =====

// GetActorByID 根据ID获取演员
func (s *javService) GetActorByID(actorID uint) (*JAVActorResponse, error) {
	actor, err := s.repo.JAVActor().GetByID(actorID)
	if err != nil {
		return nil, fmt.Errorf("获取演员失败: %w", err)
	}

	return s.convertActorToResponse(actor), nil
}

// SearchActors 搜索演员
func (s *javService) SearchActors(req *SearchActorsRequest) (*SearchActorsResponse, error) {
	// 参数验证
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	offset := (req.Page - 1) * req.PageSize

	var actors []*model.JAVActor
	var total int64
	var err error

	if req.Keyword != "" {
		actors, total, err = s.repo.JAVActor().Search(req.Keyword, offset, req.PageSize)
	} else {
		// 构建过滤条件
		filters := make(map[string]interface{})
		if req.HeightMin > 0 {
			filters["height_min"] = req.HeightMin
		}
		if req.HeightMax > 0 {
			filters["height_max"] = req.HeightMax
		}
		if req.BloodType != "" {
			filters["blood_type"] = req.BloodType
		}
		if req.DebutYear > 0 {
			filters["debut_year"] = req.DebutYear
		}

		actors, total, err = s.repo.JAVActor().List(offset, req.PageSize, filters)
	}

	if err != nil {
		return nil, fmt.Errorf("搜索演员失败: %w", err)
	}

	// 转换为响应格式
	actorResponses := make([]*JAVActorResponse, len(actors))
	for i, actor := range actors {
		actorResponses[i] = s.convertActorToResponse(actor)
	}

	return &SearchActorsResponse{
		Actors:   actorResponses,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		HasMore:  int64(req.Page*req.PageSize) < total,
	}, nil
}

// GetPopularActors 获取热门演员
func (s *javService) GetPopularActors(limit int) ([]*JAVActorResponse, error) {
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	actors, err := s.repo.JAVActor().GetPopularActors(limit)
	if err != nil {
		return nil, fmt.Errorf("获取热门演员失败: %w", err)
	}

	responses := make([]*JAVActorResponse, len(actors))
	for i, actor := range actors {
		responses[i] = s.convertActorToResponse(actor)
	}

	return responses, nil
}

// GetActorMovies 获取演员影片
func (s *javService) GetActorMovies(actorID uint, offset, limit int) ([]*JAVMovieResponse, int64, error) {
	movies, total, err := s.repo.JAVActor().GetMoviesByActor(actorID, offset, limit)
	if err != nil {
		return nil, 0, fmt.Errorf("获取演员影片失败: %w", err)
	}

	responses := make([]*JAVMovieResponse, len(movies))
	for i, movie := range movies {
		responses[i] = s.convertMovieToResponse(movie)
	}

	return responses, total, nil
}

// ===== 分类管理 =====

// GetGenreByID 根据ID获取分类
func (s *javService) GetGenreByID(genreID uint) (*JAVGenreResponse, error) {
	genre, err := s.repo.JAVGenre().GetByID(genreID)
	if err != nil {
		return nil, fmt.Errorf("获取分类失败: %w", err)
	}

	return s.convertGenreToResponse(genre), nil
}

// GetAllGenres 获取所有分类
func (s *javService) GetAllGenres() ([]*JAVGenreResponse, error) {
	genres, _, err := s.repo.JAVGenre().List(0, 1000, nil) // 获取所有分类
	if err != nil {
		return nil, fmt.Errorf("获取分类列表失败: %w", err)
	}

	responses := make([]*JAVGenreResponse, len(genres))
	for i, genre := range genres {
		responses[i] = s.convertGenreToResponse(genre)
	}

	return responses, nil
}

// GetPopularGenres 获取热门分类
func (s *javService) GetPopularGenres(limit int) ([]*JAVGenreResponse, error) {
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	genres, err := s.repo.JAVGenre().GetPopularGenres(limit)
	if err != nil {
		return nil, fmt.Errorf("获取热门分类失败: %w", err)
	}

	responses := make([]*JAVGenreResponse, len(genres))
	for i, genre := range genres {
		responses[i] = s.convertGenreToResponse(genre)
	}

	return responses, nil
}

// GetGenreMovies 获取分类影片
func (s *javService) GetGenreMovies(genreID uint, offset, limit int) ([]*JAVMovieResponse, int64, error) {
	movies, total, err := s.repo.JAVGenre().GetMoviesByGenre(genreID, offset, limit)
	if err != nil {
		return nil, 0, fmt.Errorf("获取分类影片失败: %w", err)
	}

	responses := make([]*JAVMovieResponse, len(movies))
	for i, movie := range movies {
		responses[i] = s.convertMovieToResponse(movie)
	}

	return responses, total, nil
}

// ===== 磁力链接管理 =====

// GetBestMagnet 获取最佳磁力链接
func (s *javService) GetBestMagnet(movieID uint) (*JAVMagnetResponse, error) {
	magnet, err := s.repo.JAVMagnet().GetBestMagnetByMovie(movieID)
	if err != nil {
		return nil, fmt.Errorf("获取最佳磁力链接失败: %w", err)
	}

	return s.convertMagnetToResponse(magnet), nil
}

// GetMagnetsByQuality 根据清晰度获取磁力链接
func (s *javService) GetMagnetsByQuality(quality string, offset, limit int) ([]*JAVMagnetResponse, int64, error) {
	magnets, total, err := s.repo.JAVMagnet().GetByQuality(quality, offset, limit)
	if err != nil {
		return nil, 0, fmt.Errorf("根据清晰度获取磁力链接失败: %w", err)
	}

	responses := make([]*JAVMagnetResponse, len(magnets))
	for i, magnet := range magnets {
		responses[i] = s.convertMagnetToResponse(magnet)
	}

	return responses, total, nil
}

// GetMagnetsBySubtitle 根据字幕获取磁力链接
func (s *javService) GetMagnetsBySubtitle(hasSubtitle bool, offset, limit int) ([]*JAVMagnetResponse, int64, error) {
	magnets, total, err := s.repo.JAVMagnet().GetBySubtitle(hasSubtitle, offset, limit)
	if err != nil {
		return nil, 0, fmt.Errorf("根据字幕获取磁力链接失败: %w", err)
	}

	responses := make([]*JAVMagnetResponse, len(magnets))
	for i, magnet := range magnets {
		responses[i] = s.convertMagnetToResponse(magnet)
	}

	return responses, total, nil
}

// ===== 下载集成 =====

// CreateDownloadFromMagnet 从磁力链接创建下载任务
func (s *javService) CreateDownloadFromMagnet(userID uint, magnetID uint, options *DownloadOptions) (*model.DownloadTask, error) {
	// 获取磁力链接信息
	magnet, err := s.repo.JAVMagnet().GetByID(magnetID)
	if err != nil {
		return nil, fmt.Errorf("获取磁力链接失败: %w", err)
	}

	// 获取影片信息
	movie, err := s.repo.JAVMovie().GetByID(magnet.MovieID)
	if err != nil {
		return nil, fmt.Errorf("获取影片信息失败: %w", err)
	}

	// 创建下载任务
	task := &model.DownloadTask{
		UserID:    userID,
		MagnetURI: magnet.MagnetURL,
		TaskName:  fmt.Sprintf("JAV影片: %s - %s", movie.Code, movie.Title),
		Status:    model.TaskStatusPending,
		Priority:  model.TaskPriority(options.Priority),
		SavePath:  "/downloads/jav", // 默认保存路径
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err = s.repo.Task().Create(task)
	if err != nil {
		return nil, fmt.Errorf("创建下载任务失败: %w", err)
	}

	logger.Infof("创建JAV下载任务成功: 用户%d, 影片%s, 磁力链接%d", userID, movie.Code, magnetID)
	return task, nil
}

// GetDownloadableMovies 获取可下载的影片
func (s *javService) GetDownloadableMovies(offset, limit int) ([]*JAVMovieResponse, int64, error) {
	// 获取有磁力链接的影片
	filters := map[string]interface{}{
		"status": model.JAVScrapingStatusCompleted,
	}

	movies, total, err := s.repo.JAVMovie().List(offset, limit, filters)
	if err != nil {
		return nil, 0, fmt.Errorf("获取可下载影片失败: %w", err)
	}

	// 过滤出有磁力链接的影片
	var downloadableMovies []*JAVMovieResponse
	for _, movie := range movies {
		magnetCount, _ := s.repo.JAVMagnet().CountByMovieID(movie.ID)
		if magnetCount > 0 {
			response := s.convertMovieToResponse(movie)
			response.MagnetCount = int(magnetCount)
			downloadableMovies = append(downloadableMovies, response)
		}
	}

	return downloadableMovies, total, nil
}

// ===== 统计信息 =====

// GetJAVStats 获取JAV统计信息
func (s *javService) GetJAVStats() (*JAVStatsResponse, error) {
	// 获取基础统计
	totalMovies, _ := s.repo.JAVMovie().CountByStatus(model.JAVScrapingStatusCompleted)
	pendingMovies, _ := s.repo.JAVMovie().CountByStatus(model.JAVScrapingStatusPending)
	failedMovies, _ := s.repo.JAVMovie().CountByStatus(model.JAVScrapingStatusFailed)

	// 这里可以添加更多统计逻辑
	stats := &JAVStatsResponse{
		TotalMovies:     totalMovies + pendingMovies + failedMovies,
		CompletedMovies: totalMovies,
		PendingMovies:   pendingMovies,
		FailedMovies:    failedMovies,
		// 其他统计信息可以根据需要添加
	}

	return stats, nil
}

// GetScrapingStats 获取采集统计信息
func (s *javService) GetScrapingStats() (*ScrapingStatsResponse, error) {
	// 获取采集统计
	totalScrapes := int64(0)
	successfulScrapes, _ := s.repo.JAVMovie().CountByStatus(model.JAVScrapingStatusCompleted)
	failedScrapes, _ := s.repo.JAVMovie().CountByStatus(model.JAVScrapingStatusFailed)
	totalScrapes = successfulScrapes + failedScrapes

	var successRate float64
	if totalScrapes > 0 {
		successRate = float64(successfulScrapes) / float64(totalScrapes) * 100
	}

	stats := &ScrapingStatsResponse{
		TotalScrapes:      totalScrapes,
		SuccessfulScrapes: successfulScrapes,
		FailedScrapes:     failedScrapes,
		SuccessRate:       successRate,
		// 其他统计信息可以根据需要添加
	}

	return stats, nil
}

// ===== 辅助方法 =====

// convertMovieToResponse 转换影片模型为响应格式
func (s *javService) convertMovieToResponse(movie *model.JAVMovie) *JAVMovieResponse {
	if movie == nil {
		return nil
	}

	return &JAVMovieResponse{
		ID:             movie.ID,
		Code:           movie.Code,
		Title:          movie.Title,
		TitleEn:        movie.TitleEn,
		Studio:         movie.Studio,
		ReleaseDate:    movie.ReleaseDate,
		Duration:       movie.Duration,
		Rating:         movie.Rating,
		Plot:           movie.Plot,
		PlotEn:         movie.PlotEn,
		CoverURL:       movie.CoverURL,
		PosterURL:      movie.PosterURL,
		StreamTapeURL:  movie.StreamTapeURL,
		StreamHGURL:    movie.StreamHGURL,
		ScrapingStatus: movie.ScrapingStatus,
		ScrapingSource: movie.ScrapingSource,
		CreatedAt:      movie.CreatedAt,
		UpdatedAt:      movie.UpdatedAt,
	}
}

// convertActorToResponse 转换演员模型为响应格式
func (s *javService) convertActorToResponse(actor *model.JAVActor) *JAVActorResponse {
	if actor == nil {
		return nil
	}

	return &JAVActorResponse{
		ID:        actor.ID,
		Name:      actor.Name,
		NameEn:    actor.NameEn,
		NameJp:    actor.NameJp,
		AvatarURL: actor.AvatarURL,
		BirthDate: actor.BirthDate,
		Height:    actor.Height,
		Bust:      actor.Bust,
		Waist:     actor.Waist,
		Hip:       actor.Hip,
		BloodType: actor.BloodType,
		Hobby:     actor.Hobby,
		DebutDate: actor.DebutDate,
		CreatedAt: actor.CreatedAt,
		UpdatedAt: actor.UpdatedAt,
	}
}

// convertGenreToResponse 转换分类模型为响应格式
func (s *javService) convertGenreToResponse(genre *model.JAVGenre) *JAVGenreResponse {
	if genre == nil {
		return nil
	}

	return &JAVGenreResponse{
		ID:          genre.ID,
		Name:        genre.Name,
		NameEn:      genre.NameEn,
		NameJp:      genre.NameJp,
		Description: genre.Description,
		CreatedAt:   genre.CreatedAt,
		UpdatedAt:   time.Time{}, // JAVGenre模型中没有UpdatedAt字段
	}
}

// convertMagnetToResponse 转换磁力链接模型为响应格式
func (s *javService) convertMagnetToResponse(magnet *model.JAVMagnet) *JAVMagnetResponse {
	if magnet == nil {
		return nil
	}

	uploadDate := time.Time{}
	if magnet.UploadDate != nil {
		uploadDate = *magnet.UploadDate
	}

	return &JAVMagnetResponse{
		ID:                magnet.ID,
		MovieID:           magnet.MovieID,
		MagnetURL:         magnet.MagnetURL,
		FileName:          magnet.FileName,
		FileSize:          magnet.FileSize,
		FileSizeFormatted: formatBytes(magnet.FileSize),
		Quality:           string(magnet.Quality),
		HasSubtitle:       magnet.HasSubtitle,
		SubtitleLanguage:  string(magnet.SubtitleLanguage),
		Source:            magnet.Source,
		Uploader:          magnet.Uploader,
		Seeders:           magnet.Seeders,
		Leechers:          magnet.Leechers,
		Score:             magnet.Score,
		UploadDate:        uploadDate,
		CreatedAt:         magnet.CreatedAt,
		UpdatedAt:         magnet.UpdatedAt,
	}
}

// formatBytes 格式化字节大小
func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// GetMovieDownloads 获取影片的下载任务
func (s *javService) GetMovieDownloads(movieID uint) ([]*JAVDownloadResponse, error) {
	// 这里应该调用JAVDownloadService来获取下载任务
	// 由于循环依赖问题，暂时返回空列表
	return []*JAVDownloadResponse{}, nil
}
