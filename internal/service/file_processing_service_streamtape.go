package service

import (
	"fmt"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/pkg/logger"
)

// processStreamTapeUpload StreamTape直接上传处理
func (s *fileProcessingService) processStreamTapeUpload(taskID uint, task *model.DownloadTask, filePath string) {
	logger.WithFields(map[string]interface{}{
		"task_id":   taskID,
		"file_path": filePath,
		"provider":  "streamtape",
	}).Info("Starting StreamTape direct upload")

	s.updateProcessingStatus(taskID, ProcessingStatusUploading, "正在直接上传到StreamTape...")

	// 获取处理任务
	s.mutex.RLock()
	processingTask, exists := s.processingTasks[taskID]
	s.mutex.RUnlock()

	if !exists {
		logger.Errorf("Processing task not found: TaskID=%d", taskID)
		return
	}

	// 检查是否被取消
	select {
	case <-processingTask.CancelChan:
		logger.Infof("Processing cancelled: TaskID=%d", taskID)
		return
	default:
	}

	// 直接上传文件到StreamTape
	uploadResult, err := s.streamTapeClient.UploadFile(filePath)
	if err != nil {
		s.enhancedProcessingError(taskID, task, ProcessingStatusUploading, err, map[string]interface{}{
			"file_path": filePath,
			"provider":  "streamtape",
		})
		return
	}

	if !uploadResult.Success {
		err := fmt.Errorf("StreamTape upload failed: %s", uploadResult.Error)
		s.enhancedProcessingError(taskID, task, ProcessingStatusUploading, err, map[string]interface{}{
			"file_path": filePath,
			"provider":  "streamtape",
		})
		return
	}

	s.logProcessingStep(taskID, ProcessingStatusUploading, "streamtape_upload_completed", map[string]interface{}{
		"file_path": filePath,
		"play_url":  uploadResult.PlayURL,
		"file_code": uploadResult.FileCode,
		"file_size": uploadResult.Size,
	})

	// 更新任务信息
	updateData := map[string]interface{}{
		"play_url":          uploadResult.PlayURL,
		"share_code":        uploadResult.FileCode,
		"file_size":         uploadResult.Size,
		"processing_status": "completed",
		"status":            model.TaskStatusCompleted,
		"completed_at":      time.Now(),
	}
	s.db.Model(&model.DownloadTask{}).Where("id = ?", taskID).Updates(updateData)

	s.logProcessingStep(taskID, ProcessingStatusCompleted, "streamtape_processing_completed", map[string]interface{}{
		"file_path": filePath,
		"play_url":  uploadResult.PlayURL,
		"file_code": uploadResult.FileCode,
		"file_size": uploadResult.Size,
	})

	// 清理处理任务
	s.mutex.Lock()
	delete(s.processingTasks, taskID)
	s.mutex.Unlock()

	logger.WithFields(map[string]interface{}{
		"task_id":  taskID,
		"play_url": uploadResult.PlayURL,
	}).Info("StreamTape upload completed successfully")
}