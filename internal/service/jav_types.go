package service

import (
	"time"

	"magnet-downloader/internal/model"
)

// JAV服务相关的请求和响应结构体

// ===== 影片相关 =====

// JAVMovieResponse 影片响应结构
type JAVMovieResponse struct {
	ID             uint                    `json:"id"`
	Code           string                  `json:"code"`
	Title          string                  `json:"title"`
	TitleEn        string                  `json:"title_en"`
	Studio         string                  `json:"studio"`
	ReleaseDate    *time.Time              `json:"release_date"`
	Duration       int                     `json:"duration"`
	Rating         float64                 `json:"rating"`
	Plot           string                  `json:"plot"`
	PlotEn         string                  `json:"plot_en"`
	CoverURL       string                  `json:"cover_url"`
	PosterURL      string                  `json:"poster_url"`
	StreamTapeURL  string                  `json:"streamtape_url"`
	StreamHGURL    string                  `json:"streamhg_url"`
	ScrapingStatus model.JAVScrapingStatus `json:"scraping_status"`
	ScrapingSource model.JAVScrapingSource `json:"scraping_source"`
	ActorCount     int                     `json:"actor_count"`
	GenreCount     int                     `json:"genre_count"`
	MagnetCount    int                     `json:"magnet_count"`
	BestMagnet     *JAVMagnetResponse      `json:"best_magnet,omitempty"`
	CreatedAt      time.Time               `json:"created_at"`
	UpdatedAt      time.Time               `json:"updated_at"`
}

// HasStreamingLinks 检查是否有播放链接
func (r *JAVMovieResponse) HasStreamingLinks() bool {
	return r.StreamTapeURL != "" || r.StreamHGURL != ""
}

// JAVMovieDetailsResponse 影片详情响应
type JAVMovieDetailsResponse struct {
	*JAVMovieResponse
	Actors  []*JAVActorResponse  `json:"actors"`
	Genres  []*JAVGenreResponse  `json:"genres"`
	Magnets []*JAVMagnetResponse `json:"magnets"`
}

// SearchMoviesRequest 搜索影片请求
type SearchMoviesRequest struct {
	Keyword     string                 `json:"keyword" form:"keyword"`
	Studio      string                 `json:"studio" form:"studio"`
	ActorName   string                 `json:"actor_name" form:"actor_name"`
	GenreName   string                 `json:"genre_name" form:"genre_name"`
	Year        int                    `json:"year" form:"year"`
	RatingMin   float64                `json:"rating_min" form:"rating_min"`
	RatingMax   float64                `json:"rating_max" form:"rating_max"`
	HasSubtitle *bool                  `json:"has_subtitle" form:"has_subtitle"`
	SortBy      string                 `json:"sort_by" form:"sort_by"`
	SortDesc    bool                   `json:"sort_desc" form:"sort_desc"`
	Page        int                    `json:"page" form:"page"`
	PageSize    int                    `json:"page_size" form:"page_size"`
	Filters     map[string]interface{} `json:"filters"`
}

// SearchMoviesResponse 搜索影片响应
type SearchMoviesResponse struct {
	Movies   []*JAVMovieResponse `json:"movies"`
	Total    int64               `json:"total"`
	Page     int                 `json:"page"`
	PageSize int                 `json:"page_size"`
	HasMore  bool                `json:"has_more"`
}

// ===== 演员相关 =====

// JAVActorResponse 演员响应结构
type JAVActorResponse struct {
	ID         uint       `json:"id"`
	Name       string     `json:"name"`
	NameEn     string     `json:"name_en"`
	NameJp     string     `json:"name_jp"`
	AvatarURL  string     `json:"avatar_url"`
	BirthDate  *time.Time `json:"birth_date"`
	Height     int        `json:"height"`
	Bust       int        `json:"bust"`
	Waist      int        `json:"waist"`
	Hip        int        `json:"hip"`
	BloodType  string     `json:"blood_type"`
	Hobby      string     `json:"hobby"`
	DebutDate  *time.Time `json:"debut_date"`
	MovieCount int        `json:"movie_count"`
	CreatedAt  time.Time  `json:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at"`
}

// SearchActorsRequest 搜索演员请求
type SearchActorsRequest struct {
	Keyword   string                 `json:"keyword" form:"keyword"`
	HeightMin int                    `json:"height_min" form:"height_min"`
	HeightMax int                    `json:"height_max" form:"height_max"`
	BloodType string                 `json:"blood_type" form:"blood_type"`
	DebutYear int                    `json:"debut_year" form:"debut_year"`
	SortBy    string                 `json:"sort_by" form:"sort_by"`
	SortDesc  bool                   `json:"sort_desc" form:"sort_desc"`
	Page      int                    `json:"page" form:"page"`
	PageSize  int                    `json:"page_size" form:"page_size"`
	Filters   map[string]interface{} `json:"filters"`
}

// SearchActorsResponse 搜索演员响应
type SearchActorsResponse struct {
	Actors   []*JAVActorResponse `json:"actors"`
	Total    int64               `json:"total"`
	Page     int                 `json:"page"`
	PageSize int                 `json:"page_size"`
	HasMore  bool                `json:"has_more"`
}

// ===== 分类相关 =====

// JAVGenreResponse 分类响应结构
type JAVGenreResponse struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	NameEn      string    `json:"name_en"`
	NameJp      string    `json:"name_jp"`
	Description string    `json:"description"`
	MovieCount  int       `json:"movie_count"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ===== 磁力链接相关 =====

// JAVMagnetResponse 磁力链接响应结构
type JAVMagnetResponse struct {
	ID                uint      `json:"id"`
	MovieID           uint      `json:"movie_id"`
	MovieCode         string    `json:"movie_code,omitempty"`
	MagnetURL         string    `json:"magnet_url"`
	FileName          string    `json:"file_name"`
	FileSize          int64     `json:"file_size"`
	FileSizeFormatted string    `json:"file_size_formatted"`
	Quality           string    `json:"quality"`
	HasSubtitle       bool      `json:"has_subtitle"`
	SubtitleLanguage  string    `json:"subtitle_language"`
	Source            string    `json:"source"`
	Uploader          string    `json:"uploader"`
	Seeders           int       `json:"seeders"`
	Leechers          int       `json:"leechers"`
	Score             float64   `json:"score"`
	UploadDate        time.Time `json:"upload_date"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// ===== 下载相关 =====

// DownloadOptions 下载选项
type DownloadOptions struct {
	Quality          string `json:"quality"`
	PreferSubtitle   bool   `json:"prefer_subtitle"`
	SubtitleLanguage string `json:"subtitle_language"`
	AutoUpload       bool   `json:"auto_upload"`
	UploadPlatform   string `json:"upload_platform"`
	Priority         int    `json:"priority"`
}

// JAVDownloadOptions JAV下载选项
type JAVDownloadOptions struct {
	*DownloadOptions
	UseCustomMagnet bool   `json:"use_custom_magnet"`
	CustomMagnetURL string `json:"custom_magnet_url"`
	DownloadPath    string `json:"download_path"`
	CreateFolder    bool   `json:"create_folder"`
	FolderName      string `json:"folder_name"`
}

// CreateJAVDownloadRequest 创建JAV下载请求
type CreateJAVDownloadRequest struct {
	MovieCode string              `json:"movie_code"`
	MagnetID  *uint               `json:"magnet_id"`
	Options   *JAVDownloadOptions `json:"options"`
}

// JAVDownloadResponse JAV下载响应
type JAVDownloadResponse struct {
	ID           uint                `json:"id"`
	UserID       uint                `json:"user_id"`
	MovieID      uint                `json:"movie_id"`
	MagnetID     uint                `json:"magnet_id"`
	Movie        *JAVMovieResponse   `json:"movie,omitempty"`
	Magnet       *JAVMagnetResponse  `json:"magnet,omitempty"`
	DownloadTask *model.DownloadTask `json:"download_task,omitempty"`
	Status       model.TaskStatus    `json:"status"`
	Progress     float64             `json:"progress"`
	Speed        int64               `json:"speed"`
	ETA          int64               `json:"eta"`
	Error        string              `json:"error,omitempty"`
	Options      *JAVDownloadOptions `json:"options"`
	CreatedAt    time.Time           `json:"created_at"`
	UpdatedAt    time.Time           `json:"updated_at"`
}

// ListJAVDownloadsRequest 列出JAV下载请求
type ListJAVDownloadsRequest struct {
	UserID    uint                   `json:"user_id" form:"user_id"`
	Status    model.TaskStatus       `json:"status" form:"status"`
	MovieCode string                 `json:"movie_code" form:"movie_code"`
	SortBy    string                 `json:"sort_by" form:"sort_by"`
	SortDesc  bool                   `json:"sort_desc" form:"sort_desc"`
	Page      int                    `json:"page" form:"page"`
	PageSize  int                    `json:"page_size" form:"page_size"`
	Filters   map[string]interface{} `json:"filters"`
}

// UpdateJAVDownloadRequest 更新JAV下载请求
type UpdateJAVDownloadRequest struct {
	Options  *JAVDownloadOptions `json:"options"`
	Priority *int                `json:"priority"`
}

// BatchJAVDownloadResponse 批量JAV下载响应
type BatchJAVDownloadResponse struct {
	SuccessCount int      `json:"success_count"`
	FailureCount int      `json:"failure_count"`
	Errors       []string `json:"errors,omitempty"`
}

// ===== 采集相关 =====

// ScrapeMovieResponse 采集影片响应
type ScrapeMovieResponse struct {
	Success      bool              `json:"success"`
	MovieInfo    *JAVMovieResponse `json:"movie_info,omitempty"`
	Source       string            `json:"source"`
	Duration     time.Duration     `json:"duration"`
	Error        string            `json:"error,omitempty"`
	Confidence   float64           `json:"confidence"`
	ActorsFound  int               `json:"actors_found"`
	GenresFound  int               `json:"genres_found"`
	MagnetsFound int               `json:"magnets_found"`
}

// MultiSourceScrapeResponse 多源采集响应
type MultiSourceScrapeResponse struct {
	MovieCode      string                 `json:"movie_code"`
	Sources        []*ScrapeMovieResponse `json:"sources"`
	MergedResult   *JAVMovieResponse      `json:"merged_result,omitempty"`
	BestSource     string                 `json:"best_source"`
	TotalSources   int                    `json:"total_sources"`
	SuccessSources int                    `json:"success_sources"`
	MergeSuccess   bool                   `json:"merge_success"`
	TotalDuration  time.Duration          `json:"total_duration"`
}

// BatchScrapeResponse 批量采集响应
type BatchScrapeResponse struct {
	TotalCodes    int                    `json:"total_codes"`
	SuccessCount  int                    `json:"success_count"`
	FailureCount  int                    `json:"failure_count"`
	Results       []*ScrapeMovieResponse `json:"results"`
	Errors        []string               `json:"errors,omitempty"`
	TotalDuration time.Duration          `json:"total_duration"`
}

// CreateScrapeTaskRequest 创建采集任务请求
type CreateScrapeTaskRequest struct {
	MovieCodes []string `json:"movie_codes"`
	Sources    []string `json:"sources"`
	Priority   int      `json:"priority"`
	AutoMerge  bool     `json:"auto_merge"`
	AutoRetry  bool     `json:"auto_retry"`
	MaxRetries int      `json:"max_retries"`
}

// ScrapeTaskResponse 采集任务响应
type ScrapeTaskResponse struct {
	ID              uint                    `json:"id"`
	MovieCodes      []string                `json:"movie_codes"`
	Sources         []string                `json:"sources"`
	Status          model.JAVScrapingStatus `json:"status"`
	Progress        float64                 `json:"progress"`
	TotalMovies     int                     `json:"total_movies"`
	CompletedMovies int                     `json:"completed_movies"`
	FailedMovies    int                     `json:"failed_movies"`
	Results         []*ScrapeMovieResponse  `json:"results,omitempty"`
	Error           string                  `json:"error,omitempty"`
	CreatedAt       time.Time               `json:"created_at"`
	UpdatedAt       time.Time               `json:"updated_at"`
}

// ListScrapeTasksRequest 列出采集任务请求
type ListScrapeTasksRequest struct {
	Status   model.JAVScrapingStatus `json:"status" form:"status"`
	Source   string                  `json:"source" form:"source"`
	SortBy   string                  `json:"sort_by" form:"sort_by"`
	SortDesc bool                    `json:"sort_desc" form:"sort_desc"`
	Page     int                     `json:"page" form:"page"`
	PageSize int                     `json:"page_size" form:"page_size"`
}

// MergeDataResponse 数据融合响应
type MergeDataResponse struct {
	Success      bool              `json:"success"`
	MovieID      uint              `json:"movie_id"`
	SourcesUsed  []string          `json:"sources_used"`
	MergedMovie  *JAVMovieResponse `json:"merged_movie,omitempty"`
	Confidence   float64           `json:"confidence"`
	ChangesCount int               `json:"changes_count"`
	Error        string            `json:"error,omitempty"`
	Duration     time.Duration     `json:"duration"`
}

// RefreshDataResponse 刷新数据响应
type RefreshDataResponse struct {
	Success      bool              `json:"success"`
	MovieID      uint              `json:"movie_id"`
	UpdatedMovie *JAVMovieResponse `json:"updated_movie,omitempty"`
	ChangesCount int               `json:"changes_count"`
	Error        string            `json:"error,omitempty"`
	Duration     time.Duration     `json:"duration"`
}

// ===== 配置相关 =====

// ScrapingConfigRequest 采集配置请求
type ScrapingConfigRequest struct {
	JavBusEnabled    bool          `json:"javbus_enabled"`
	JavinizerEnabled bool          `json:"javinizer_enabled"`
	JavSPEnabled     bool          `json:"javsp_enabled"`
	Timeout          time.Duration `json:"timeout"`
	RateLimit        time.Duration `json:"rate_limit"`
	MaxRetries       int           `json:"max_retries"`
	AutoMerge        bool          `json:"auto_merge"`
	MinConfidence    float64       `json:"min_confidence"`
}

// ScrapingConfigResponse 采集配置响应
type ScrapingConfigResponse struct {
	*ScrapingConfigRequest
	UpdatedAt time.Time `json:"updated_at"`
}

// SourceTestResponse 数据源测试响应
type SourceTestResponse struct {
	Source       string               `json:"source"`
	Available    bool                 `json:"available"`
	ResponseTime time.Duration        `json:"response_time"`
	Error        string               `json:"error,omitempty"`
	TestCode     string               `json:"test_code"`
	TestResult   *ScrapeMovieResponse `json:"test_result,omitempty"`
}

// ===== 统计相关 =====

// JAVStatsResponse JAV统计响应
type JAVStatsResponse struct {
	TotalMovies          int64            `json:"total_movies"`
	TotalActors          int64            `json:"total_actors"`
	TotalGenres          int64            `json:"total_genres"`
	TotalMagnets         int64            `json:"total_magnets"`
	CompletedMovies      int64            `json:"completed_movies"`
	PendingMovies        int64            `json:"pending_movies"`
	FailedMovies         int64            `json:"failed_movies"`
	AvgRating            float64          `json:"avg_rating"`
	AvgMagnetsPerMovie   float64          `json:"avg_magnets_per_movie"`
	TopStudios           []StudioStats    `json:"top_studios"`
	TopActors            []ActorStats     `json:"top_actors"`
	TopGenres            []GenreStats     `json:"top_genres"`
	QualityDistribution  map[string]int64 `json:"quality_distribution"`
	SubtitleDistribution map[string]int64 `json:"subtitle_distribution"`
}

// ScrapingStatsResponse 采集统计响应
type ScrapingStatsResponse struct {
	TotalScrapes      int64                   `json:"total_scrapes"`
	SuccessfulScrapes int64                   `json:"successful_scrapes"`
	FailedScrapes     int64                   `json:"failed_scrapes"`
	SuccessRate       float64                 `json:"success_rate"`
	AvgScrapeTime     float64                 `json:"avg_scrape_time"`
	SourceStats       map[string]*SourceStats `json:"source_stats"`
	RecentScrapes     []*ScrapeTaskResponse   `json:"recent_scrapes"`
	DailyScrapes      map[string]int64        `json:"daily_scrapes"`
}

// ScrapingStatisticsResponse 采集统计详情响应
type ScrapingStatisticsResponse struct {
	*ScrapingStatsResponse
	TotalDataSources int                `json:"total_data_sources"`
	ActiveSources    []string           `json:"active_sources"`
	SourceHealth     map[string]float64 `json:"source_health"`
	LastScrapeTime   time.Time          `json:"last_scrape_time"`
}

// SourceStatisticsResponse 数据源统计响应
type SourceStatisticsResponse struct {
	Source          string    `json:"source"`
	TotalScrapes    int64     `json:"total_scrapes"`
	SuccessCount    int64     `json:"success_count"`
	FailureCount    int64     `json:"failure_count"`
	SuccessRate     float64   `json:"success_rate"`
	AvgResponseTime float64   `json:"avg_response_time"`
	LastScrapeTime  time.Time `json:"last_scrape_time"`
	IsHealthy       bool      `json:"is_healthy"`
	ErrorRate       float64   `json:"error_rate"`
}

// JAVDownloadStatsResponse JAV下载统计响应
type JAVDownloadStatsResponse struct {
	TotalDownloads     int64            `json:"total_downloads"`
	ActiveDownloads    int64            `json:"active_downloads"`
	CompletedDownloads int64            `json:"completed_downloads"`
	FailedDownloads    int64            `json:"failed_downloads"`
	SuccessRate        float64          `json:"success_rate"`
	TotalSize          int64            `json:"total_size"`
	AvgDownloadTime    float64          `json:"avg_download_time"`
	PopularQualities   map[string]int64 `json:"popular_qualities"`
	DailyDownloads     map[string]int64 `json:"daily_downloads"`
}

// UserJAVDownloadStatsResponse 用户JAV下载统计响应
type UserJAVDownloadStatsResponse struct {
	UserID             uint                   `json:"user_id"`
	TotalDownloads     int64                  `json:"total_downloads"`
	CompletedDownloads int64                  `json:"completed_downloads"`
	FailedDownloads    int64                  `json:"failed_downloads"`
	SuccessRate        float64                `json:"success_rate"`
	TotalSize          int64                  `json:"total_size"`
	FavoriteQualities  []string               `json:"favorite_qualities"`
	RecentDownloads    []*JAVDownloadResponse `json:"recent_downloads"`
}

// ===== 辅助统计结构 =====

// StudioStats 工作室统计
type StudioStats struct {
	Name       string  `json:"name"`
	MovieCount int64   `json:"movie_count"`
	AvgRating  float64 `json:"avg_rating"`
}

// ActorStats 演员统计
type ActorStats struct {
	ID         uint    `json:"id"`
	Name       string  `json:"name"`
	MovieCount int64   `json:"movie_count"`
	AvgRating  float64 `json:"avg_rating"`
}

// GenreStats 分类统计
type GenreStats struct {
	ID         uint    `json:"id"`
	Name       string  `json:"name"`
	MovieCount int64   `json:"movie_count"`
	AvgRating  float64 `json:"avg_rating"`
}

// SourceStats 数据源统计
type SourceStats struct {
	Source       string  `json:"source"`
	ScrapeCount  int64   `json:"scrape_count"`
	SuccessCount int64   `json:"success_count"`
	SuccessRate  float64 `json:"success_rate"`
	AvgTime      float64 `json:"avg_time"`
}
