package service

import (
	"fmt"
	"strings"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/internal/repository"
	"magnet-downloader/pkg/logger"
)

// taskService 任务服务实现
type taskService struct {
	repo            repository.Repository
	downloadService DownloadService
	queueService    QueueService
}

// NewTaskService 创建任务服务
func NewTaskService(repo repository.Repository, downloadService DownloadService, queueService QueueService) TaskService {
	return &taskService{
		repo:            repo,
		downloadService: downloadService,
		queueService:    queueService,
	}
}

// CreateTask 创建任务
func (ts *taskService) CreateTask(userID uint, req *CreateTaskRequest) (*model.DownloadTask, error) {
	// 验证用户权限
	user, err := ts.repo.User().GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	if !user.CanDownload() {
		return nil, fmt.Errorf("user cannot download")
	}

	// 验证磁力链接格式
	if err := ts.validateMagnetURI(req.MagnetURI); err != nil {
		return nil, fmt.Errorf("invalid magnet URI: %w", err)
	}

	// 检查用户活跃任务数量限制
	activeTasks, err := ts.repo.Task().GetActiveTasksByUser(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get active tasks: %w", err)
	}

	maxConcurrent := 5 // 从配置获取
	if len(activeTasks) >= maxConcurrent {
		return nil, fmt.Errorf("maximum concurrent downloads (%d) reached", maxConcurrent)
	}

	// 设置默认值
	if req.Priority == 0 {
		req.Priority = model.TaskPriorityNormal
	}
	if req.SavePath == "" {
		req.SavePath = "/downloads" // 从配置获取默认路径
	}

	// 创建任务
	task := &model.DownloadTask{
		MagnetURI:  req.MagnetURI,
		TaskName:   req.TaskName,
		Status:     model.TaskStatusPending,
		Priority:   req.Priority,
		UserID:     userID,
		SavePath:   req.SavePath,
		MaxRetries: 3,
	}

	// 保存到数据库
	if err := ts.repo.Task().Create(task); err != nil {
		return nil, fmt.Errorf("failed to create task: %w", err)
	}

	// 添加到队列
	if err := ts.queueService.EnqueueDownloadTask(task); err != nil {
		logger.Errorf("Failed to enqueue task %d: %v", task.ID, err)
	}

	logger.Infof("Created task: ID=%d, User=%d, URI=%s", task.ID, userID, req.MagnetURI)
	return task, nil
}

// GetTask 获取任务
func (ts *taskService) GetTask(taskID uint) (*model.DownloadTask, error) {
	task, err := ts.repo.Task().GetByID(taskID)
	if err != nil {
		return nil, fmt.Errorf("task not found: %w", err)
	}
	return task, nil
}

// UpdateTask 更新任务
func (ts *taskService) UpdateTask(taskID uint, req *UpdateTaskRequest) (*model.DownloadTask, error) {
	task, err := ts.repo.Task().GetByID(taskID)
	if err != nil {
		return nil, fmt.Errorf("task not found: %w", err)
	}

	// 检查任务是否可以更新
	if task.Status == model.TaskStatusRunning {
		return nil, fmt.Errorf("cannot update running task")
	}

	// 更新字段
	if req.TaskName != "" {
		task.TaskName = req.TaskName
	}
	if req.Priority != 0 {
		task.Priority = req.Priority
	}
	if req.SavePath != "" {
		task.SavePath = req.SavePath
	}

	if err := ts.repo.Task().Update(task); err != nil {
		return nil, fmt.Errorf("failed to update task: %w", err)
	}

	logger.Infof("Updated task: ID=%d", taskID)
	return task, nil
}

// DeleteTask 删除任务
func (ts *taskService) DeleteTask(taskID uint) error {
	task, err := ts.repo.Task().GetByID(taskID)
	if err != nil {
		return fmt.Errorf("task not found: %w", err)
	}

	// 如果任务正在运行，先取消
	if task.Status == model.TaskStatusRunning {
		if err := ts.downloadService.CancelDownload(taskID); err != nil {
			logger.Warnf("Failed to cancel running task %d: %v", taskID, err)
		}
	}

	if err := ts.repo.Task().Delete(taskID); err != nil {
		return fmt.Errorf("failed to delete task: %w", err)
	}

	logger.Infof("Deleted task: ID=%d", taskID)
	return nil
}

// ListTasks 获取任务列表
func (ts *taskService) ListTasks(req *ListTasksRequest) ([]*model.DownloadTask, int64, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	offset := (req.Page - 1) * req.PageSize
	filters := ts.buildFilters(req)

	tasks, total, err := ts.repo.Task().List(offset, req.PageSize, filters)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list tasks: %w", err)
	}

	return tasks, total, nil
}

// GetUserTasks 获取用户任务
func (ts *taskService) GetUserTasks(userID uint, req *ListTasksRequest) ([]*model.DownloadTask, int64, error) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	offset := (req.Page - 1) * req.PageSize
	tasks, total, err := ts.repo.Task().GetByUserID(userID, offset, req.PageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get user tasks: %w", err)
	}

	return tasks, total, nil
}

// GetTasksByStatus 根据状态获取任务
func (ts *taskService) GetTasksByStatus(status model.TaskStatus, req *ListTasksRequest) ([]*model.DownloadTask, int64, error) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	offset := (req.Page - 1) * req.PageSize
	tasks, total, err := ts.repo.Task().GetByStatus(status, offset, req.PageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get tasks by status: %w", err)
	}

	return tasks, total, nil
}

// StartTask 开始任务
func (ts *taskService) StartTask(taskID uint) error {
	return ts.downloadService.StartDownload(taskID)
}

// PauseTask 暂停任务
func (ts *taskService) PauseTask(taskID uint) error {
	return ts.downloadService.PauseDownload(taskID)
}

// ResumeTask 恢复任务
func (ts *taskService) ResumeTask(taskID uint) error {
	return ts.downloadService.ResumeDownload(taskID)
}

// CancelTask 取消任务
func (ts *taskService) CancelTask(taskID uint) error {
	return ts.downloadService.CancelDownload(taskID)
}

// RetryTask 重试任务
func (ts *taskService) RetryTask(taskID uint) error {
	return ts.downloadService.RetryDownload(taskID)
}

// RestartTask 重启任务
func (ts *taskService) RestartTask(taskID uint) error {
	// 先取消任务，然后重新开始
	if err := ts.CancelTask(taskID); err != nil {
		return fmt.Errorf("failed to cancel task before restart: %w", err)
	}
	
	// 等待一小段时间确保取消完成
	time.Sleep(1 * time.Second)
	
	return ts.StartTask(taskID)
}

// FailTask 标记任务为失败
func (ts *taskService) FailTask(taskID uint) error {
	task, err := ts.GetTask(taskID)
	if err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}
	
	// 更新任务状态为失败
	updateReq := &UpdateTaskRequest{
		TaskName: task.TaskName,
		Priority: task.Priority,
		SavePath: task.SavePath,
	}
	
	_, err = ts.UpdateTask(taskID, updateReq)
	if err != nil {
		return fmt.Errorf("failed to update task status: %w", err)
	}
	
	return nil
}

// GetStuckTasks 获取卡住的任务
func (ts *taskService) GetStuckTasks(threshold time.Duration) ([]*model.DownloadTask, error) {
	// 获取所有进行中的任务
	req := &ListTasksRequest{
		Status:   model.TaskStatusRunning,
		Page:     1,
		PageSize: 1000, // 获取大量任务
	}
	
	tasks, _, err := ts.GetTasksByStatus(model.TaskStatusRunning, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get downloading tasks: %w", err)
	}
	
	var stuckTasks []*model.DownloadTask
	cutoffTime := time.Now().Add(-threshold)
	
	for _, task := range tasks {
		// 如果任务更新时间超过阈值，认为是卡住的任务
		if task.UpdatedAt.Before(cutoffTime) {
			stuckTasks = append(stuckTasks, task)
		}
	}
	
	return stuckTasks, nil
}

// BatchStart 批量开始
func (ts *taskService) BatchStart(taskIDs []uint) (*BatchOperationResult, error) {
	return ts.batchOperation(taskIDs, "start", func(taskID uint) error {
		return ts.StartTask(taskID)
	})
}

// BatchPause 批量暂停
func (ts *taskService) BatchPause(taskIDs []uint) (*BatchOperationResult, error) {
	return ts.batchOperation(taskIDs, "pause", func(taskID uint) error {
		return ts.PauseTask(taskID)
	})
}

// BatchResume 批量恢复
func (ts *taskService) BatchResume(taskIDs []uint) (*BatchOperationResult, error) {
	return ts.batchOperation(taskIDs, "resume", func(taskID uint) error {
		return ts.ResumeTask(taskID)
	})
}

// BatchCancel 批量取消
func (ts *taskService) BatchCancel(taskIDs []uint) (*BatchOperationResult, error) {
	return ts.batchOperation(taskIDs, "cancel", func(taskID uint) error {
		return ts.CancelTask(taskID)
	})
}

// BatchDelete 批量删除
func (ts *taskService) BatchDelete(taskIDs []uint) (*BatchOperationResult, error) {
	return ts.batchOperation(taskIDs, "delete", func(taskID uint) error {
		return ts.DeleteTask(taskID)
	})
}

// GetTaskStats 获取任务统计
func (ts *taskService) GetTaskStats() (*TaskStats, error) {
	stats := &TaskStats{}

	// 获取各状态任务数量
	if count, err := ts.repo.Task().CountByStatus(model.TaskStatusPending); err == nil {
		stats.PendingTasks = count
		stats.TotalTasks += count
	}
	if count, err := ts.repo.Task().CountByStatus(model.TaskStatusRunning); err == nil {
		stats.ActiveTasks = count
		stats.TotalTasks += count
	}
	if count, err := ts.repo.Task().CountByStatus(model.TaskStatusCompleted); err == nil {
		stats.CompletedTasks = count
		stats.TotalTasks += count
	}
	if count, err := ts.repo.Task().CountByStatus(model.TaskStatusFailed); err == nil {
		stats.FailedTasks = count
		stats.TotalTasks += count
	}
	if count, err := ts.repo.Task().CountByStatus(model.TaskStatusPaused); err == nil {
		stats.PausedTasks = count
		stats.TotalTasks += count
	}
	if count, err := ts.repo.Task().CountByStatus(model.TaskStatusCancelled); err == nil {
		stats.CancelledTasks = count
		stats.TotalTasks += count
	}

	// 计算成功率
	if stats.TotalTasks > 0 {
		stats.SuccessRate = float64(stats.CompletedTasks) / float64(stats.TotalTasks) * 100
	}

	return stats, nil
}

// GetUserTaskStats 获取用户任务统计
func (ts *taskService) GetUserTaskStats(userID uint) (*UserTaskStats, error) {
	stats := &UserTaskStats{
		UserID: userID,
	}

	// 获取用户任务总数
	if total, err := ts.repo.Task().CountByUserID(userID); err == nil {
		stats.TotalTasks = total
	}

	// 获取用户活跃任务
	if activeTasks, err := ts.repo.Task().GetActiveTasksByUser(userID); err == nil {
		stats.ActiveTasks = int64(len(activeTasks))
	}

	// 这里可以添加更多统计逻辑
	return stats, nil
}

// 辅助方法

// validateMagnetURI 验证磁力链接格式
func (ts *taskService) validateMagnetURI(magnetURI string) error {
	if magnetURI == "" {
		return fmt.Errorf("magnet URI is required")
	}
	if len(magnetURI) < 20 || !strings.HasPrefix(magnetURI, "magnet:?") {
		return fmt.Errorf("invalid magnet URI format")
	}
	return nil
}

// buildFilters 构建查询过滤器
func (ts *taskService) buildFilters(req *ListTasksRequest) map[string]interface{} {
	filters := make(map[string]interface{})

	if req.Status != "" {
		filters["status"] = req.Status
	}
	if req.Priority != 0 {
		filters["priority"] = req.Priority
	}
	if req.Search != "" {
		filters["search"] = req.Search
	}

	// 添加自定义过滤器
	for key, value := range req.Filters {
		filters[key] = value
	}

	return filters
}

// batchOperation 批量操作辅助方法
func (ts *taskService) batchOperation(taskIDs []uint, operation string, fn func(uint) error) (*BatchOperationResult, error) {
	result := &BatchOperationResult{
		Total: len(taskIDs),
	}

	for _, taskID := range taskIDs {
		if err := fn(taskID); err != nil {
			result.Failed++
			result.Errors = append(result.Errors, fmt.Sprintf("Task %d: %v", taskID, err))
			result.FailedIDs = append(result.FailedIDs, taskID)
		} else {
			result.Success++
			result.SuccessIDs = append(result.SuccessIDs, taskID)
		}
	}

	logger.Infof("Batch %s operation: Total=%d, Success=%d, Failed=%d",
		operation, result.Total, result.Success, result.Failed)

	return result, nil
}
