package service

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/model"
	"magnet-downloader/internal/websocket"
	"magnet-downloader/pkg/fileprocessor"
	"magnet-downloader/pkg/imgbb"
	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/mixfile"
	"magnet-downloader/pkg/streaming"
	"magnet-downloader/pkg/streamtape"

	"gorm.io/gorm"
)

// 处理状态常量
const (
	ProcessingStatusInitializing    = "initializing"     // 初始化
	ProcessingStatusFileChecking    = "file_checking"    // 文件检查
	ProcessingStatusChunking        = "chunking"         // 分片处理
	ProcessingStatusEncrypting      = "encrypting"       // 加密处理
	ProcessingStatusUploading       = "uploading"        // 上传处理
	ProcessingStatusGeneratingIndex = "generating_index" // 生成索引文件
	ProcessingStatusUploadingIndex  = "uploading_index"  // 上传索引文件
	ProcessingStatusGeneratingShare = "generating_share" // 生成分享码
	ProcessingStatusCompleted       = "completed"        // 处理完成
	ProcessingStatusFailed          = "failed"           // 处理失败
)

// fileProcessingService 文件处理服务实现
type fileProcessingService struct {
	db               *gorm.DB
	websocketSvc     *websocket.Service
	processor        *fileprocessor.Processor
	imgbbClient      *imgbb.Client
	streamTapeClient *streamtape.Client
	playlistGen      *streaming.PlaylistGenerator
	processingTasks  map[uint]*ProcessingTask // 正在处理的任务
	mutex            sync.RWMutex

	// 配置相关
	uploadProvider string
	mixFileConfig  *config.MixFileConfig

	// MixFile相关组件
	indexManager       mixfile.IndexManager
	shareCodeProcessor *mixfile.ShareCodeProcessor
}

// ProcessingTask 处理任务
type ProcessingTask struct {
	TaskID     uint                           `json:"task_id"`
	Status     string                         `json:"status"`
	StartTime  time.Time                      `json:"start_time"`
	Progress   float64                        `json:"progress"`
	Stage      string                         `json:"stage"`
	CancelChan chan bool                      `json:"-"`
	ProgressCb fileprocessor.ProgressCallback `json:"-"`
}

// NewFileProcessingService 创建文件处理服务
func NewFileProcessingService(
	db *gorm.DB,
	websocketSvc *websocket.Service,
	processorConfig *fileprocessor.ProcessingConfig,
	imgbbConfig *imgbb.Config,
	streamTapeConfig *streamtape.Config,
	uploadProvider string,
	playlistConfig *streaming.PlaylistConfig,
	mixFileConfig *config.MixFileConfig,
) FileProcessingService {

	processor := fileprocessor.NewProcessor(processorConfig)
	imgbbClient := imgbb.NewClient(imgbbConfig)

	// 根据配置创建StreamTape客户端
	var streamTapeClient *streamtape.Client
	if (uploadProvider == "streamtape" || uploadProvider == "dual") && streamTapeConfig != nil {
		streamTapeClient = streamtape.NewClient(streamTapeConfig)
		logger.Infof("StreamTape client initialized for upload provider: %s", uploadProvider)
	}

	playlistGen := streaming.NewPlaylistGenerator(playlistConfig)

	// 初始化MixFile组件
	var indexManager mixfile.IndexManager
	var shareCodeProcessor *mixfile.ShareCodeProcessor

	if mixFileConfig != nil && mixFileConfig.Enabled {
		indexManager = mixfile.NewIndexManager()
		shareCodeProcessor = mixfile.NewShareCodeProcessor(mixFileConfig.EnableIndexCompression)
		logger.Info("MixFile functionality enabled")
	}

	return &fileProcessingService{
		db:                 db,
		websocketSvc:       websocketSvc,
		processor:          processor,
		imgbbClient:        imgbbClient,
		streamTapeClient:   streamTapeClient,
		uploadProvider:     uploadProvider,
		playlistGen:        playlistGen,
		processingTasks:    make(map[uint]*ProcessingTask),
		mixFileConfig:      mixFileConfig,
		indexManager:       indexManager,
		shareCodeProcessor: shareCodeProcessor,
	}
}

// StartProcessing 开始处理文件
func (s *fileProcessingService) StartProcessing(taskID uint) error {
	// 获取任务信息
	var task model.DownloadTask
	if err := s.db.First(&task, taskID).Error; err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}

	// 检查任务状态
	if !task.CanStartProcessing() {
		return fmt.Errorf("task cannot start processing: status=%s", task.Status)
	}

	// 预检查文件路径和存在性
	filePath, err := s.GetActualFilePath(&task)
	if err != nil {
		return fmt.Errorf("failed to get file path: %w", err)
	}

	if err := s.ValidateFilePath(filePath, taskID); err != nil {
		return fmt.Errorf("file validation failed: %w", err)
	}

	logger.Infof("Pre-validation passed for task %d: %s", taskID, filePath)

	// 检查是否已在处理中
	s.mutex.RLock()
	if _, exists := s.processingTasks[taskID]; exists {
		s.mutex.RUnlock()
		return fmt.Errorf("task is already being processed")
	}
	s.mutex.RUnlock()

	// 更新任务状态
	task.Status = model.TaskStatusProcessing
	task.ProcessingStatus = "starting"
	if err := s.db.Save(&task).Error; err != nil {
		return fmt.Errorf("failed to update task status: %w", err)
	}

	// 通知WebSocket
	s.websocketSvc.NotifyTaskProcessing(&task)

	// 创建处理任务
	processingTask := &ProcessingTask{
		TaskID:     taskID,
		Status:     "running",
		StartTime:  time.Now(),
		Progress:   0,
		Stage:      "starting",
		CancelChan: make(chan bool, 1),
	}

	// 设置进度回调
	processingTask.ProgressCb = func(stage string, progress float64, message string) {
		processingTask.Stage = stage
		processingTask.Progress = progress

		// 更新数据库
		updateData := map[string]interface{}{
			"processing_status":   stage,
			"processing_progress": progress,
		}
		s.db.Model(&model.DownloadTask{}).Where("id = ?", taskID).Updates(updateData)

		// 发送WebSocket通知
		progressData := &websocket.FileProcessingProgressData{
			TaskID:          taskID,
			TaskName:        task.TaskName,
			Stage:           stage,
			Progress:        progress,
			OverallProgress: progress, // 简化版本，后续可以细化
			Message:         message,
		}
		s.websocketSvc.NotifyFileProcessingProgress(task.UserID, progressData)

		logger.Debugf("Processing progress: TaskID=%d, Stage=%s, Progress=%.2f%%", taskID, stage, progress)
	}

	// 注册处理任务
	s.mutex.Lock()
	s.processingTasks[taskID] = processingTask
	s.mutex.Unlock()

	// 启动异步处理
	go s.processFileAsync(taskID, &task)

	logger.Infof("Started file processing: TaskID=%d", taskID)
	return nil
}

// processFileAsync 异步处理文件
func (s *fileProcessingService) processFileAsync(taskID uint, task *model.DownloadTask) {
	defer func() {
		// 清理处理任务
		s.mutex.Lock()
		delete(s.processingTasks, taskID)
		s.mutex.Unlock()
	}()

	// 检查处理任务是否存在
	s.mutex.RLock()
	_, exists := s.processingTasks[taskID]
	s.mutex.RUnlock()

	if !exists {
		logger.Errorf("Processing task not found: TaskID=%d", taskID)
		return
	}

	// 阶段1：文件路径解析和检查
	logger.WithFields(map[string]interface{}{
		"task_id":   taskID,
		"task_name": task.TaskName,
		"stage":     "file_checking",
	}).Info("Starting file path resolution and validation")

	s.updateProcessingStatus(taskID, ProcessingStatusFileChecking, "正在解析文件路径...")

	// 获取实际文件路径
	filePath, err := s.GetActualFilePath(task)
	if err != nil {
		s.enhancedProcessingError(taskID, task, ProcessingStatusFileChecking, err, map[string]interface{}{
			"has_actual_files": task.HasActualFiles(),
			"save_path":        task.SavePath,
			"task_name":        task.TaskName,
		})
		return
	}

	s.logProcessingStep(taskID, ProcessingStatusFileChecking, "file_path_resolved", map[string]interface{}{
		"file_path":        filePath,
		"has_actual_files": task.HasActualFiles(),
	})

	// 验证文件路径
	if err := s.ValidateFilePath(filePath, taskID); err != nil {
		s.enhancedProcessingError(taskID, task, ProcessingStatusFileChecking, err, map[string]interface{}{
			"file_path": filePath,
		})
		return
	}

	s.logProcessingStep(taskID, ProcessingStatusFileChecking, "file_validation_passed", map[string]interface{}{
		"file_path": filePath,
	})

	// 根据上传提供商选择处理方式
	switch s.uploadProvider {
	case "streamtape":
		// StreamTape直接上传处理
		s.processStreamTapeUpload(taskID, task, filePath)
		return
	default:
		// 传统分片上传处理
		s.processImgBBUpload(taskID, task, filePath)
		return
	}
}


// processImgBBUpload 传统分片上传处理
func (s *fileProcessingService) processImgBBUpload(taskID uint, task *model.DownloadTask, filePath string) {
	// 获取处理任务
	s.mutex.RLock()
	processingTask, exists := s.processingTasks[taskID]
	s.mutex.RUnlock()

	if !exists {
		logger.Errorf("Processing task not found: TaskID=%d", taskID)
		return
	}

	// 阶段2：文件分片和加密处理
	logger.WithFields(map[string]interface{}{
		"task_id":   taskID,
		"file_path": filePath,
		"stage":     "chunking_encrypting",
	}).Info("Starting file chunking and encryption")

	s.updateProcessingStatus(taskID, ProcessingStatusChunking, "正在进行文件分片...")

	// 创建增强的进度回调
	enhancedProgressCb := func(stage string, progress float64, message string) {
		// 调用原始回调
		if processingTask.ProgressCb != nil {
			processingTask.ProgressCb(stage, progress, message)
		}

		// 记录详细进度日志
		s.logProcessingStep(taskID, stage, "progress_update", map[string]interface{}{
			"progress": progress,
			"message":  message,
		})

		// 根据阶段更新状态
		var status string
		switch stage {
		case "chunking":
			status = ProcessingStatusChunking
		case "encrypting":
			status = ProcessingStatusEncrypting
		default:
			status = ProcessingStatusChunking
		}
		s.updateProcessingStatus(taskID, status, message)
	}

	// 第一阶段：文件分片和加密
	result, err := s.processor.ProcessFile(filePath, enhancedProgressCb)
	if err != nil {
		s.enhancedProcessingError(taskID, task, ProcessingStatusChunking, err, map[string]interface{}{
			"file_path": filePath,
		})
		return
	}

	s.logProcessingStep(taskID, ProcessingStatusEncrypting, "file_processing_completed", map[string]interface{}{
		"file_path":          filePath,
		"chunk_count":        result.ChunkCount,
		"total_size":         result.TotalSize,
		"work_dir":           result.WorkDir,
		"has_encryption_key": result.EncryptionKey != "",
	})

	// 检查是否被取消
	select {
	case <-processingTask.CancelChan:
		logger.Infof("Processing cancelled: TaskID=%d", taskID)
		return
	default:
	}

	// 更新任务信息
	updateData := map[string]interface{}{
		"chunk_count":         len(result.Chunks),
		"encryption_key":      result.EncryptionKey,
		"processing_work_dir": result.WorkDir,
		"processing_status":   "uploading",
		"status":              model.TaskStatusUploading,
	}
	s.db.Model(&model.DownloadTask{}).Where("id = ?", taskID).Updates(updateData)

	logger.Infof("Updated task %d with work directory: %s", taskID, result.WorkDir)

	// 阶段3：上传分片
	logger.WithFields(map[string]interface{}{
		"task_id":     taskID,
		"chunk_count": result.ChunkCount,
		"work_dir":    result.WorkDir,
		"stage":       "uploading",
	}).Info("Starting chunk upload")

	s.updateProcessingStatus(taskID, ProcessingStatusUploading, "正在上传分片文件...")

	// 通知上传开始
	s.websocketSvc.NotifyTaskUploading(task)

	// 第二阶段：上传分片
	if err := s.uploadChunks(taskID, task, result); err != nil {
		s.enhancedProcessingError(taskID, task, ProcessingStatusUploading, err, map[string]interface{}{
			"chunk_count": result.ChunkCount,
			"work_dir":    result.WorkDir,
		})
		return
	}

	s.logProcessingStep(taskID, ProcessingStatusUploading, "chunk_upload_completed", map[string]interface{}{
		"chunk_count": result.ChunkCount,
	})

	// 检查是否被取消
	select {
	case <-processingTask.CancelChan:
		logger.Infof("Processing cancelled: TaskID=%d", taskID)
		return
	default:
	}

	// 第三阶段：MixFile处理（如果启用）
	if s.isMixFileEnabled() {
		if err := s.processMixFile(taskID, task, result); err != nil {
			s.enhancedProcessingError(taskID, task, ProcessingStatusGeneratingShare, err, map[string]interface{}{
				"chunk_count": result.ChunkCount,
				"chunk_urls":  len(result.ChunkURLs),
			})
			return
		}
	}

	// 第四阶段：生成播放列表
	if err := s.generatePlaylist(taskID, task); err != nil {
		s.handleProcessingError(taskID, task, fmt.Sprintf("playlist generation failed: %v", err))
		return
	}

	// 处理完成
	s.completeProcessing(taskID, task)
}

// uploadChunks 上传分片
func (s *fileProcessingService) uploadChunks(taskID uint, task *model.DownloadTask, result *fileprocessor.ProcessingResult) error {
	// 验证工作目录
	if result.WorkDir == "" {
		return fmt.Errorf("processing result has empty work directory")
	}

	s.logProcessingStep(taskID, ProcessingStatusUploading, "upload_preparation", map[string]interface{}{
		"work_dir":    result.WorkDir,
		"chunk_count": len(result.Chunks),
	})

	// 准备上传任务
	jobs := make([]imgbb.UploadJob, len(result.Chunks))
	for i, chunk := range result.Chunks {
		chunkPath := s.processor.GetChunkPathFromResult(result, chunk)

		// 验证分片文件是否存在
		fileInfo, err := os.Stat(chunkPath)
		if os.IsNotExist(err) {
			logger.WithFields(map[string]interface{}{
				"task_id":     taskID,
				"chunk_index": i,
				"chunk_path":  chunkPath,
				"error":       "file_not_found",
			}).Error("Chunk file does not exist")
			return fmt.Errorf("chunk file does not exist: %s", chunkPath)
		}
		if err != nil {
			logger.WithFields(map[string]interface{}{
				"task_id":     taskID,
				"chunk_index": i,
				"chunk_path":  chunkPath,
				"error":       err.Error(),
			}).Error("Failed to check chunk file")
			return fmt.Errorf("failed to check chunk file: %s, error: %v", chunkPath, err)
		}

		jobs[i] = imgbb.UploadJob{
			Index:    i,
			FilePath: chunkPath,
			Filename: chunk.Filename,
		}

		logger.WithFields(map[string]interface{}{
			"task_id":     taskID,
			"chunk_index": i,
			"chunk_path":  chunkPath,
			"chunk_size":  fileInfo.Size(),
			"filename":    chunk.Filename,
		}).Debug("Prepared upload job")
	}

	// 执行批量上传
	uploadResult, err := s.imgbbClient.BatchUpload(jobs, 3, func(completed, total int) {
		progress := float64(completed) / float64(total) * 100.0

		// 更新数据库
		s.db.Model(&model.DownloadTask{}).Where("id = ?", taskID).Update("uploaded_chunks", completed)

		// 发送WebSocket通知
		chunkData := &websocket.ChunkUploadData{
			TaskID:     taskID,
			TaskName:   task.TaskName,
			ChunkIndex: completed - 1,
			ChunkCount: total,
			Progress:   progress,
		}
		s.websocketSvc.NotifyChunkUploaded(task.UserID, chunkData)
	})

	if err != nil {
		return fmt.Errorf("batch upload failed: %w", err)
	}

	if uploadResult.Failed > 0 {
		return fmt.Errorf("upload failed: %d/%d chunks failed", uploadResult.Failed, uploadResult.Total)
	}

	// 保存上传URL到ProcessingResult中
	if len(uploadResult.URLs) > 0 {
		result.ChunkURLs = uploadResult.URLs
		result.MixFileMode = s.isMixFileEnabled()
		logger.Infof("Upload completed: TaskID=%d, URLs=%d, MixFileMode=%t",
			taskID, len(uploadResult.URLs), result.MixFileMode)
	}

	return nil
}

// generatePlaylist 生成播放列表
func (s *fileProcessingService) generatePlaylist(taskID uint, task *model.DownloadTask) error {
	// 这里需要从数据库或缓存中获取上传的URL列表
	// 暂时使用模拟数据
	urls := []string{} // 实际应该从上传结果中获取

	// 生成播放列表
	playlistContent, err := s.playlistGen.GenerateFromChunks(urls, 10.0, task.EncryptionKey)
	if err != nil {
		return fmt.Errorf("failed to generate playlist: %w", err)
	}

	// 保存播放列表（这里需要实现播放列表存储逻辑）
	// TODO: 实际保存playlistContent到文件系统或数据库
	_ = playlistContent // 暂时忽略未使用的变量
	playlistURL := fmt.Sprintf("/api/playlist/%d.m3u8", taskID)

	// 更新任务
	updateData := map[string]interface{}{
		"playlist_url":            playlistURL,
		"status":                  model.TaskStatusReady,
		"processing_status":       "completed",
		"processing_progress":     100.0,
		"processing_completed_at": time.Now(),
	}
	s.db.Model(&model.DownloadTask{}).Where("id = ?", taskID).Updates(updateData)

	// 通知播放列表就绪
	playlistData := &websocket.PlaylistReadyData{
		TaskID:      taskID,
		TaskName:    task.TaskName,
		PlaylistURL: playlistURL,
		ChunkCount:  task.ChunkCount,
		TotalSize:   task.TotalSize,
		Encrypted:   task.EncryptionKey != "",
	}
	s.websocketSvc.NotifyPlaylistReady(task.UserID, playlistData)

	logger.Infof("Playlist generated: TaskID=%d, URL=%s", taskID, playlistURL)
	return nil
}

// completeProcessing 完成处理
func (s *fileProcessingService) completeProcessing(taskID uint, task *model.DownloadTask) {
	logger.Infof("Processing completed: TaskID=%d", taskID)

	// 清理工作目录（如果配置允许）
	if task.ProcessingWorkDir != "" {
		go s.cleanupWorkDirectory(taskID, task.ProcessingWorkDir)
	}
}

// cleanupWorkDirectory 清理工作目录
func (s *fileProcessingService) cleanupWorkDirectory(taskID uint, workDir string) {
	// 延迟清理，给上传一些时间完成
	time.Sleep(5 * time.Minute)

	if _, err := os.Stat(workDir); os.IsNotExist(err) {
		logger.Debugf("Work directory already cleaned: %s", workDir)
		return
	}

	if err := os.RemoveAll(workDir); err != nil {
		logger.Errorf("Failed to cleanup work directory for task %d: %s, error: %v", taskID, workDir, err)
	} else {
		logger.Infof("Successfully cleaned up work directory for task %d: %s", taskID, workDir)
	}
}

// handleProcessingError 处理错误
func (s *fileProcessingService) handleProcessingError(taskID uint, task *model.DownloadTask, errorMsg string) {
	// 更新任务状态
	updateData := map[string]interface{}{
		"status":            model.TaskStatusFailed,
		"processing_status": "failed",
		"error_message":     errorMsg,
	}
	s.db.Model(&model.DownloadTask{}).Where("id = ?", taskID).Updates(updateData)

	// 发送错误通知
	s.websocketSvc.NotifyProcessingError(task.UserID, taskID, task.TaskName, errorMsg)

	logger.Errorf("Processing failed: TaskID=%d, Error=%s", taskID, errorMsg)
}

// GetActualFilePath 获取实际文件路径
func (s *fileProcessingService) GetActualFilePath(task *model.DownloadTask) (string, error) {
	// 优先使用ActualFiles中的信息
	if task.HasActualFiles() {
		primaryFile := task.GetPrimaryFile()
		if primaryFile != nil {
			logger.Infof("Using actual file path from database: %s", primaryFile.Path)
			return primaryFile.Path, nil
		}
	}

	// 回退到传统方式（向后兼容）
	if task.SavePath != "" && task.TaskName != "" {
		fallbackPath := filepath.Join(task.SavePath, task.TaskName)
		logger.Warnf("No actual files found for task %d, using fallback path: %s", task.ID, fallbackPath)
		return fallbackPath, nil
	}

	return "", fmt.Errorf("no valid file path found for task %d", task.ID)
}

// ValidateFilePath 验证文件路径的安全性和存在性
func (s *fileProcessingService) ValidateFilePath(filePath string, taskID uint) error {
	// 检查路径是否为空
	if filePath == "" {
		return fmt.Errorf("file path is empty for task %d", taskID)
	}

	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		return fmt.Errorf("file does not exist: %s", filePath)
	}
	if err != nil {
		return fmt.Errorf("failed to check file: %s, error: %v", filePath, err)
	}

	// 检查是否为文件（不是目录）
	if fileInfo.IsDir() {
		return fmt.Errorf("path is a directory, not a file: %s", filePath)
	}

	// 检查文件大小
	if fileInfo.Size() == 0 {
		return fmt.Errorf("file is empty: %s", filePath)
	}

	// 基本的路径安全检查（防止路径遍历）
	cleanPath := filepath.Clean(filePath)
	if cleanPath != filePath {
		logger.Warnf("File path was cleaned: original=%s, cleaned=%s", filePath, cleanPath)
	}

	logger.Infof("File validation passed: %s (size: %d bytes)", filePath, fileInfo.Size())
	return nil
}

// PauseProcessing 暂停处理
func (s *fileProcessingService) PauseProcessing(taskID uint) error {
	s.mutex.RLock()
	processingTask, exists := s.processingTasks[taskID]
	s.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("task is not being processed")
	}

	processingTask.Status = "paused"

	// 更新数据库状态
	updateData := map[string]interface{}{
		"status":            model.TaskStatusPaused,
		"processing_status": "paused",
	}
	s.db.Model(&model.DownloadTask{}).Where("id = ?", taskID).Updates(updateData)

	logger.Infof("Processing paused: TaskID=%d", taskID)
	return nil
}

// ResumeProcessing 恢复处理
func (s *fileProcessingService) ResumeProcessing(taskID uint) error {
	s.mutex.RLock()
	processingTask, exists := s.processingTasks[taskID]
	s.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("task is not being processed")
	}

	processingTask.Status = "running"

	// 更新数据库状态
	updateData := map[string]interface{}{
		"status":            model.TaskStatusProcessing,
		"processing_status": "running",
	}
	s.db.Model(&model.DownloadTask{}).Where("id = ?", taskID).Updates(updateData)

	logger.Infof("Processing resumed: TaskID=%d", taskID)
	return nil
}

// CancelProcessing 取消处理
func (s *fileProcessingService) CancelProcessing(taskID uint) error {
	s.mutex.RLock()
	processingTask, exists := s.processingTasks[taskID]
	s.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("task is not being processed")
	}

	// 发送取消信号
	select {
	case processingTask.CancelChan <- true:
	default:
	}

	// 更新数据库状态
	updateData := map[string]interface{}{
		"status":            model.TaskStatusCancelled,
		"processing_status": "cancelled",
	}
	s.db.Model(&model.DownloadTask{}).Where("id = ?", taskID).Updates(updateData)

	logger.Infof("Processing cancelled: TaskID=%d", taskID)
	return nil
}

// RetryProcessing 重试处理
func (s *fileProcessingService) RetryProcessing(taskID uint) error {
	// 获取任务信息
	var task model.DownloadTask
	if err := s.db.First(&task, taskID).Error; err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}

	// 检查任务状态
	if task.Status != model.TaskStatusFailed {
		return fmt.Errorf("only failed tasks can be retried")
	}

	// 重置处理状态
	updateData := map[string]interface{}{
		"status":                  model.TaskStatusCompleted, // 重置为可处理状态
		"processing_status":       "",
		"processing_progress":     0,
		"uploaded_chunks":         0,
		"error_message":           "",
		"processing_started_at":   nil,
		"processing_completed_at": nil,
	}
	s.db.Model(&model.DownloadTask{}).Where("id = ?", taskID).Updates(updateData)

	// 重新开始处理
	return s.StartProcessing(taskID)
}

// GetProcessingStatus 获取处理状态
func (s *fileProcessingService) GetProcessingStatus(taskID uint) (*ProcessingStatusResponse, error) {
	var task model.DownloadTask
	if err := s.db.First(&task, taskID).Error; err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	response := &ProcessingStatusResponse{
		TaskID:             task.ID,
		Status:             string(task.Status),
		ProcessingStatus:   task.ProcessingStatus,
		ProcessingProgress: task.ProcessingProgress,
		ChunkCount:         task.ChunkCount,
		UploadedChunks:     task.UploadedChunks,
		ErrorMessage:       task.ErrorMessage,
	}

	if task.ProcessingStartedAt != nil {
		startedAt := task.ProcessingStartedAt.Unix()
		response.StartedAt = &startedAt
	}

	if task.ProcessingCompletedAt != nil {
		completedAt := task.ProcessingCompletedAt.Unix()
		response.CompletedAt = &completedAt
	}

	return response, nil
}

// GetProcessingProgress 获取处理进度
func (s *fileProcessingService) GetProcessingProgress(taskID uint) (*ProcessingProgressResponse, error) {
	s.mutex.RLock()
	processingTask, exists := s.processingTasks[taskID]
	s.mutex.RUnlock()

	if !exists {
		// 如果不在处理中，从数据库获取静态信息
		var task model.DownloadTask
		if err := s.db.First(&task, taskID).Error; err != nil {
			return nil, fmt.Errorf("failed to get task: %w", err)
		}

		return &ProcessingProgressResponse{
			TaskID:          task.ID,
			Stage:           task.ProcessingStatus,
			Progress:        task.ProcessingProgress,
			OverallProgress: task.ProcessingProgress,
			ChunkCount:      task.ChunkCount,
			UploadedChunks:  task.UploadedChunks,
			Message:         "Task not currently processing",
		}, nil
	}

	// 返回实时进度信息
	return &ProcessingProgressResponse{
		TaskID:          taskID,
		Stage:           processingTask.Stage,
		Progress:        processingTask.Progress,
		OverallProgress: processingTask.Progress,
		Message:         fmt.Sprintf("Processing stage: %s", processingTask.Stage),
	}, nil
}

// ListProcessingTasks 列出处理任务
func (s *fileProcessingService) ListProcessingTasks(req *ListProcessingTasksRequest) ([]*model.DownloadTask, int64, error) {
	query := s.db.Model(&model.DownloadTask{})

	// 过滤处理状态
	if req.ProcessingStatus != "" {
		query = query.Where("processing_status = ?", req.ProcessingStatus)
	}

	// 搜索
	if req.Search != "" {
		query = query.Where("task_name LIKE ?", "%"+req.Search+"%")
	}

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count tasks: %w", err)
	}

	// 分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 排序
	if req.SortBy != "" {
		order := req.SortBy
		if req.SortDesc {
			order += " DESC"
		}
		query = query.Order(order)
	} else {
		query = query.Order("created_at DESC")
	}

	// 查询结果
	var tasks []*model.DownloadTask
	if err := query.Find(&tasks).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to query tasks: %w", err)
	}

	return tasks, total, nil
}

// GetPlaylist 获取播放列表
func (s *fileProcessingService) GetPlaylist(taskID uint) (*PlaylistResponse, error) {
	var task model.DownloadTask
	if err := s.db.First(&task, taskID).Error; err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	if task.PlaylistURL == "" {
		return nil, fmt.Errorf("playlist not ready")
	}

	// 这里应该从存储中获取播放列表内容
	// 暂时返回基本信息
	return &PlaylistResponse{
		TaskID:      task.ID,
		PlaylistURL: task.PlaylistURL,
		ChunkCount:  task.ChunkCount,
		Encrypted:   task.EncryptionKey != "",
		CreatedAt:   task.CreatedAt.Unix(),
		UpdatedAt:   task.UpdatedAt.Unix(),
	}, nil
}

// GeneratePlaylist 生成播放列表
func (s *fileProcessingService) GeneratePlaylist(taskID uint) (*PlaylistResponse, error) {
	return s.generatePlaylistForTask(taskID)
}

// RefreshPlaylist 刷新播放列表
func (s *fileProcessingService) RefreshPlaylist(taskID uint) (*PlaylistResponse, error) {
	return s.generatePlaylistForTask(taskID)
}

// generatePlaylistForTask 为任务生成播放列表
func (s *fileProcessingService) generatePlaylistForTask(taskID uint) (*PlaylistResponse, error) {
	var task model.DownloadTask
	if err := s.db.First(&task, taskID).Error; err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	if task.Status != model.TaskStatusReady && task.Status != model.TaskStatusUploading {
		return nil, fmt.Errorf("task is not ready for playlist generation")
	}

	// 这里应该从数据库获取上传的URL列表
	urls := []string{} // 实际实现需要从存储中获取

	// 生成播放列表
	_, err := s.playlistGen.GenerateFromChunks(urls, 10.0, task.EncryptionKey)
	if err != nil {
		return nil, fmt.Errorf("failed to generate playlist: %w", err)
	}

	// 更新播放列表URL
	playlistURL := fmt.Sprintf("/api/playlist/%d.m3u8", taskID)
	s.db.Model(&task).Update("playlist_url", playlistURL)

	return &PlaylistResponse{
		TaskID:      task.ID,
		PlaylistURL: playlistURL,
		ChunkCount:  task.ChunkCount,
		Encrypted:   task.EncryptionKey != "",
		CreatedAt:   task.CreatedAt.Unix(),
		UpdatedAt:   time.Now().Unix(),
	}, nil
}

// BatchStartProcessing 批量开始处理
func (s *fileProcessingService) BatchStartProcessing(taskIDs []uint) (*BatchOperationResult, error) {
	result := &BatchOperationResult{
		Total: len(taskIDs),
	}

	for _, taskID := range taskIDs {
		if err := s.StartProcessing(taskID); err != nil {
			result.Failed++
			result.Errors = append(result.Errors, fmt.Sprintf("TaskID %d: %v", taskID, err))
			result.FailedIDs = append(result.FailedIDs, taskID)
		} else {
			result.Success++
			result.SuccessIDs = append(result.SuccessIDs, taskID)
		}
	}

	return result, nil
}

// BatchCancelProcessing 批量取消处理
func (s *fileProcessingService) BatchCancelProcessing(taskIDs []uint) (*BatchOperationResult, error) {
	result := &BatchOperationResult{
		Total: len(taskIDs),
	}

	for _, taskID := range taskIDs {
		if err := s.CancelProcessing(taskID); err != nil {
			result.Failed++
			result.Errors = append(result.Errors, fmt.Sprintf("TaskID %d: %v", taskID, err))
			result.FailedIDs = append(result.FailedIDs, taskID)
		} else {
			result.Success++
			result.SuccessIDs = append(result.SuccessIDs, taskID)
		}
	}

	return result, nil
}

// GetProcessingStats 获取处理统计
func (s *fileProcessingService) GetProcessingStats() (*ProcessingStats, error) {
	var stats ProcessingStats

	// 统计各种状态的任务数量
	s.db.Model(&model.DownloadTask{}).Where("status IN ?", []string{
		string(model.TaskStatusProcessing),
		string(model.TaskStatusUploading),
	}).Count(&stats.ActiveProcessing)

	s.db.Model(&model.DownloadTask{}).Where("status = ?", model.TaskStatusReady).Count(&stats.CompletedProcessing)

	s.db.Model(&model.DownloadTask{}).Where("status = ? AND processing_status = ?",
		model.TaskStatusFailed, "failed").Count(&stats.FailedProcessing)

	s.db.Model(&model.DownloadTask{}).Where("processing_status != ''").Count(&stats.TotalProcessingTasks)

	// 统计分片信息
	s.db.Model(&model.DownloadTask{}).Where("chunk_count > 0").
		Select("SUM(chunk_count) as total, SUM(uploaded_chunks) as uploaded").
		Scan(&struct {
			Total    int64 `json:"total"`
			Uploaded int64 `json:"uploaded"`
		}{Total: stats.TotalChunks, Uploaded: stats.UploadedChunks})

	s.db.Model(&model.DownloadTask{}).Where("playlist_url != ''").Count(&stats.TotalPlaylistsReady)

	// 计算成功率
	if stats.TotalProcessingTasks > 0 {
		stats.SuccessRate = float64(stats.CompletedProcessing) / float64(stats.TotalProcessingTasks) * 100.0
	}

	return &stats, nil
}

// GetUserProcessingStats 获取用户处理统计
func (s *fileProcessingService) GetUserProcessingStats(userID uint) (*UserProcessingStats, error) {
	var stats UserProcessingStats
	stats.UserID = userID

	// 统计用户的处理任务
	s.db.Model(&model.DownloadTask{}).Where("user_id = ? AND processing_status != ''", userID).
		Count(&stats.TotalProcessing)

	s.db.Model(&model.DownloadTask{}).Where("user_id = ? AND status = ?", userID, model.TaskStatusReady).
		Count(&stats.CompletedProcessing)

	s.db.Model(&model.DownloadTask{}).Where("user_id = ? AND status = ? AND processing_status = ?",
		userID, model.TaskStatusFailed, "failed").Count(&stats.FailedProcessing)

	// 统计用户的分片信息
	s.db.Model(&model.DownloadTask{}).Where("user_id = ? AND chunk_count > 0", userID).
		Select("SUM(chunk_count) as total, SUM(uploaded_chunks) as uploaded").
		Scan(&struct {
			Total    int64 `json:"total"`
			Uploaded int64 `json:"uploaded"`
		}{Total: stats.TotalChunks, Uploaded: stats.UploadedChunks})

	s.db.Model(&model.DownloadTask{}).Where("user_id = ? AND playlist_url != ''", userID).
		Count(&stats.PlaylistsReady)

	// 计算成功率
	if stats.TotalProcessing > 0 {
		stats.SuccessRate = float64(stats.CompletedProcessing) / float64(stats.TotalProcessing) * 100.0
	}

	return &stats, nil
}

// CleanupProcessingFiles 清理处理文件
func (s *fileProcessingService) CleanupProcessingFiles(taskID uint) error {
	var task model.DownloadTask
	if err := s.db.First(&task, taskID).Error; err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}

	// 这里应该实现清理逻辑，删除临时文件、分片文件等
	logger.Infof("Cleanup processing files for task: TaskID=%d", taskID)
	return nil
}

// CleanupExpiredFiles 清理过期文件
func (s *fileProcessingService) CleanupExpiredFiles() error {
	// 查找过期的任务（例如：完成超过30天的任务）
	expiredTime := time.Now().AddDate(0, 0, -30)

	var expiredTasks []model.DownloadTask
	if err := s.db.Where("status = ? AND processing_completed_at < ?",
		model.TaskStatusReady, expiredTime).Find(&expiredTasks).Error; err != nil {
		return fmt.Errorf("failed to query expired tasks: %w", err)
	}

	for _, task := range expiredTasks {
		if err := s.CleanupProcessingFiles(task.ID); err != nil {
			logger.Warnf("Failed to cleanup files for task %d: %v", task.ID, err)
		}
	}

	logger.Infof("Cleanup expired files completed: processed=%d", len(expiredTasks))
	return nil
}

// updateProcessingStatus 更新处理状态并发送通知
func (s *fileProcessingService) updateProcessingStatus(taskID uint, status string, message string) {
	// 更新数据库状态
	updateData := map[string]interface{}{
		"processing_status": status,
	}
	s.db.Model(&model.DownloadTask{}).Where("id = ?", taskID).Updates(updateData)

	// 发送WebSocket通知
	s.websocketSvc.NotifyProcessingProgress(taskID, status, message)

	// 记录状态变更日志
	logger.WithFields(map[string]interface{}{
		"task_id": taskID,
		"status":  status,
		"message": message,
	}).Info("Processing status updated")
}

// logProcessingStep 记录处理步骤的详细日志
func (s *fileProcessingService) logProcessingStep(taskID uint, stage string, step string, details map[string]interface{}) {
	fields := map[string]interface{}{
		"task_id": taskID,
		"stage":   stage,
		"step":    step,
	}

	// 合并额外的详细信息
	for k, v := range details {
		fields[k] = v
	}

	logger.WithFields(fields).Info("Processing step completed")
}

// enhancedProcessingError 增强的错误处理，提供更详细的错误信息
func (s *fileProcessingService) enhancedProcessingError(taskID uint, task *model.DownloadTask, stage string, err error, context map[string]interface{}) {
	// 构建详细的错误信息
	errorDetails := map[string]interface{}{
		"task_id":   taskID,
		"task_name": task.TaskName,
		"stage":     stage,
		"error":     err.Error(),
	}

	// 合并上下文信息
	for k, v := range context {
		errorDetails[k] = v
	}

	// 记录详细错误日志
	logger.WithFields(errorDetails).Error("Processing failed with detailed context")

	// 生成用户友好的错误消息
	var userMessage string
	switch stage {
	case ProcessingStatusFileChecking:
		userMessage = fmt.Sprintf("文件检查失败: %v", err)
	case ProcessingStatusChunking:
		userMessage = fmt.Sprintf("文件分片失败: %v", err)
	case ProcessingStatusEncrypting:
		userMessage = fmt.Sprintf("文件加密失败: %v", err)
	case ProcessingStatusUploading:
		userMessage = fmt.Sprintf("文件上传失败: %v", err)
	default:
		userMessage = fmt.Sprintf("处理失败: %v", err)
	}

	// 调用原有的错误处理方法
	s.handleProcessingError(taskID, task, userMessage)
}

// isMixFileEnabled 检查是否启用MixFile功能
func (s *fileProcessingService) isMixFileEnabled() bool {
	return s.mixFileConfig != nil && s.mixFileConfig.Enabled
}

// processMixFile 处理MixFile流程
func (s *fileProcessingService) processMixFile(taskID uint, task *model.DownloadTask, result *fileprocessor.ProcessingResult) error {
	if !s.isMixFileEnabled() {
		return fmt.Errorf("MixFile functionality is not enabled")
	}

	logger.WithFields(map[string]interface{}{
		"task_id":     taskID,
		"chunk_count": len(result.ChunkURLs),
		"stage":       "mixfile_processing",
	}).Info("Starting MixFile processing")

	// 阶段1：生成索引文件
	s.updateProcessingStatus(taskID, ProcessingStatusGeneratingIndex, "正在生成索引文件...")

	index, err := s.generateIndexFile(result)
	if err != nil {
		return fmt.Errorf("failed to generate index file: %w", err)
	}

	s.logProcessingStep(taskID, ProcessingStatusGeneratingIndex, "index_generated", map[string]interface{}{
		"filename":    index.FileName,
		"chunk_count": index.GetChunkCount(),
		"file_size":   index.FileSize,
	})

	// 阶段2：上传索引文件
	s.updateProcessingStatus(taskID, ProcessingStatusUploadingIndex, "正在上传索引文件...")

	indexURL, err := s.uploadIndexFile(index)
	if err != nil {
		return fmt.Errorf("failed to upload index file: %w", err)
	}

	result.IndexURL = indexURL
	s.logProcessingStep(taskID, ProcessingStatusUploadingIndex, "index_uploaded", map[string]interface{}{
		"index_url": indexURL,
	})

	// 阶段3：生成分享码
	s.updateProcessingStatus(taskID, ProcessingStatusGeneratingShare, "正在生成分享码...")

	shareCode, err := s.generateShareCode(index, indexURL)
	if err != nil {
		return fmt.Errorf("failed to generate share code: %w", err)
	}

	result.ShareCode = shareCode
	s.logProcessingStep(taskID, ProcessingStatusGeneratingShare, "share_code_generated", map[string]interface{}{
		"share_code_length": len(shareCode),
		"share_code_prefix": shareCode[:20] + "...",
	})

	// 更新数据库
	updateData := map[string]interface{}{
		"index_url":    indexURL,
		"share_code":   shareCode,
		"mixfile_mode": true,
	}
	s.db.Model(&model.DownloadTask{}).Where("id = ?", taskID).Updates(updateData)

	// 发送MixFile完成通知
	s.notifyMixFileCompleted(task, result)

	logger.Infof("MixFile processing completed: TaskID=%d, ShareCode=%s", taskID, shareCode[:20]+"...")
	return nil
}

// generateIndexFile 生成索引文件
func (s *fileProcessingService) generateIndexFile(result *fileprocessor.ProcessingResult) (*mixfile.IndexFile, error) {
	// 准备分片信息
	chunks := make([]mixfile.ChunkInfo, len(result.Chunks))
	for i, chunk := range result.Chunks {
		if i >= len(result.ChunkURLs) {
			return nil, fmt.Errorf("chunk URL missing for chunk %d", i)
		}
		if i >= len(result.ChunkHashes) {
			return nil, fmt.Errorf("chunk hash missing for chunk %d", i)
		}

		chunks[i] = mixfile.ChunkInfo{
			Index: i,
			URL:   result.ChunkURLs[i],
			Hash:  result.ChunkHashes[i],
			Size:  chunk.Size,
		}
	}

	// 准备元数据
	metadata := map[string]interface{}{
		"filename":           filepath.Base(result.WorkDir), // 使用工作目录名作为文件名
		"chunk_size":         1024 * 1024,                   // 1MB分片大小
		"encryption_key":     result.EncryptionKey,
		"original_file_hash": result.OriginalFileHash,
		"processing_time":    result.ProcessingTime.String(),
		"total_size":         result.TotalSize,
	}

	// 创建索引文件
	index, err := s.indexManager.CreateIndex(chunks, metadata)
	if err != nil {
		return nil, fmt.Errorf("failed to create index: %w", err)
	}

	logger.Debugf("Generated index file: %s, chunks=%d", index.FileName, len(chunks))
	return index, nil
}

// uploadIndexFile 上传索引文件
func (s *fileProcessingService) uploadIndexFile(index *mixfile.IndexFile) (string, error) {
	// 序列化索引文件
	indexData, err := s.indexManager.SerializeIndex(index)
	if err != nil {
		return "", fmt.Errorf("failed to serialize index: %w", err)
	}

	// 压缩索引数据（如果启用）
	var processedData []byte
	if s.mixFileConfig.EnableIndexCompression {
		processedData, err = s.indexManager.CompressIndex(indexData)
		if err != nil {
			return "", fmt.Errorf("failed to compress index: %w", err)
		}
		logger.Debugf("Index compressed: %d -> %d bytes", len(indexData), len(processedData))
	} else {
		processedData = indexData
	}

	// 加密索引数据（如果启用）
	if s.mixFileConfig.EnableIndexEncryption {
		// 使用文件的加密密钥来加密索引
		encryptionKey := []byte(index.EncryptionKey)
		if len(encryptionKey) > 32 {
			encryptionKey = encryptionKey[:32] // 截取前32字节作为AES-256密钥
		}

		encryptedData, err := s.indexManager.EncryptIndex(processedData, encryptionKey)
		if err != nil {
			return "", fmt.Errorf("failed to encrypt index: %w", err)
		}
		processedData = encryptedData
		logger.Debugf("Index encrypted: %d bytes", len(processedData))
	}

	// 生成索引文件名
	indexFilename := fmt.Sprintf("index_%s.json", index.FileName)

	// 上传索引文件（使用隐写术）
	var uploadResult *imgbb.UploadResult
	var uploadErr error

	for attempt := 0; attempt < s.mixFileConfig.IndexUploadRetries; attempt++ {
		if s.mixFileConfig.EnableSteganography {
			uploadResult, uploadErr = s.imgbbClient.UploadWithSteganography(processedData, indexFilename)
		} else {
			uploadResult, uploadErr = s.imgbbClient.UploadData(processedData, indexFilename)
		}

		if uploadErr == nil && uploadResult.Success {
			break
		}

		logger.Warnf("Index upload attempt %d failed: %v", attempt+1, uploadErr)
		if attempt < s.mixFileConfig.IndexUploadRetries-1 {
			time.Sleep(time.Duration(attempt+1) * time.Second) // 指数退避
		}
	}

	if uploadErr != nil {
		return "", fmt.Errorf("failed to upload index after %d attempts: %w",
			s.mixFileConfig.IndexUploadRetries, uploadErr)
	}

	if !uploadResult.Success {
		return "", fmt.Errorf("index upload failed: %s", uploadResult.Error)
	}

	logger.Infof("Index file uploaded successfully: %s", uploadResult.URL)
	return uploadResult.URL, nil
}

// generateShareCode 生成分享码
func (s *fileProcessingService) generateShareCode(index *mixfile.IndexFile, indexURL string) (string, error) {
	shareCode, err := s.shareCodeProcessor.IndexToShareCode(index, indexURL)
	if err != nil {
		return "", fmt.Errorf("failed to generate share code: %w", err)
	}

	// 验证分享码长度
	if len(shareCode) > s.mixFileConfig.MaxShareCodeLength {
		return "", fmt.Errorf("share code too long: %d > %d",
			len(shareCode), s.mixFileConfig.MaxShareCodeLength)
	}

	// 验证分享码格式
	if err := s.shareCodeProcessor.ValidateShareCodeFormat(shareCode); err != nil {
		return "", fmt.Errorf("invalid share code format: %w", err)
	}

	logger.Debugf("Generated share code: length=%d, prefix=%s",
		len(shareCode), s.mixFileConfig.ShareCodePrefix)
	return shareCode, nil
}

// notifyMixFileCompleted 发送MixFile完成通知
func (s *fileProcessingService) notifyMixFileCompleted(task *model.DownloadTask, result *fileprocessor.ProcessingResult) {
	// 发送WebSocket通知
	mixFileData := &websocket.MixFileCompletedData{
		TaskID:     task.ID,
		TaskName:   task.TaskName,
		ShareCode:  result.ShareCode,
		IndexURL:   result.IndexURL,
		ChunkCount: len(result.ChunkURLs),
		TotalSize:  result.TotalSize,
		Encrypted:  result.EncryptionKey != "",
	}
	s.websocketSvc.NotifyMixFileCompleted(task.UserID, mixFileData)
}
