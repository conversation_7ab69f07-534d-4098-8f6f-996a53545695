package service

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/streamhg"
	"magnet-downloader/pkg/streamtape"
)

// 服务商文件大小限制常量
const (
	StreamTapeSizeLimit = 15 * 1024 * 1024 * 1024 // 15GB
	StreamHGSizeLimit   = 50 * 1024 * 1024 * 1024 // 50GB
)

// executeUploadToStreamTape 执行StreamTape上传
func (s *autoUploadService) executeUploadToStreamTape(task *UploadTask) {
	logger.Infof("开始上传到StreamTape: %s", task.VideoFile.Name)

	if s.streamTapeClient == nil {
		s.handleUploadError(task, fmt.Errorf("StreamTape客户端未初始化"))
		return
	}

	result, err := s.streamTapeClient.UploadFile(task.VideoFile.Path)
	if err != nil {
		s.handleUploadError(task, err)
		return
	}

	// 转换StreamTape结果为通用格式
	uploadResult := &UploadResult{
		Success:  result.Success,
		URL:      result.URL,
		PlayURL:  result.PlayURL,
		FileCode: result.FileCode,
		Size:     result.Size,
		Title:    result.Title,
		CanPlay:  result.CanPlay,
		Error:    result.Error,
	}

	s.handleStreamTapeUploadSuccess(task, result, uploadResult)
}

// handleStreamTapeUploadSuccess 处理StreamTape上传成功
func (s *autoUploadService) handleStreamTapeUploadSuccess(task *UploadTask, streamResult *streamtape.UploadResult, primaryResult *UploadResult) {
	// 设置任务状态
	task.Status = "completed"
	task.Progress = 100
	task.EndTime = time.Now()
	task.Result = primaryResult

	// 准备数据库更新数据
	updateData := map[string]interface{}{
		"status":            "completed",
		"processing_status": "completed",
		"completed_at":      time.Now(),
		"play_url":          primaryResult.PlayURL,
		"share_code":        primaryResult.FileCode,
		"total_size":        primaryResult.Size,
		"stream_tape_url":   streamResult.PlayURL,
	}

	logger.Infof("保存StreamTape链接: %s", streamResult.PlayURL)

	// 查找或创建数据库记录
	var existingTask model.DownloadTask
	result := s.db.Where("save_path = ?", task.VideoFile.Path).First(&existingTask)

	if result.Error != nil {
		// 记录不存在，创建新记录
		newTask := model.DownloadTask{
			TaskName:         task.VideoFile.Name,
			SavePath:         task.VideoFile.Path,
			Status:           model.TaskStatusCompleted,
			ProcessingStatus: "completed",
			PlayURL:          primaryResult.PlayURL,
			ShareCode:        primaryResult.FileCode,
			TotalSize:        primaryResult.Size,
			CompletedAt:      &task.EndTime,
			Priority:         2, // 自动上传优先级
			UserID:           1, // 默认用户ID
			StreamTapeURL:    streamResult.PlayURL,
		}

		if err := s.db.Create(&newTask).Error; err != nil {
			logger.Errorf("创建下载任务记录失败: %v", err)
		} else {
			logger.Infof("创建下载任务记录成功: %s", task.VideoFile.Name)
		}
	} else {
		// 记录存在，更新记录
		if err := s.db.Model(&existingTask).Updates(updateData).Error; err != nil {
			logger.Errorf("更新下载任务记录失败: %v", err)
		} else {
			logger.Infof("更新下载任务记录成功: %s", task.VideoFile.Name)
		}
	}

	// 发送成功通知
	s.websocketSvc.BroadcastSystemNotification("自动上传完成", fmt.Sprintf("文件 %s 上传成功", task.VideoFile.Name), "success")
	logger.Infof("视频上传成功: %s -> %s", task.VideoFile.Name, primaryResult.PlayURL)

	// 🔥 重要：删除上传成功的文件和目录
	if s.config.FileProcessing.AutoUpload.DeleteAfterUpload {
		// 删除单个文件
		s.deleteUploadedFile(task)
		// 检查是否需要删除整个任务目录
		s.deleteTaskDirectoryIfComplete(task.VideoFile.TaskDir)
	}
}

// deleteUploadedFile 删除上传成功的文件
func (s *autoUploadService) deleteUploadedFile(task *UploadTask) {
	filePath := task.VideoFile.Path

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		logger.Warnf("文件已不存在，无需删除: %s", filePath)
		return
	}

	// 删除文件
	if err := os.Remove(filePath); err != nil {
		logger.Errorf("删除文件失败: %s -> %v", filePath, err)
		return
	}

	logger.Infof("✅ 文件删除成功: %s", filePath)

	// 如果是根目录文件，不删除目录
	if task.VideoFile.TaskDir == task.VideoFile.Path+"/__ROOT__" {
		logger.Debugf("根目录文件，不删除目录: %s", filePath)
		return
	}

	// 检查是否需要删除空目录
	dirPath := filepath.Dir(filePath)
	if dirPath != "/www/wwwroot/JAVAPI.COM/downloads" { // 不删除根下载目录
		s.deleteEmptyDirectory(dirPath)
	}
}

// deleteEmptyDirectory 删除空目录
func (s *autoUploadService) deleteEmptyDirectory(dirPath string) {
	// 检查目录是否为空
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		logger.Warnf("无法读取目录: %s -> %v", dirPath, err)
		return
	}

	if len(entries) == 0 {
		// 目录为空，删除它
		if err := os.Remove(dirPath); err != nil {
			logger.Warnf("删除空目录失败: %s -> %v", dirPath, err)
		} else {
			logger.Infof("✅ 空目录删除成功: %s", dirPath)
		}
	} else {
		logger.Debugf("目录不为空，保留: %s (包含 %d 个项目)", dirPath, len(entries))
	}
}

// executeUploadToStreamHG 执行StreamHG上传
func (s *autoUploadService) executeUploadToStreamHG(task *UploadTask) {
	logger.Infof("开始上传到StreamHG: %s", task.VideoFile.Name)

	if s.streamHGClient == nil {
		s.handleUploadError(task, fmt.Errorf("StreamHG客户端未初始化"))
		return
	}

	result, err := s.streamHGClient.UploadFile(task.VideoFile.Path)
	if err != nil {
		s.handleUploadError(task, err)
		return
	}

	// 转换StreamHG结果为通用格式
	uploadResult := &UploadResult{
		Success:  result.Success,
		URL:      result.URL,
		PlayURL:  result.PlayURL,
		FileCode: result.FileCode,
		Size:     result.Size,
		Title:    result.Title,
		CanPlay:  result.CanPlay,
		Error:    result.Error,
	}

	s.handleStreamHGUploadSuccess(task, result, uploadResult)
}

// executeUploadToDual 执行智能双重上传（根据文件大小选择策略）
func (s *autoUploadService) executeUploadToDual(task *UploadTask) {
	logger.Infof("开始智能双重上传: %s (大小: %.2f GB)", task.VideoFile.Name, float64(task.VideoFile.Size)/(1024*1024*1024))

	// 检查客户端是否都已初始化
	if s.streamTapeClient == nil && s.streamHGClient == nil {
		s.handleUploadError(task, fmt.Errorf("StreamTape和StreamHG客户端都未初始化"))
		return
	}

	// 智能上传策略选择
	strategy := s.selectUploadStrategy(task.VideoFile.Size)
	logger.Infof("选择上传策略: %s", strategy)

	switch strategy {
	case "streamhg_only":
		s.executeStreamHGOnlyUpload(task)
	case "streamtape_split":
		s.executeStreamTapeSplitUpload(task)
	case "dual_normal":
		s.executeDualNormalUpload(task)
	case "dual_smart":
		s.executeDualSmartUpload(task)
	default:
		s.executeDualNormalUpload(task) // 默认策略
	}
}

// selectUploadStrategy 选择上传策略
func (s *autoUploadService) selectUploadStrategy(fileSize int64) string {
	if fileSize > StreamHGSizeLimit {
		// 超过50GB，两个服务商都需要切割
		return "dual_smart"
	} else if fileSize > StreamTapeSizeLimit {
		// 15GB-50GB之间，StreamTape需要切割，StreamHG可以直接上传
		return "streamhg_only"
	} else {
		// 小于15GB，两个服务商都可以直接上传
		return "dual_normal"
	}
}

// executeStreamHGOnlyUpload 仅使用StreamHG上传（15GB-50GB文件）
func (s *autoUploadService) executeStreamHGOnlyUpload(task *UploadTask) {
	logger.Infof("文件大小%.2f GB，仅使用StreamHG上传", float64(task.VideoFile.Size)/(1024*1024*1024))

	if s.streamHGClient == nil {
		s.handleUploadError(task, fmt.Errorf("StreamHG客户端未初始化"))
		return
	}

	result, err := s.streamHGClient.UploadFile(task.VideoFile.Path)
	if err != nil {
		s.handleUploadError(task, err)
		return
	}

	// 转换StreamHG结果为通用格式
	uploadResult := &UploadResult{
		Success:  result.Success,
		URL:      result.URL,
		PlayURL:  result.PlayURL,
		FileCode: result.FileCode,
		Size:     result.Size,
		Title:    result.Title,
		CanPlay:  result.CanPlay,
		Error:    result.Error,
	}

	s.handleStreamHGUploadSuccess(task, result, uploadResult)
}

// executeDualNormalUpload 执行正常双重上传（<15GB文件）
func (s *autoUploadService) executeDualNormalUpload(task *UploadTask) {

	var wg sync.WaitGroup
	var streamTapeResult *streamtape.UploadResult
	var streamHGResult *streamhg.UploadResult
	var streamTapeErr, streamHGErr error

	// StreamTape上传
	if s.streamTapeClient != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			logger.Infof("开始StreamTape并行上传: %s", task.VideoFile.Name)
			streamTapeResult, streamTapeErr = s.streamTapeClient.UploadFile(task.VideoFile.Path)
			if streamTapeErr != nil {
				logger.Warnf("StreamTape上传失败: %v", streamTapeErr)
			} else {
				logger.Infof("StreamTape上传成功: %s", task.VideoFile.Name)
			}
		}()
	}

	// StreamHG上传
	if s.streamHGClient != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			logger.Infof("开始StreamHG并行上传: %s", task.VideoFile.Name)
			streamHGResult, streamHGErr = s.streamHGClient.UploadFile(task.VideoFile.Path)
			if streamHGErr != nil {
				logger.Warnf("StreamHG上传失败: %v", streamHGErr)
			} else {
				logger.Infof("StreamHG上传成功: %s", task.VideoFile.Name)
			}
		}()
	}

	// 等待所有上传完成
	wg.Wait()

	// 处理双重上传结果
	s.handleDualUploadResult(task, streamTapeResult, streamHGResult, streamTapeErr, streamHGErr)
}

// executeStreamTapeSplitUpload 执行StreamTape切割上传（>15GB文件）
func (s *autoUploadService) executeStreamTapeSplitUpload(task *UploadTask) {
	logger.Infof("文件大小%.2f GB，StreamTape需要切割上传", float64(task.VideoFile.Size)/(1024*1024*1024))

	// TODO: 实现文件切割逻辑
	// 暂时使用StreamHG单独上传作为备选方案
	s.executeStreamHGOnlyUpload(task)
}

// executeDualSmartUpload 执行智能双重上传（>50GB文件）
func (s *autoUploadService) executeDualSmartUpload(task *UploadTask) {
	logger.Infof("文件大小%.2f GB，两个服务商都需要切割上传", float64(task.VideoFile.Size)/(1024*1024*1024))

	// TODO: 实现双重切割逻辑
	// 暂时跳过超大文件
	s.handleUploadError(task, fmt.Errorf("文件过大(%.2f GB)，暂不支持切割上传", float64(task.VideoFile.Size)/(1024*1024*1024)))
}

// handleStreamHGUploadSuccess 处理StreamHG上传成功
func (s *autoUploadService) handleStreamHGUploadSuccess(task *UploadTask, streamResult *streamhg.UploadResult, primaryResult *UploadResult) {
	// 设置任务状态
	task.Status = "completed"
	task.Progress = 100
	task.EndTime = time.Now()
	task.Result = primaryResult

	// 准备数据库更新数据
	updateData := map[string]interface{}{
		"status":            "completed",
		"processing_status": "completed",
		"completed_at":      time.Now(),
		"play_url":          primaryResult.PlayURL,
		"share_code":        primaryResult.FileCode,
		"total_size":        primaryResult.Size,
		"stream_hg_url":     streamResult.PlayURL,
	}

	logger.Infof("保存StreamHG链接: %s", streamResult.PlayURL)

	// 查找或创建数据库记录
	var existingTask model.DownloadTask
	result := s.db.Where("save_path = ?", task.VideoFile.Path).First(&existingTask)

	if result.Error != nil {
		// 记录不存在，创建新记录
		newTask := model.DownloadTask{
			TaskName:         task.VideoFile.Name,
			SavePath:         task.VideoFile.Path,
			Status:           "completed",
			ProcessingStatus: "completed",
			CompletedAt:      &[]time.Time{time.Now()}[0],
			PlayURL:          primaryResult.PlayURL,
			ShareCode:        primaryResult.FileCode,
			TotalSize:        primaryResult.Size,
			StreamHGURL:      streamResult.PlayURL,
			UserID:           1, // 默认用户ID
			Priority:         2, // 自动上传优先级
		}

		if err := s.db.Create(&newTask).Error; err != nil {
			logger.Errorf("创建下载任务记录失败: %v", err)
		} else {
			logger.Infof("创建下载任务记录成功: %s", task.VideoFile.Name)
		}
	} else {
		// 记录存在，更新记录
		if err := s.db.Model(&existingTask).Updates(updateData).Error; err != nil {
			logger.Errorf("更新下载任务记录失败: %v", err)
		} else {
			logger.Infof("更新下载任务记录成功: %s", task.VideoFile.Name)
		}
	}

	// 发送成功通知
	s.websocketSvc.BroadcastSystemNotification("自动上传完成", fmt.Sprintf("文件 %s 上传成功", task.VideoFile.Name), "success")
	logger.Infof("视频上传成功: %s -> %s", task.VideoFile.Name, primaryResult.PlayURL)

	// 删除上传成功的文件和目录
	if s.config.FileProcessing.AutoUpload.DeleteAfterUpload {
		s.deleteUploadedFile(task)
		s.deleteTaskDirectoryIfComplete(task.VideoFile.TaskDir)
	}
}

// handleDualUploadResult 处理双重上传结果
func (s *autoUploadService) handleDualUploadResult(task *UploadTask, streamTapeResult *streamtape.UploadResult, streamHGResult *streamhg.UploadResult, streamTapeErr, streamHGErr error) {
	// 统计成功的上传数量
	successCount := 0
	var primaryResult *UploadResult
	var primaryPlayURL string

	// 检查StreamTape结果
	if streamTapeErr == nil && streamTapeResult != nil && streamTapeResult.Success {
		successCount++
		if primaryResult == nil {
			primaryResult = &UploadResult{
				Success:  streamTapeResult.Success,
				URL:      streamTapeResult.URL,
				PlayURL:  streamTapeResult.PlayURL,
				FileCode: streamTapeResult.FileCode,
				Size:     streamTapeResult.Size,
				Title:    streamTapeResult.Title,
				CanPlay:  streamTapeResult.CanPlay,
				Error:    streamTapeResult.Error,
			}
			primaryPlayURL = streamTapeResult.PlayURL
		}
	}

	// 检查StreamHG结果
	if streamHGErr == nil && streamHGResult != nil && streamHGResult.Success {
		successCount++
		if primaryResult == nil {
			primaryResult = &UploadResult{
				Success:  streamHGResult.Success,
				URL:      streamHGResult.URL,
				PlayURL:  streamHGResult.PlayURL,
				FileCode: streamHGResult.FileCode,
				Size:     streamHGResult.Size,
				Title:    streamHGResult.Title,
				CanPlay:  streamHGResult.CanPlay,
				Error:    streamHGResult.Error,
			}
			primaryPlayURL = streamHGResult.PlayURL
		}
	}

	// 判断双重上传是否成功（至少一个成功即可）
	if successCount > 0 {
		logger.Infof("双重上传成功: %s (成功: %d/2)", task.VideoFile.Name, successCount)

		// 设置任务状态
		task.Status = "completed"
		task.Progress = 100
		task.EndTime = time.Now()
		task.Result = primaryResult

		// 准备数据库更新数据
		updateData := map[string]interface{}{
			"status":            "completed",
			"processing_status": "completed",
			"completed_at":      time.Now(),
			"play_url":          primaryPlayURL,
			"share_code":        primaryResult.FileCode,
			"total_size":        primaryResult.Size,
		}

		// 添加各服务商的链接
		if streamTapeResult != nil && streamTapeResult.Success {
			updateData["stream_tape_url"] = streamTapeResult.PlayURL
		}
		if streamHGResult != nil && streamHGResult.Success {
			updateData["stream_hg_url"] = streamHGResult.PlayURL
		}

		// 更新数据库
		s.updateDualUploadDatabase(task, updateData)

		// 发送成功通知
		s.websocketSvc.BroadcastSystemNotification("双重上传完成",
			fmt.Sprintf("文件 %s 双重上传成功 (%d/2)", task.VideoFile.Name, successCount), "success")

		// 删除文件
		if s.config.FileProcessing.AutoUpload.DeleteAfterUpload {
			s.deleteUploadedFile(task)
			s.deleteTaskDirectoryIfComplete(task.VideoFile.TaskDir)
		}
	} else {
		// 所有上传都失败了
		errorMsg := fmt.Sprintf("双重上传全部失败: StreamTape错误: %v, StreamHG错误: %v", streamTapeErr, streamHGErr)
		logger.Errorf("双重上传失败: %s - %s", task.VideoFile.Name, errorMsg)
		s.handleUploadError(task, fmt.Errorf(errorMsg))
	}
}

// updateDualUploadDatabase 更新双重上传的数据库记录
func (s *autoUploadService) updateDualUploadDatabase(task *UploadTask, updateData map[string]interface{}) {
	var existingTask model.DownloadTask
	result := s.db.Where("save_path = ?", task.VideoFile.Path).First(&existingTask)

	if result.Error != nil {
		// 记录不存在，创建新记录
		newTask := model.DownloadTask{
			TaskName:         task.VideoFile.Name,
			SavePath:         task.VideoFile.Path,
			Status:           "completed",
			ProcessingStatus: "completed",
			CompletedAt:      &[]time.Time{time.Now()}[0],
			PlayURL:          updateData["play_url"].(string),
			ShareCode:        updateData["share_code"].(string),
			TotalSize:        updateData["total_size"].(int64),
			UserID:           1, // 默认用户ID
			Priority:         2, // 自动上传优先级
		}

		// 添加各服务商链接
		if streamTapeURL, ok := updateData["stream_tape_url"].(string); ok {
			newTask.StreamTapeURL = streamTapeURL
		}
		if streamHGURL, ok := updateData["stream_hg_url"].(string); ok {
			newTask.StreamHGURL = streamHGURL
		}

		if err := s.db.Create(&newTask).Error; err != nil {
			logger.Errorf("创建双重上传任务记录失败: %v", err)
		} else {
			logger.Infof("创建双重上传任务记录成功: %s", task.VideoFile.Name)
		}
	} else {
		// 记录存在，更新记录
		if err := s.db.Model(&existingTask).Updates(updateData).Error; err != nil {
			logger.Errorf("更新双重上传任务记录失败: %v", err)
		} else {
			logger.Infof("更新双重上传任务记录成功: %s", task.VideoFile.Name)
		}
	}
}

// VOE上传功能已移除，改为使用独立的远程上传脚本
// 请使用 scripts/voe_remote_upload.go 进行VOE远程上传