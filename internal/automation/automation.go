package automation

import (
	"context"
	"fmt"
	"sync"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/repository"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/logger"
)

// AutomationManager 自动化流程管理器
type AutomationManager struct {
	config   *config.Config
	repo     repository.Repository
	services *service.Services
	
	// 流程控制
	workflows map[string]*Workflow
	running   bool
	mu        sync.RWMutex
	
	// 上下文控制
	ctx    context.Context
	cancel context.CancelFunc
}

// NewAutomationManager 创建自动化管理器
func NewAutomationManager(config *config.Config, repo repository.Repository, services *service.Services) *AutomationManager {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &AutomationManager{
		config:    config,
		repo:      repo,
		services:  services,
		workflows: make(map[string]*Workflow),
		ctx:       ctx,
		cancel:    cancel,
	}
}

// Start 启动自动化管理器
func (am *AutomationManager) Start() error {
	am.mu.Lock()
	defer am.mu.Unlock()
	
	if am.running {
		return fmt.Errorf("automation manager is already running")
	}
	
	logger.Info("Starting automation manager")
	
	// 注册默认工作流
	if err := am.registerDefaultWorkflows(); err != nil {
		return fmt.Errorf("failed to register default workflows: %w", err)
	}
	
	// 启动所有工作流
	for name, workflow := range am.workflows {
		if workflow.Config.Enabled {
			go am.runWorkflow(name, workflow)
			logger.Infof("Started workflow: %s", name)
		}
	}
	
	am.running = true
	logger.Info("Automation manager started successfully")
	return nil
}

// Stop 停止自动化管理器
func (am *AutomationManager) Stop() error {
	am.mu.Lock()
	defer am.mu.Unlock()
	
	if !am.running {
		return nil
	}
	
	logger.Info("Stopping automation manager")
	
	// 取消所有工作流
	am.cancel()
	
	// 等待所有工作流停止
	for name := range am.workflows {
		logger.Infof("Stopped workflow: %s", name)
	}
	
	am.running = false
	logger.Info("Automation manager stopped")
	return nil
}

// registerDefaultWorkflows 注册默认工作流
func (am *AutomationManager) registerDefaultWorkflows() error {
	// JAV自动化工作流
	if am.config.JAV.Enabled {
		javWorkflow := am.createJAVAutomationWorkflow()
		am.workflows["jav_automation"] = javWorkflow
		logger.Info("Registered JAV automation workflow")
	}
	
	// 下载自动化工作流
	downloadWorkflow := am.createDownloadAutomationWorkflow()
	am.workflows["download_automation"] = downloadWorkflow
	logger.Info("Registered download automation workflow")
	
	// 文件处理自动化工作流
	if am.config.FileProcessing.Enabled {
		fileProcessingWorkflow := am.createFileProcessingWorkflow()
		am.workflows["file_processing"] = fileProcessingWorkflow
		logger.Info("Registered file processing workflow")
	}
	
	return nil
}

// createJAVAutomationWorkflow 创建JAV自动化工作流
func (am *AutomationManager) createJAVAutomationWorkflow() *Workflow {
	return &Workflow{
		Name: "JAV Automation",
		Config: WorkflowConfig{
			Enabled:      true,
			Interval:     2 * time.Hour, // 每2小时执行一次
			MaxDuration:  30 * time.Minute,
			MaxRetries:   3,
			RetryDelay:   5 * time.Minute,
		},
		Steps: []WorkflowStep{
			{
				Name:        "scrape_new_movies",
				Description: "采集新影片数据",
				Handler:     am.scrapeNewMoviesStep,
				Timeout:     10 * time.Minute,
				Required:    true,
			},
			{
				Name:        "update_magnet_scores",
				Description: "更新磁力链接评分",
				Handler:     am.updateMagnetScoresStep,
				Timeout:     5 * time.Minute,
				Required:    false,
			},
			{
				Name:        "create_auto_downloads",
				Description: "创建自动下载任务",
				Handler:     am.createAutoDownloadsStep,
				Timeout:     5 * time.Minute,
				Required:    false,
				Condition:   am.config.JAV.Download.AutoStart,
			},
		},
	}
}

// createDownloadAutomationWorkflow 创建下载自动化工作流
func (am *AutomationManager) createDownloadAutomationWorkflow() *Workflow {
	return &Workflow{
		Name: "Download Automation",
		Config: WorkflowConfig{
			Enabled:      true,
			Interval:     30 * time.Minute, // 每30分钟执行一次
			MaxDuration:  15 * time.Minute,
			MaxRetries:   2,
			RetryDelay:   2 * time.Minute,
		},
		Steps: []WorkflowStep{
			{
				Name:        "monitor_downloads",
				Description: "监控下载任务状态",
				Handler:     am.monitorDownloadsStep,
				Timeout:     5 * time.Minute,
				Required:    true,
			},
			{
				Name:        "retry_failed_downloads",
				Description: "重试失败的下载",
				Handler:     am.retryFailedDownloadsStep,
				Timeout:     5 * time.Minute,
				Required:    false,
			},
			{
				Name:        "cleanup_completed",
				Description: "清理已完成的下载",
				Handler:     am.cleanupCompletedStep,
				Timeout:     3 * time.Minute,
				Required:    false,
			},
		},
	}
}

// createFileProcessingWorkflow 创建文件处理工作流
func (am *AutomationManager) createFileProcessingWorkflow() *Workflow {
	return &Workflow{
		Name: "File Processing Automation",
		Config: WorkflowConfig{
			Enabled:      am.config.FileProcessing.AutoUpload.Enabled,
			Interval:     time.Duration(am.config.FileProcessing.AutoUpload.ScanInterval) * time.Minute,
			MaxDuration:  20 * time.Minute,
			MaxRetries:   3,
			RetryDelay:   3 * time.Minute,
		},
		Steps: []WorkflowStep{
			{
				Name:        "scan_files",
				Description: "扫描待处理文件",
				Handler:     am.scanFilesStep,
				Timeout:     5 * time.Minute,
				Required:    true,
			},
			{
				Name:        "process_files",
				Description: "处理文件",
				Handler:     am.processFilesStep,
				Timeout:     10 * time.Minute,
				Required:    true,
			},
			{
				Name:        "upload_files",
				Description: "上传文件",
				Handler:     am.uploadFilesStep,
				Timeout:     10 * time.Minute,
				Required:    false,
			},
		},
	}
}

// runWorkflow 运行工作流
func (am *AutomationManager) runWorkflow(name string, workflow *Workflow) {
	ticker := time.NewTicker(workflow.Config.Interval)
	defer ticker.Stop()
	
	for {
		select {
		case <-am.ctx.Done():
			logger.Infof("Workflow %s stopped due to context cancellation", name)
			return
		case <-ticker.C:
			am.executeWorkflow(name, workflow)
		}
	}
}

// executeWorkflow 执行工作流
func (am *AutomationManager) executeWorkflow(name string, workflow *Workflow) {
	logger.Infof("Executing workflow: %s", name)
	startTime := time.Now()
	
	// 创建工作流上下文
	ctx, cancel := context.WithTimeout(am.ctx, workflow.Config.MaxDuration)
	defer cancel()
	
	// 执行工作流步骤
	result := am.executeWorkflowSteps(ctx, workflow)
	
	duration := time.Since(startTime)
	if result.Success {
		logger.Infof("Workflow %s completed successfully in %v: %s", name, duration, result.Summary)
	} else {
		logger.Errorf("Workflow %s failed in %v: %s", name, duration, result.Error)
		
		// 如果配置了重试，则安排重试
		if result.RetryCount < workflow.Config.MaxRetries {
			go am.retryWorkflow(name, workflow, result.RetryCount+1)
		}
	}
}

// executeWorkflowSteps 执行工作流步骤
func (am *AutomationManager) executeWorkflowSteps(ctx context.Context, workflow *Workflow) *WorkflowResult {
	result := &WorkflowResult{
		Success:       true,
		ExecutedSteps: 0,
		FailedSteps:   0,
	}
	
	for _, step := range workflow.Steps {
		// 检查步骤条件
		if !step.Condition {
			logger.Debugf("Skipping step %s due to condition", step.Name)
			continue
		}
		
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			result.Success = false
			result.Error = "workflow cancelled due to timeout"
			return result
		default:
		}
		
		// 执行步骤
		stepResult := am.executeWorkflowStep(ctx, &step)
		result.ExecutedSteps++
		
		if !stepResult.Success {
			result.FailedSteps++
			result.Error = fmt.Sprintf("step %s failed: %s", step.Name, stepResult.Error)
			
			if step.Required {
				result.Success = false
				return result
			}
		}
		
		// 更新结果摘要
		if result.Summary == "" {
			result.Summary = stepResult.Summary
		} else {
			result.Summary += "; " + stepResult.Summary
		}
	}
	
	return result
}

// executeWorkflowStep 执行工作流步骤
func (am *AutomationManager) executeWorkflowStep(ctx context.Context, step *WorkflowStep) *StepResult {
	logger.Infof("Executing step: %s", step.Name)
	
	// 创建步骤上下文
	stepCtx, cancel := context.WithTimeout(ctx, step.Timeout)
	defer cancel()
	
	// 执行步骤处理器
	result := step.Handler(stepCtx)
	
	if result.Success {
		logger.Infof("Step %s completed: %s", step.Name, result.Summary)
	} else {
		logger.Errorf("Step %s failed: %s", step.Name, result.Error)
	}
	
	return result
}

// retryWorkflow 重试工作流
func (am *AutomationManager) retryWorkflow(name string, workflow *Workflow, retryCount int) {
	logger.Infof("Retrying workflow %s (attempt %d/%d)", name, retryCount, workflow.Config.MaxRetries)
	
	// 等待重试延迟
	time.Sleep(workflow.Config.RetryDelay)
	
	// 重新执行工作流
	am.executeWorkflow(name, workflow)
}

// GetWorkflowStatus 获取工作流状态
func (am *AutomationManager) GetWorkflowStatus() map[string]*WorkflowStatus {
	am.mu.RLock()
	defer am.mu.RUnlock()
	
	status := make(map[string]*WorkflowStatus)
	for name, workflow := range am.workflows {
		status[name] = &WorkflowStatus{
			Name:        name,
			Enabled:     workflow.Config.Enabled,
			LastRun:     workflow.LastRun,
			NextRun:     workflow.LastRun.Add(workflow.Config.Interval),
			IsRunning:   workflow.IsRunning,
			LastResult:  workflow.LastResult,
		}
	}
	
	return status
}

// EnableWorkflow 启用工作流
func (am *AutomationManager) EnableWorkflow(name string) error {
	am.mu.Lock()
	defer am.mu.Unlock()
	
	workflow, exists := am.workflows[name]
	if !exists {
		return fmt.Errorf("workflow %s not found", name)
	}
	
	if !workflow.Config.Enabled {
		workflow.Config.Enabled = true
		if am.running {
			go am.runWorkflow(name, workflow)
		}
		logger.Infof("Enabled workflow: %s", name)
	}
	
	return nil
}

// DisableWorkflow 禁用工作流
func (am *AutomationManager) DisableWorkflow(name string) error {
	am.mu.Lock()
	defer am.mu.Unlock()
	
	workflow, exists := am.workflows[name]
	if !exists {
		return fmt.Errorf("workflow %s not found", name)
	}
	
	workflow.Config.Enabled = false
	logger.Infof("Disabled workflow: %s", name)
	
	return nil
}

// IsRunning 检查自动化管理器是否正在运行
func (am *AutomationManager) IsRunning() bool {
	am.mu.RLock()
	defer am.mu.RUnlock()
	return am.running
}