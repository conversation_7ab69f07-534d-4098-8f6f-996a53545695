package automation

import (
	"context"
	"fmt"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/logger"
)

// scrapeNewMoviesStep 采集新影片数据步骤
func (am *AutomationManager) scrapeNewMoviesStep(ctx context.Context) *StepResult {
	startTime := time.Now()
	result := &StepResult{
		StartTime: startTime,
		Data:      make(map[string]interface{}),
	}

	// 获取需要采集的影片代码
	movieCodes, err := am.getMovieCodesToScrape(ctx, 10) // 限制数量
	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("failed to get movie codes: %v", err)
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		return result
	}

	if len(movieCodes) == 0 {
		result.Success = true
		result.Summary = "no new movies to scrape"
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		return result
	}

	// 执行采集
	scraped := 0
	failed := 0
	for _, code := range movieCodes {
		select {
		case <-ctx.Done():
			result.Success = false
			result.Error = "context cancelled"
			result.EndTime = time.Now()
			result.Duration = time.Since(startTime)
			return result
		default:
		}

		if err := am.scrapeMovie(ctx, code); err != nil {
			logger.Errorf("Failed to scrape movie %s: %v", code, err)
			failed++
		} else {
			scraped++
		}
	}

	result.Success = true
	result.Summary = fmt.Sprintf("scraped %d movies, %d failed", scraped, failed)
	result.Data["scraped"] = scraped
	result.Data["failed"] = failed
	result.Data["total"] = len(movieCodes)
	result.EndTime = time.Now()
	result.Duration = time.Since(startTime)

	return result
}

// updateMagnetScoresStep 更新磁力链接评分步骤
func (am *AutomationManager) updateMagnetScoresStep(ctx context.Context) *StepResult {
	startTime := time.Now()
	result := &StepResult{
		StartTime: startTime,
		Data:      make(map[string]interface{}),
	}

	// 获取需要更新评分的磁力链接
	cutoffTime := time.Now().AddDate(0, 0, -7) // 7天前
	magnets, err := am.repo.JAVMagnet().GetForScoreUpdate(cutoffTime, 20)
	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("failed to get magnets for score update: %v", err)
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		return result
	}

	if len(magnets) == 0 {
		result.Success = true
		result.Summary = "no magnets need score update"
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		return result
	}

	// 更新评分
	updated := 0
	for _, magnet := range magnets {
		select {
		case <-ctx.Done():
			result.Success = false
			result.Error = "context cancelled"
			result.EndTime = time.Now()
			result.Duration = time.Since(startTime)
			return result
		default:
		}

		newScore := am.calculateMagnetScore(magnet)
		if newScore != magnet.Score {
			magnet.Score = newScore
			if err := am.repo.JAVMagnet().Update(magnet); err != nil {
				logger.Errorf("Failed to update magnet score %d: %v", magnet.ID, err)
				continue
			}
			updated++
		}
	}

	result.Success = true
	result.Summary = fmt.Sprintf("updated %d magnet scores", updated)
	result.Data["updated"] = updated
	result.Data["total"] = len(magnets)
	result.EndTime = time.Now()
	result.Duration = time.Since(startTime)

	return result
}

// createAutoDownloadsStep 创建自动下载任务步骤
func (am *AutomationManager) createAutoDownloadsStep(ctx context.Context) *StepResult {
	startTime := time.Now()
	result := &StepResult{
		StartTime: startTime,
		Data:      make(map[string]interface{}),
	}

	// 获取适合自动下载的影片
	movies, err := am.getMoviesForAutoDownload(ctx, 5) // 限制数量
	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("failed to get movies for auto download: %v", err)
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		return result
	}

	if len(movies) == 0 {
		result.Success = true
		result.Summary = "no movies suitable for auto download"
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		return result
	}

	// 创建下载任务
	created := 0
	for _, movie := range movies {
		select {
		case <-ctx.Done():
			result.Success = false
			result.Error = "context cancelled"
			result.EndTime = time.Now()
			result.Duration = time.Since(startTime)
			return result
		default:
		}

		if err := am.createDownloadForMovie(ctx, movie); err != nil {
			logger.Errorf("Failed to create download for movie %s: %v", movie.Code, err)
			continue
		}
		created++
	}

	result.Success = true
	result.Summary = fmt.Sprintf("created %d auto downloads", created)
	result.Data["created"] = created
	result.Data["total"] = len(movies)
	result.EndTime = time.Now()
	result.Duration = time.Since(startTime)

	return result
}

// monitorDownloadsStep 监控下载任务状态步骤
func (am *AutomationManager) monitorDownloadsStep(ctx context.Context) *StepResult {
	startTime := time.Now()
	result := &StepResult{
		StartTime: startTime,
		Data:      make(map[string]interface{}),
	}

	// 获取活跃的下载任务
	activeDownloads, err := am.getActiveDownloads(ctx)
	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("failed to get active downloads: %v", err)
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		return result
	}

	if len(activeDownloads) == 0 {
		result.Success = true
		result.Summary = "no active downloads to monitor"
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		return result
	}

	// 监控下载状态
	monitored := 0
	completed := 0
	failed := 0

	for _, download := range activeDownloads {
		select {
		case <-ctx.Done():
			result.Success = false
			result.Error = "context cancelled"
			result.EndTime = time.Now()
			result.Duration = time.Since(startTime)
			return result
		default:
		}

		status, err := am.checkDownloadStatus(ctx, download)
		if err != nil {
			logger.Errorf("Failed to check download status for %s: %v", download.MovieCode, err)
			continue
		}

		monitored++
		switch status {
		case "completed":
			completed++
		case "failed":
			failed++
		}
	}

	result.Success = true
	result.Summary = fmt.Sprintf("monitored %d downloads (%d completed, %d failed)", monitored, completed, failed)
	result.Data["monitored"] = monitored
	result.Data["completed"] = completed
	result.Data["failed"] = failed
	result.Data["total"] = len(activeDownloads)
	result.EndTime = time.Now()
	result.Duration = time.Since(startTime)

	return result
}

// retryFailedDownloadsStep 重试失败的下载步骤
func (am *AutomationManager) retryFailedDownloadsStep(ctx context.Context) *StepResult {
	startTime := time.Now()
	result := &StepResult{
		StartTime: startTime,
		Data:      make(map[string]interface{}),
	}

	// 获取失败的下载任务
	failedDownloads, err := am.getFailedDownloads(ctx, 5) // 限制数量
	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("failed to get failed downloads: %v", err)
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		return result
	}

	if len(failedDownloads) == 0 {
		result.Success = true
		result.Summary = "no failed downloads to retry"
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		return result
	}

	// 重试下载
	retried := 0
	for _, download := range failedDownloads {
		select {
		case <-ctx.Done():
			result.Success = false
			result.Error = "context cancelled"
			result.EndTime = time.Now()
			result.Duration = time.Since(startTime)
			return result
		default:
		}

		if err := am.retryDownload(ctx, download); err != nil {
			logger.Errorf("Failed to retry download for movie %s: %v", download.MovieCode, err)
			continue
		}
		retried++
	}

	result.Success = true
	result.Summary = fmt.Sprintf("retried %d failed downloads", retried)
	result.Data["retried"] = retried
	result.Data["total"] = len(failedDownloads)
	result.EndTime = time.Now()
	result.Duration = time.Since(startTime)

	return result
}

// cleanupCompletedStep 清理已完成的下载步骤
func (am *AutomationManager) cleanupCompletedStep(ctx context.Context) *StepResult {
	startTime := time.Now()
	result := &StepResult{
		StartTime: startTime,
		Data:      make(map[string]interface{}),
	}

	// 获取需要清理的已完成下载
	cutoffTime := time.Now().AddDate(0, 0, -7) // 7天前
	completedDownloads, err := am.getCompletedDownloadsForCleanup(ctx, cutoffTime)
	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("failed to get completed downloads for cleanup: %v", err)
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		return result
	}

	if len(completedDownloads) == 0 {
		result.Success = true
		result.Summary = "no completed downloads to cleanup"
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		return result
	}

	// 清理下载
	cleaned := 0
	for _, download := range completedDownloads {
		select {
		case <-ctx.Done():
			result.Success = false
			result.Error = "context cancelled"
			result.EndTime = time.Now()
			result.Duration = time.Since(startTime)
			return result
		default:
		}

		if err := am.cleanupDownload(ctx, download); err != nil {
			logger.Errorf("Failed to cleanup download %d: %v", download.ID, err)
			continue
		}
		cleaned++
	}

	result.Success = true
	result.Summary = fmt.Sprintf("cleaned %d completed downloads", cleaned)
	result.Data["cleaned"] = cleaned
	result.Data["total"] = len(completedDownloads)
	result.EndTime = time.Now()
	result.Duration = time.Since(startTime)

	return result
}

// scanFilesStep 扫描待处理文件步骤
func (am *AutomationManager) scanFilesStep(ctx context.Context) *StepResult {
	startTime := time.Now()
	result := &StepResult{
		StartTime: startTime,
		Data:      make(map[string]interface{}),
	}

	// 扫描待处理文件
	files, err := am.services.FileProcessing.ScanFiles()
	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("failed to scan files: %v", err)
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		return result
	}

	result.Success = true
	result.Summary = fmt.Sprintf("found %d files to process", len(files))
	result.Data["files_found"] = len(files)
	result.EndTime = time.Now()
	result.Duration = time.Since(startTime)

	return result
}

// processFilesStep 处理文件步骤
func (am *AutomationManager) processFilesStep(ctx context.Context) *StepResult {
	startTime := time.Now()
	result := &StepResult{
		StartTime: startTime,
		Data:      make(map[string]interface{}),
	}

	// 获取待处理文件
	files, err := am.services.FileProcessing.GetPendingFiles(10) // 限制数量
	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("failed to get pending files: %v", err)
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		return result
	}

	if len(files) == 0 {
		result.Success = true
		result.Summary = "no files to process"
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		return result
	}

	// 处理文件
	processed := 0
	failed := 0

	for _, file := range files {
		select {
		case <-ctx.Done():
			result.Success = false
			result.Error = "context cancelled"
			result.EndTime = time.Now()
			result.Duration = time.Since(startTime)
			return result
		default:
		}

		if err := am.services.FileProcessing.ProcessFile(file.ID); err != nil {
			logger.Errorf("Failed to process file %s: %v", file.FileName, err)
			failed++
		} else {
			processed++
		}
	}

	result.Success = true
	result.Summary = fmt.Sprintf("processed %d files, %d failed", processed, failed)
	result.Data["processed"] = processed
	result.Data["failed"] = failed
	result.Data["total"] = len(files)
	result.EndTime = time.Now()
	result.Duration = time.Since(startTime)

	return result
}

// uploadFilesStep 上传文件步骤
func (am *AutomationManager) uploadFilesStep(ctx context.Context) *StepResult {
	startTime := time.Now()
	result := &StepResult{
		StartTime: startTime,
		Data:      make(map[string]interface{}),
	}

	// 获取待上传文件
	files, err := am.services.FileProcessing.GetProcessedFiles(10) // 限制数量
	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("failed to get processed files: %v", err)
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		return result
	}

	if len(files) == 0 {
		result.Success = true
		result.Summary = "no files to upload"
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		return result
	}

	// 上传文件
	uploaded := 0
	failed := 0

	for _, file := range files {
		select {
		case <-ctx.Done():
			result.Success = false
			result.Error = "context cancelled"
			result.EndTime = time.Now()
			result.Duration = time.Since(startTime)
			return result
		default:
		}

		if err := am.services.AutoUpload.UploadFile(file.ID); err != nil {
			logger.Errorf("Failed to upload file %s: %v", file.FileName, err)
			failed++
		} else {
			uploaded++
		}
	}

	result.Success = true
	result.Summary = fmt.Sprintf("uploaded %d files, %d failed", uploaded, failed)
	result.Data["uploaded"] = uploaded
	result.Data["failed"] = failed
	result.Data["total"] = len(files)
	result.EndTime = time.Now()
	result.Duration = time.Since(startTime)

	return result
}

// 辅助方法

// getMovieCodesToScrape 获取需要采集的影片代码
func (am *AutomationManager) getMovieCodesToScrape(ctx context.Context, limit int) ([]string, error) {
	// 获取采集状态为失败或待处理的影片
	movies, err := am.repo.JAVMovie().GetByScrapingStatus([]model.JAVScrapingStatus{
		model.JAVScrapingStatusPending,
		model.JAVScrapingStatusFailed,
	}, limit)
	
	if err != nil {
		return nil, fmt.Errorf("failed to get movies by scraping status: %w", err)
	}

	var movieCodes []string
	for _, movie := range movies {
		movieCodes = append(movieCodes, movie.Code)
	}

	return movieCodes, nil
}

// scrapeMovie 采集影片数据
func (am *AutomationManager) scrapeMovie(ctx context.Context, code string) error {
	result, err := am.services.JAVScraper.ScrapeMovieByCode(code)
	if err != nil {
		return fmt.Errorf("failed to scrape movie %s: %w", code, err)
	}

	if !result.Success {
		return fmt.Errorf("scraping failed for movie %s: %s", code, result.Error)
	}

	return nil
}

// calculateMagnetScore 计算磁力链接评分
func (am *AutomationManager) calculateMagnetScore(magnet *model.JAVMagnet) float64 {
	// 基础评分逻辑（简化版）
	score := 50.0

	// 根据清晰度加分
	switch magnet.Quality {
	case model.JAVMagnetQuality4K:
		score += 30
	case model.JAVMagnetQuality1080p:
		score += 20
	case model.JAVMagnetQuality720p:
		score += 10
	}

	// 根据字幕加分
	if magnet.HasSubtitle {
		score += 15
	}

	// 根据种子数加分
	if magnet.Seeders > 0 {
		score += float64(magnet.Seeders) * 0.1
		if score > 100 {
			score = 100
		}
	}

	return score
}

// getMoviesForAutoDownload 获取适合自动下载的影片
func (am *AutomationManager) getMoviesForAutoDownload(ctx context.Context, limit int) ([]*model.JAVMovie, error) {
	// 这里需要实现复杂的查询逻辑
	// 暂时返回空列表
	return []*model.JAVMovie{}, nil
}

// createDownloadForMovie 为影片创建下载任务
func (am *AutomationManager) createDownloadForMovie(ctx context.Context, movie *model.JAVMovie) error {
	// 获取最佳磁力链接
	magnet, err := am.services.JAV.GetBestMagnet(movie.ID)
	if err != nil {
		return fmt.Errorf("failed to get best magnet: %w", err)
	}

	// 创建下载请求
	req := &service.CreateJAVDownloadRequest{
		MovieCode: movie.Code,
		MagnetID:  &magnet.ID,
		Options: &service.JAVDownloadOptions{
			Quality:        am.config.JAV.Download.PreferQuality,
			PreferSubtitle: am.config.JAV.Download.PreferSubtitle,
			AutoUpload:     am.config.JAV.Download.AutoUpload,
			Priority:       3,
		},
	}

	// 使用系统用户ID创建下载任务
	systemUserID := uint(1)
	_, err = am.services.JAVDownload.CreateJAVDownload(systemUserID, req)
	return err
}

// getActiveDownloads 获取活跃的下载任务
func (am *AutomationManager) getActiveDownloads(ctx context.Context) ([]*model.JAVDownload, error) {
	req := &service.ListJAVDownloadsRequest{
		Status:   "downloading,pending,queued",
		Page:     1,
		PageSize: 50,
	}

	downloads, _, err := am.services.JAVDownload.ListJAVDownloads(req)
	return downloads, err
}

// checkDownloadStatus 检查下载状态
func (am *AutomationManager) checkDownloadStatus(ctx context.Context, download *model.JAVDownload) (string, error) {
	if download.TaskID == nil {
		return "unknown", nil
	}

	task, err := am.services.Task.GetTask(*download.TaskID)
	if err != nil {
		return "unknown", err
	}

	switch task.Status {
	case model.TaskStatusCompleted:
		return "completed", nil
	case model.TaskStatusFailed:
		return "failed", nil
	case model.TaskStatusRunning:
		return "downloading", nil
	default:
		return "pending", nil
	}
}

// getFailedDownloads 获取失败的下载任务
func (am *AutomationManager) getFailedDownloads(ctx context.Context, limit int) ([]*model.JAVDownload, error) {
	req := &service.ListJAVDownloadsRequest{
		Status:   "failed",
		Page:     1,
		PageSize: limit,
	}

	downloads, _, err := am.services.JAVDownload.ListJAVDownloads(req)
	return downloads, err
}

// retryDownload 重试下载
func (am *AutomationManager) retryDownload(ctx context.Context, download *model.JAVDownload) error {
	req := &service.CreateJAVDownloadRequest{
		MovieCode: download.MovieCode,
		MagnetID:  download.MagnetID,
		Options: &service.JAVDownloadOptions{
			Quality:        am.config.JAV.Download.PreferQuality,
			PreferSubtitle: am.config.JAV.Download.PreferSubtitle,
			AutoUpload:     am.config.JAV.Download.AutoUpload,
			Priority:       2,
		},
	}

	_, err := am.services.JAVDownload.CreateJAVDownload(download.UserID, req)
	return err
}

// getCompletedDownloadsForCleanup 获取需要清理的已完成下载
func (am *AutomationManager) getCompletedDownloadsForCleanup(ctx context.Context, cutoffTime time.Time) ([]*model.JAVDownload, error) {
	// 这里需要实现查询逻辑
	// 暂时返回空列表
	return []*model.JAVDownload{}, nil
}

// cleanupDownload 清理下载
func (am *AutomationManager) cleanupDownload(ctx context.Context, download *model.JAVDownload) error {
	// 这里实现清理逻辑
	logger.Infof("Would cleanup download %d for movie %s", download.ID, download.MovieCode)
	return nil
}