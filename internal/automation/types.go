package automation

import (
	"context"
	"time"
)

// Workflow 工作流定义
type Workflow struct {
	Name       string         `json:"name"`
	Config     WorkflowConfig `json:"config"`
	Steps      []WorkflowStep `json:"steps"`
	LastRun    time.Time      `json:"last_run"`
	LastResult *WorkflowResult `json:"last_result"`
	IsRunning  bool           `json:"is_running"`
}

// WorkflowConfig 工作流配置
type WorkflowConfig struct {
	Enabled      bool          `json:"enabled"`       // 是否启用
	Interval     time.Duration `json:"interval"`      // 执行间隔
	MaxDuration  time.Duration `json:"max_duration"`  // 最大执行时间
	MaxRetries   int           `json:"max_retries"`   // 最大重试次数
	RetryDelay   time.Duration `json:"retry_delay"`   // 重试延迟
}

// WorkflowStep 工作流步骤
type WorkflowStep struct {
	Name        string                                `json:"name"`        // 步骤名称
	Description string                                `json:"description"` // 步骤描述
	Handler     func(context.Context) *StepResult    `json:"-"`           // 步骤处理器
	Timeout     time.Duration                         `json:"timeout"`     // 步骤超时时间
	Required    bool                                  `json:"required"`    // 是否必需
	Condition   bool                                  `json:"condition"`   // 执行条件
}

// WorkflowResult 工作流执行结果
type WorkflowResult struct {
	Success       bool      `json:"success"`        // 是否成功
	ExecutedSteps int       `json:"executed_steps"` // 已执行步骤数
	FailedSteps   int       `json:"failed_steps"`   // 失败步骤数
	Summary       string    `json:"summary"`        // 结果摘要
	Error         string    `json:"error"`          // 错误信息
	StartTime     time.Time `json:"start_time"`     // 开始时间
	EndTime       time.Time `json:"end_time"`       // 结束时间
	Duration      time.Duration `json:"duration"`   // 执行时长
	RetryCount    int       `json:"retry_count"`    // 重试次数
}

// StepResult 步骤执行结果
type StepResult struct {
	Success   bool      `json:"success"`    // 是否成功
	Summary   string    `json:"summary"`    // 结果摘要
	Error     string    `json:"error"`      // 错误信息
	StartTime time.Time `json:"start_time"` // 开始时间
	EndTime   time.Time `json:"end_time"`   // 结束时间
	Duration  time.Duration `json:"duration"` // 执行时长
	Data      map[string]interface{} `json:"data"` // 步骤数据
}

// WorkflowStatus 工作流状态
type WorkflowStatus struct {
	Name        string          `json:"name"`         // 工作流名称
	Enabled     bool            `json:"enabled"`      // 是否启用
	LastRun     time.Time       `json:"last_run"`     // 上次运行时间
	NextRun     time.Time       `json:"next_run"`     // 下次运行时间
	IsRunning   bool            `json:"is_running"`   // 是否正在运行
	LastResult  *WorkflowResult `json:"last_result"`  // 上次执行结果
}

// AutomationStats 自动化统计信息
type AutomationStats struct {
	TotalWorkflows    int                        `json:"total_workflows"`
	EnabledWorkflows  int                        `json:"enabled_workflows"`
	RunningWorkflows  int                        `json:"running_workflows"`
	WorkflowStats     map[string]*WorkflowStats  `json:"workflow_stats"`
}

// WorkflowStats 工作流统计信息
type WorkflowStats struct {
	Name              string    `json:"name"`
	TotalRuns         int       `json:"total_runs"`
	SuccessfulRuns    int       `json:"successful_runs"`
	FailedRuns        int       `json:"failed_runs"`
	AverageRunTime    time.Duration `json:"average_run_time"`
	LastSuccessfulRun time.Time `json:"last_successful_run"`
	LastFailedRun     time.Time `json:"last_failed_run"`
}

// WorkflowEvent 工作流事件
type WorkflowEvent struct {
	Type      WorkflowEventType `json:"type"`
	Workflow  string            `json:"workflow"`
	Step      string            `json:"step,omitempty"`
	Message   string            `json:"message"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Timestamp time.Time         `json:"timestamp"`
}

// WorkflowEventType 工作流事件类型
type WorkflowEventType string

const (
	WorkflowEventStarted   WorkflowEventType = "workflow_started"
	WorkflowEventCompleted WorkflowEventType = "workflow_completed"
	WorkflowEventFailed    WorkflowEventType = "workflow_failed"
	WorkflowEventRetrying  WorkflowEventType = "workflow_retrying"
	StepEventStarted       WorkflowEventType = "step_started"
	StepEventCompleted     WorkflowEventType = "step_completed"
	StepEventFailed        WorkflowEventType = "step_failed"
	StepEventSkipped       WorkflowEventType = "step_skipped"
)

// WorkflowTemplate 工作流模板
type WorkflowTemplate struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Category    string                 `json:"category"`
	Config      WorkflowConfig         `json:"config"`
	Steps       []WorkflowStepTemplate `json:"steps"`
	Variables   map[string]interface{} `json:"variables"`
}

// WorkflowStepTemplate 工作流步骤模板
type WorkflowStepTemplate struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Type        string                 `json:"type"`
	Config      map[string]interface{} `json:"config"`
	Timeout     time.Duration          `json:"timeout"`
	Required    bool                   `json:"required"`
	Condition   string                 `json:"condition"`
}

// WorkflowSchedule 工作流调度信息
type WorkflowSchedule struct {
	WorkflowName string    `json:"workflow_name"`
	CronExpr     string    `json:"cron_expr"`
	NextRun      time.Time `json:"next_run"`
	Enabled      bool      `json:"enabled"`
}

// WorkflowMetrics 工作流指标
type WorkflowMetrics struct {
	WorkflowName      string        `json:"workflow_name"`
	ExecutionCount    int64         `json:"execution_count"`
	SuccessCount      int64         `json:"success_count"`
	FailureCount      int64         `json:"failure_count"`
	AverageRunTime    time.Duration `json:"average_run_time"`
	MinRunTime        time.Duration `json:"min_run_time"`
	MaxRunTime        time.Duration `json:"max_run_time"`
	LastExecutionTime time.Time     `json:"last_execution_time"`
	SuccessRate       float64       `json:"success_rate"`
}

// StepMetrics 步骤指标
type StepMetrics struct {
	StepName          string        `json:"step_name"`
	WorkflowName      string        `json:"workflow_name"`
	ExecutionCount    int64         `json:"execution_count"`
	SuccessCount      int64         `json:"success_count"`
	FailureCount      int64         `json:"failure_count"`
	SkipCount         int64         `json:"skip_count"`
	AverageRunTime    time.Duration `json:"average_run_time"`
	LastExecutionTime time.Time     `json:"last_execution_time"`
	SuccessRate       float64       `json:"success_rate"`
}

// WorkflowExecution 工作流执行记录
type WorkflowExecution struct {
	ID           uint              `json:"id"`
	WorkflowName string            `json:"workflow_name"`
	StartTime    time.Time         `json:"start_time"`
	EndTime      *time.Time        `json:"end_time"`
	Status       ExecutionStatus   `json:"status"`
	Result       *WorkflowResult   `json:"result"`
	Steps        []StepExecution   `json:"steps"`
	CreatedAt    time.Time         `json:"created_at"`
	UpdatedAt    time.Time         `json:"updated_at"`
}

// StepExecution 步骤执行记录
type StepExecution struct {
	ID               uint            `json:"id"`
	WorkflowExecutionID uint         `json:"workflow_execution_id"`
	StepName         string          `json:"step_name"`
	StartTime        time.Time       `json:"start_time"`
	EndTime          *time.Time      `json:"end_time"`
	Status           ExecutionStatus `json:"status"`
	Result           *StepResult     `json:"result"`
	CreatedAt        time.Time       `json:"created_at"`
	UpdatedAt        time.Time       `json:"updated_at"`
}

// ExecutionStatus 执行状态
type ExecutionStatus string

const (
	ExecutionStatusPending   ExecutionStatus = "pending"
	ExecutionStatusRunning   ExecutionStatus = "running"
	ExecutionStatusCompleted ExecutionStatus = "completed"
	ExecutionStatusFailed    ExecutionStatus = "failed"
	ExecutionStatusCancelled ExecutionStatus = "cancelled"
	ExecutionStatusSkipped   ExecutionStatus = "skipped"
)

// WorkflowTrigger 工作流触发器
type WorkflowTrigger struct {
	Type       TriggerType            `json:"type"`
	Config     map[string]interface{} `json:"config"`
	Enabled    bool                   `json:"enabled"`
	LastFired  time.Time              `json:"last_fired"`
	NextFire   time.Time              `json:"next_fire"`
}

// TriggerType 触发器类型
type TriggerType string

const (
	TriggerTypeCron     TriggerType = "cron"
	TriggerTypeInterval TriggerType = "interval"
	TriggerTypeEvent    TriggerType = "event"
	TriggerTypeManual   TriggerType = "manual"
	TriggerTypeWebhook  TriggerType = "webhook"
)

// WorkflowVariable 工作流变量
type WorkflowVariable struct {
	Name        string      `json:"name"`
	Value       interface{} `json:"value"`
	Type        string      `json:"type"`
	Description string      `json:"description"`
	Required    bool        `json:"required"`
	Default     interface{} `json:"default"`
}

// WorkflowContext 工作流上下文
type WorkflowContext struct {
	WorkflowName string                 `json:"workflow_name"`
	ExecutionID  uint                   `json:"execution_id"`
	Variables    map[string]interface{} `json:"variables"`
	StepResults  map[string]*StepResult `json:"step_results"`
	StartTime    time.Time              `json:"start_time"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// WorkflowHook 工作流钩子
type WorkflowHook struct {
	Type     HookType                `json:"type"`
	Handler  func(*WorkflowContext)  `json:"-"`
	Enabled  bool                    `json:"enabled"`
}

// HookType 钩子类型
type HookType string

const (
	HookTypeBeforeWorkflow HookType = "before_workflow"
	HookTypeAfterWorkflow  HookType = "after_workflow"
	HookTypeBeforeStep     HookType = "before_step"
	HookTypeAfterStep      HookType = "after_step"
	HookTypeOnError        HookType = "on_error"
	HookTypeOnSuccess      HookType = "on_success"
)