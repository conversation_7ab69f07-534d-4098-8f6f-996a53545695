package model

import (
	"math"
	"regexp"
	"strings"
	"time"

	"gorm.io/gorm"
)

// JAVMagnetQuality 视频质量枚举
type JAVMagnetQuality string

const (
	JAVMagnetQuality4K     JAVMagnetQuality = "4K"     // 4K/UHD
	JAVMagnetQuality1080p  JAVMagnetQuality = "1080p"  // 1080p
	JAVMagnetQuality720p   JAVMagnetQuality = "720p"   // 720p
	JAVMagnetQuality480p   JAVMagnetQuality = "480p"   // 480p
	JAVMagnetQualityUnknown JAVMagnetQuality = "unknown" // 未知
)

// JAVMagnetSubtitleLanguage 字幕语言枚举
type JAVMagnetSubtitleLanguage string

const (
	JAVMagnetSubtitleLanguageChinese  JAVMagnetSubtitleLanguage = "chinese"  // 中文
	JAVMagnetSubtitleLanguageEnglish  JAVMagnetSubtitleLanguage = "english"  // 英文
	JAVMagnetSubtitleLanguageJapanese JAVMagnetSubtitleLanguage = "japanese" // 日文
	JAVMagnetSubtitleLanguageNone     JAVMagnetSubtitleLanguage = "none"     // 无字幕
)

// JAVMagnet JAV磁力链接模型
type JAVMagnet struct {
	ID               uint                      `json:"id" gorm:"primaryKey;autoIncrement"`
	MovieID          uint                      `json:"movie_id" gorm:"type:int;not null;comment:关联的影片ID"`
	MagnetURL        string                    `json:"magnet_url" gorm:"type:text;not null;comment:磁力链接URL"`
	FileName         string                    `json:"file_name" gorm:"type:varchar(500);comment:文件名"`
	FileSize         int64                     `json:"file_size" gorm:"type:bigint;comment:文件大小(字节)"`
	Quality          JAVMagnetQuality          `json:"quality" gorm:"type:varchar(20);comment:视频质量"`
	HasSubtitle      bool                      `json:"has_subtitle" gorm:"type:boolean;default:false;comment:是否有字幕"`
	SubtitleLanguage JAVMagnetSubtitleLanguage `json:"subtitle_language" gorm:"type:varchar(10);comment:字幕语言"`
	Source           string                    `json:"source" gorm:"type:varchar(100);comment:磁力来源"`
	Uploader         string                    `json:"uploader" gorm:"type:varchar(100);comment:上传者"`
	UploadDate       *time.Time                `json:"upload_date" gorm:"comment:上传日期"`
	Seeders          int                       `json:"seeders" gorm:"type:int;default:0;comment:做种数"`
	Leechers         int                       `json:"leechers" gorm:"type:int;default:0;comment:下载数"`
	Score            float64                   `json:"score" gorm:"type:decimal(5,2);default:0;comment:综合评分"`
	SizeScore        float64                   `json:"size_score" gorm:"type:decimal(5,2);default:0;comment:文件大小评分"`
	SubtitleScore    float64                   `json:"subtitle_score" gorm:"type:decimal(5,2);default:0;comment:字幕评分"`
	QualityScore     float64                   `json:"quality_score" gorm:"type:decimal(5,2);default:0;comment:清晰度评分"`
	SourceScore      float64                   `json:"source_score" gorm:"type:decimal(5,2);default:0;comment:来源可靠性评分"`
	IsSelected       bool                      `json:"is_selected" gorm:"type:boolean;default:false;comment:是否为选中的磁力链接"`
	CreatedAt        time.Time                 `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt        time.Time                 `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`

	// 关联关系
	Movie *JAVMovie `json:"movie,omitempty" gorm:"foreignKey:MovieID;references:ID"`
}

// TableName 指定表名
func (JAVMagnet) TableName() string {
	return "jav_magnets"
}

// MagnetSelectorWeights 磁力筛选算法权重配置
type MagnetSelectorWeights struct {
	SizeWeight     float64 `json:"size_weight"`     // 文件大小权重 (默认: 40%)
	SubtitleWeight float64 `json:"subtitle_weight"` // 字幕权重 (默认: 35%)
	QualityWeight  float64 `json:"quality_weight"`  // 清晰度权重 (默认: 15%)
	SourceWeight   float64 `json:"source_weight"`   // 来源可靠性权重 (默认: 10%)
}

// DefaultMagnetSelectorWeights 默认磁力筛选权重
var DefaultMagnetSelectorWeights = MagnetSelectorWeights{
	SizeWeight:     0.40,
	SubtitleWeight: 0.35,
	QualityWeight:  0.15,
	SourceWeight:   0.10,
}

// CalculateSizeScore 计算文件大小评分
func (m *JAVMagnet) CalculateSizeScore() float64 {
	if m.FileSize <= 0 {
		return 0
	}

	// 转换为GB
	sizeGB := float64(m.FileSize) / (1024 * 1024 * 1024)

	switch {
	case sizeGB >= 4.0:
		return 100.0 // >4GB: 100分
	case sizeGB >= 2.0:
		return 80.0 // 2-4GB: 80分
	case sizeGB >= 1.0:
		return 60.0 // 1-2GB: 60分
	default:
		return 30.0 // <1GB: 30分
	}
}

// CalculateSubtitleScore 计算字幕评分
func (m *JAVMagnet) CalculateSubtitleScore() float64 {
	if !m.HasSubtitle {
		return 0.0 // 无字幕: 0分
	}

	switch m.SubtitleLanguage {
	case JAVMagnetSubtitleLanguageChinese:
		return 100.0 // 中文字幕: 100分
	case JAVMagnetSubtitleLanguageEnglish:
		return 60.0 // 英文字幕: 60分
	default:
		return 30.0 // 其他字幕: 30分
	}
}

// CalculateQualityScore 计算清晰度评分
func (m *JAVMagnet) CalculateQualityScore() float64 {
	switch m.Quality {
	case JAVMagnetQuality4K:
		return 100.0 // 4K/UHD: 100分
	case JAVMagnetQuality1080p:
		return 80.0 // 1080p: 80分
	case JAVMagnetQuality720p:
		return 60.0 // 720p: 60分
	case JAVMagnetQuality480p:
		return 40.0 // 480p: 40分
	default:
		return 50.0 // 未知: 50分
	}
}

// CalculateSourceScore 计算来源可靠性评分
func (m *JAVMagnet) CalculateSourceScore() float64 {
	if m.Source == "" && m.Uploader == "" {
		return 50.0 // 未知来源: 50分
	}

	// 知名发布组列表
	knownGroups := []string{
		"FHD", "HD", "UNCENSORED", "LEAKED", "1080p", "720p",
		"javhd", "javfull", "javmost", "javfree", "javdoe",
		"thz", "sukebei", "nyaa", "rarbg", "1337x",
	}

	source := strings.ToLower(m.Source + " " + m.Uploader)
	for _, group := range knownGroups {
		if strings.Contains(source, strings.ToLower(group)) {
			return 100.0 // 知名发布组: 100分
		}
	}

	// 检查是否有明确的发布组标识
	if strings.Contains(source, "[") && strings.Contains(source, "]") {
		return 70.0 // 一般来源: 70分
	}

	return 50.0 // 未知来源: 50分
}

// CalculateScore 计算综合评分
func (m *JAVMagnet) CalculateScore(weights *MagnetSelectorWeights) float64 {
	if weights == nil {
		weights = &DefaultMagnetSelectorWeights
	}

	// 计算各项评分
	m.SizeScore = m.CalculateSizeScore()
	m.SubtitleScore = m.CalculateSubtitleScore()
	m.QualityScore = m.CalculateQualityScore()
	m.SourceScore = m.CalculateSourceScore()

	// 计算加权总分
	totalScore := m.SizeScore*weights.SizeWeight +
		m.SubtitleScore*weights.SubtitleWeight +
		m.QualityScore*weights.QualityWeight +
		m.SourceScore*weights.SourceWeight

	m.Score = math.Round(totalScore*100) / 100 // 保留两位小数
	return m.Score
}

// ParseQualityFromFileName 从文件名解析视频质量
func (m *JAVMagnet) ParseQualityFromFileName() {
	if m.FileName == "" {
		m.Quality = JAVMagnetQualityUnknown
		return
	}

	fileName := strings.ToLower(m.FileName)

	// 4K/UHD检测
	if regexp.MustCompile(`\b(4k|uhd|2160p)\b`).MatchString(fileName) {
		m.Quality = JAVMagnetQuality4K
		return
	}

	// 1080p检测
	if regexp.MustCompile(`\b1080p?\b`).MatchString(fileName) {
		m.Quality = JAVMagnetQuality1080p
		return
	}

	// 720p检测
	if regexp.MustCompile(`\b720p?\b`).MatchString(fileName) {
		m.Quality = JAVMagnetQuality720p
		return
	}

	// 480p检测
	if regexp.MustCompile(`\b480p?\b`).MatchString(fileName) {
		m.Quality = JAVMagnetQuality480p
		return
	}

	m.Quality = JAVMagnetQualityUnknown
}

// ParseSubtitleFromFileName 从文件名解析字幕信息
func (m *JAVMagnet) ParseSubtitleFromFileName() {
	if m.FileName == "" {
		m.HasSubtitle = false
		m.SubtitleLanguage = JAVMagnetSubtitleLanguageNone
		return
	}

	fileName := strings.ToLower(m.FileName)

	// 中文字幕检测
	chinesePatterns := []string{
		`\b(中文|中字|字幕|sub|subtitle)\b`,
		`\b(chs|cht|chinese)\b`,
		`\b(简体|繁体|简中|繁中)\b`,
	}

	for _, pattern := range chinesePatterns {
		if regexp.MustCompile(pattern).MatchString(fileName) {
			m.HasSubtitle = true
			m.SubtitleLanguage = JAVMagnetSubtitleLanguageChinese
			return
		}
	}

	// 英文字幕检测
	englishPatterns := []string{
		`\b(english|eng|en)\b`,
		`\b(sub|subtitle).*eng\b`,
	}

	for _, pattern := range englishPatterns {
		if regexp.MustCompile(pattern).MatchString(fileName) {
			m.HasSubtitle = true
			m.SubtitleLanguage = JAVMagnetSubtitleLanguageEnglish
			return
		}
	}

	// 日文字幕检测
	japanesePatterns := []string{
		`\b(japanese|jpn|jp)\b`,
		`\b(日文|日语|日字)\b`,
	}

	for _, pattern := range japanesePatterns {
		if regexp.MustCompile(pattern).MatchString(fileName) {
			m.HasSubtitle = true
			m.SubtitleLanguage = JAVMagnetSubtitleLanguageJapanese
			return
		}
	}

	// 默认无字幕
	m.HasSubtitle = false
	m.SubtitleLanguage = JAVMagnetSubtitleLanguageNone
}

// GetFormattedFileSize 获取格式化的文件大小
func (m *JAVMagnet) GetFormattedFileSize() string {
	if m.FileSize <= 0 {
		return "未知"
	}
	return formatBytes(m.FileSize)
}

// GetFormattedUploadDate 获取格式化的上传日期
func (m *JAVMagnet) GetFormattedUploadDate() string {
	if m.UploadDate == nil {
		return "未知"
	}
	return m.UploadDate.Format("2006-01-02")
}

// GetHealthScore 获取健康度评分（基于做种数和下载数）
func (m *JAVMagnet) GetHealthScore() float64 {
	if m.Seeders == 0 && m.Leechers == 0 {
		return 0.0
	}

	total := m.Seeders + m.Leechers
	if total == 0 {
		return 0.0
	}

	// 健康度 = 做种数 / (做种数 + 下载数) * 100
	return float64(m.Seeders) / float64(total) * 100
}

// BeforeCreate GORM钩子：创建前
func (m *JAVMagnet) BeforeCreate(tx *gorm.DB) error {
	// 自动解析文件名中的质量和字幕信息
	m.ParseQualityFromFileName()
	m.ParseSubtitleFromFileName()

	// 计算评分
	m.CalculateScore(nil)

	return nil
}

// BeforeUpdate GORM钩子：更新前
func (m *JAVMagnet) BeforeUpdate(tx *gorm.DB) error {
	// 重新计算评分
	m.CalculateScore(nil)
	return nil
}

// JAVMagnetProfile JAV磁力链接资料（用于API响应）
type JAVMagnetProfile struct {
	ID                    uint                      `json:"id"`
	MovieID               uint                      `json:"movie_id"`
	MagnetURL             string                    `json:"magnet_url"`
	FileName              string                    `json:"file_name"`
	FileSize              int64                     `json:"file_size"`
	FormattedFileSize     string                    `json:"formatted_file_size"`
	Quality               JAVMagnetQuality          `json:"quality"`
	HasSubtitle           bool                      `json:"has_subtitle"`
	SubtitleLanguage      JAVMagnetSubtitleLanguage `json:"subtitle_language"`
	Source                string                    `json:"source"`
	Uploader              string                    `json:"uploader"`
	UploadDate            *time.Time                `json:"upload_date"`
	FormattedUploadDate   string                    `json:"formatted_upload_date"`
	Seeders               int                       `json:"seeders"`
	Leechers              int                       `json:"leechers"`
	HealthScore           float64                   `json:"health_score"`
	Score                 float64                   `json:"score"`
	SizeScore             float64                   `json:"size_score"`
	SubtitleScore         float64                   `json:"subtitle_score"`
	QualityScore          float64                   `json:"quality_score"`
	SourceScore           float64                   `json:"source_score"`
	IsSelected            bool                      `json:"is_selected"`
	CreatedAt             time.Time                 `json:"created_at"`
	UpdatedAt             time.Time                 `json:"updated_at"`
}

// ToProfile 转换为磁力链接资料
func (m *JAVMagnet) ToProfile() *JAVMagnetProfile {
	return &JAVMagnetProfile{
		ID:                  m.ID,
		MovieID:             m.MovieID,
		MagnetURL:           m.MagnetURL,
		FileName:            m.FileName,
		FileSize:            m.FileSize,
		FormattedFileSize:   m.GetFormattedFileSize(),
		Quality:             m.Quality,
		HasSubtitle:         m.HasSubtitle,
		SubtitleLanguage:    m.SubtitleLanguage,
		Source:              m.Source,
		Uploader:            m.Uploader,
		UploadDate:          m.UploadDate,
		FormattedUploadDate: m.GetFormattedUploadDate(),
		Seeders:             m.Seeders,
		Leechers:            m.Leechers,
		HealthScore:         m.GetHealthScore(),
		Score:               m.Score,
		SizeScore:           m.SizeScore,
		SubtitleScore:       m.SubtitleScore,
		QualityScore:        m.QualityScore,
		SourceScore:         m.SourceScore,
		IsSelected:          m.IsSelected,
		CreatedAt:           m.CreatedAt,
		UpdatedAt:           m.UpdatedAt,
	}
}

// CreateJAVMagnetRequest 创建JAV磁力链接请求
type CreateJAVMagnetRequest struct {
	MovieID          uint                      `json:"movie_id" binding:"required"`
	MagnetURL        string                    `json:"magnet_url" binding:"required"`
	FileName         string                    `json:"file_name"`
	FileSize         int64                     `json:"file_size"`
	Quality          JAVMagnetQuality          `json:"quality"`
	HasSubtitle      bool                      `json:"has_subtitle"`
	SubtitleLanguage JAVMagnetSubtitleLanguage `json:"subtitle_language"`
	Source           string                    `json:"source"`
	Uploader         string                    `json:"uploader"`
	UploadDate       string                    `json:"upload_date"` // 格式: YYYY-MM-DD
	Seeders          int                       `json:"seeders"`
	Leechers         int                       `json:"leechers"`
}

// UpdateJAVMagnetRequest 更新JAV磁力链接请求
type UpdateJAVMagnetRequest struct {
	FileName         string                    `json:"file_name"`
	FileSize         int64                     `json:"file_size"`
	Quality          JAVMagnetQuality          `json:"quality"`
	HasSubtitle      bool                      `json:"has_subtitle"`
	SubtitleLanguage JAVMagnetSubtitleLanguage `json:"subtitle_language"`
	Source           string                    `json:"source"`
	Uploader         string                    `json:"uploader"`
	UploadDate       string                    `json:"upload_date"` // 格式: YYYY-MM-DD
	Seeders          int                       `json:"seeders"`
	Leechers         int                       `json:"leechers"`
	IsSelected       bool                      `json:"is_selected"`
}

// JAVMagnetListResponse JAV磁力链接列表响应
type JAVMagnetListResponse struct {
	Magnets    []JAVMagnetProfile `json:"magnets"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	PageSize   int                `json:"page_size"`
	TotalPages int                `json:"total_pages"`
}

// formatBytes函数已在task.go中定义，这里直接使用