package model

import (
	"time"

	"gorm.io/gorm"
)

// JAVGenre JAV分类标签模型
type JAVGenre struct {
	ID          uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	Name        string         `json:"name" gorm:"type:varchar(100);uniqueIndex;not null;comment:分类名称(中文)"`
	NameEn      string         `json:"name_en" gorm:"type:varchar(100);comment:分类名称(英文)"`
	NameJp      string         `json:"name_jp" gorm:"type:varchar(100);comment:分类名称(日文)"`
	Description string         `json:"description" gorm:"type:text;comment:分类描述"`
	CreatedAt   time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index;comment:删除时间"`

	// 关联关系
	Movies []JAVMovie `json:"movies,omitempty" gorm:"many2many:jav_movie_genres;"`
}

// TableName 指定表名
func (JAVGenre) TableName() string {
	return "jav_genres"
}

// GetDisplayName 获取显示名称（优先英文，其次中文）
func (g *JAVGenre) GetDisplayName() string {
	if g.NameEn != "" {
		return g.NameEn
	}
	return g.Name
}

// JAVGenreProfile JAV分类资料（用于API响应）
type JAVGenreProfile struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	NameEn      string    `json:"name_en"`
	NameJp      string    `json:"name_jp"`
	Description string    `json:"description"`
	DisplayName string    `json:"display_name"`
	MovieCount  int       `json:"movie_count,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
}

// ToProfile 转换为分类资料
func (g *JAVGenre) ToProfile() *JAVGenreProfile {
	return &JAVGenreProfile{
		ID:          g.ID,
		Name:        g.Name,
		NameEn:      g.NameEn,
		NameJp:      g.NameJp,
		Description: g.Description,
		DisplayName: g.GetDisplayName(),
		MovieCount:  len(g.Movies),
		CreatedAt:   g.CreatedAt,
	}
}

// CreateJAVGenreRequest 创建JAV分类请求
type CreateJAVGenreRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=100"`
	NameEn      string `json:"name_en"`
	NameJp      string `json:"name_jp"`
	Description string `json:"description"`
}

// UpdateJAVGenreRequest 更新JAV分类请求
type UpdateJAVGenreRequest struct {
	Name        string `json:"name"`
	NameEn      string `json:"name_en"`
	NameJp      string `json:"name_jp"`
	Description string `json:"description"`
}

// JAVGenreListResponse JAV分类列表响应
type JAVGenreListResponse struct {
	Genres     []JAVGenreProfile `json:"genres"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
	TotalPages int               `json:"total_pages"`
}