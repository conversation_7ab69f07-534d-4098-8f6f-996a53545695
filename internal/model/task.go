package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// TaskStatus 任务状态枚举
type TaskStatus string

const (
	TaskStatusPending    TaskStatus = "pending"    // 等待中
	TaskStatusRunning    TaskStatus = "running"    // 下载中
	TaskStatusPaused     TaskStatus = "paused"     // 已暂停
	TaskStatusCompleted  TaskStatus = "completed"  // 下载完成
	TaskStatusProcessing TaskStatus = "processing" // 文件处理中
	TaskStatusUploading  TaskStatus = "uploading"  // 分片上传中
	TaskStatusReady      TaskStatus = "ready"      // 播放列表就绪
	TaskStatusFailed     TaskStatus = "failed"     // 失败
	TaskStatusCancelled  TaskStatus = "cancelled"  // 已取消
)

// TaskPriority 任务优先级
type TaskPriority int

const (
	TaskPriorityLow    TaskPriority = 1 // 低优先级
	TaskPriorityNormal TaskPriority = 2 // 普通优先级
	TaskPriorityHigh   TaskPriority = 3 // 高优先级
	TaskPriorityUrgent TaskPriority = 4 // 紧急优先级
)

// ActualFileInfo 实际下载文件信息
type ActualFileInfo struct {
	Index           int    `json:"index"`            // 文件索引
	Path            string `json:"path"`             // 文件完整路径
	Name            string `json:"name"`             // 文件名
	Size            int64  `json:"size"`             // 文件大小
	CompletedLength int64  `json:"completed_length"` // 已完成大小
	Selected        bool   `json:"selected"`         // 是否选中下载
}

// ActualFiles 实际文件列表类型，用于JSONB存储
type ActualFiles []ActualFileInfo

// Value 实现driver.Valuer接口，用于数据库存储
func (af ActualFiles) Value() (driver.Value, error) {
	if af == nil {
		return nil, nil
	}
	return json.Marshal(af)
}

// Scan 实现sql.Scanner接口，用于数据库读取
func (af *ActualFiles) Scan(value interface{}) error {
	if value == nil {
		*af = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into ActualFiles", value)
	}

	return json.Unmarshal(bytes, af)
}

// DownloadTask 下载任务模型
type DownloadTask struct {
	ID             uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	MagnetURI      string         `json:"magnet_uri" gorm:"type:text;not null;comment:磁力链接"`
	TaskName       string         `json:"task_name" gorm:"type:varchar(255);not null;comment:任务名称"`
	Status         TaskStatus     `json:"status" gorm:"type:varchar(20);default:'pending';comment:任务状态"`
	Priority       TaskPriority   `json:"priority" gorm:"type:int;default:2;comment:优先级"`
	Progress       float64        `json:"progress" gorm:"type:decimal(5,2);default:0;comment:下载进度(0-100)"`
	DownloadSpeed  int64          `json:"download_speed" gorm:"type:bigint;default:0;comment:下载速度(bytes/s)"`
	UploadSpeed    int64          `json:"upload_speed" gorm:"type:bigint;default:0;comment:上传速度(bytes/s)"`
	TotalSize      int64          `json:"total_size" gorm:"type:bigint;default:0;comment:总大小(bytes)"`
	DownloadedSize int64          `json:"downloaded_size" gorm:"type:bigint;default:0;comment:已下载大小(bytes)"`
	UploadedSize   int64          `json:"uploaded_size" gorm:"type:bigint;default:0;comment:已上传大小(bytes)"`
	ETA            *time.Duration `json:"eta" gorm:"type:bigint;comment:预计剩余时间(秒)"`
	Aria2GID       string         `json:"aria2_gid" gorm:"type:varchar(64);comment:aria2任务ID"`
	SavePath       string         `json:"save_path" gorm:"type:varchar(500);comment:保存路径"`
	ErrorMessage   string         `json:"error_message" gorm:"type:text;comment:错误信息"`
	RetryCount     int            `json:"retry_count" gorm:"type:int;default:0;comment:重试次数"`
	MaxRetries     int            `json:"max_retries" gorm:"type:int;default:3;comment:最大重试次数"`
	UserID         uint           `json:"user_id" gorm:"type:int;not null;comment:用户ID"`
	StartedAt      *time.Time     `json:"started_at" gorm:"comment:开始时间"`
	CompletedAt    *time.Time     `json:"completed_at" gorm:"comment:完成时间"`
	CreatedAt      time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt      time.Time      `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt      gorm.DeletedAt `json:"deleted_at" gorm:"index;comment:删除时间"`

	// 文件处理相关字段
	ProcessingStatus      string     `json:"processing_status" gorm:"type:varchar(50);default:'';comment:文件处理状态"`
	ChunkCount            int        `json:"chunk_count" gorm:"type:int;default:0;comment:分片总数"`
	UploadedChunks        int        `json:"uploaded_chunks" gorm:"type:int;default:0;comment:已上传分片数"`
	ProcessingProgress    float64    `json:"processing_progress" gorm:"type:decimal(5,2);default:0;comment:处理进度(0-100)"`
	PlaylistURL           string     `json:"playlist_url" gorm:"type:text;comment:播放列表URL"`
	EncryptionKey         string     `json:"encryption_key" gorm:"type:varchar(255);comment:加密密钥(加密存储)"`
	ProcessingStartedAt   *time.Time `json:"processing_started_at" gorm:"comment:处理开始时间"`
	ProcessingCompletedAt *time.Time `json:"processing_completed_at" gorm:"comment:处理完成时间"`

	// 新增字段：实际文件信息和工作目录
	ActualFiles       ActualFiles `json:"actual_files" gorm:"type:jsonb;comment:实际下载的文件列表"`
	ProcessingWorkDir string      `json:"processing_work_dir" gorm:"type:varchar(500);default:'';comment:文件处理工作目录"`

	// MixFile相关字段
	IndexURL    string `json:"index_url" gorm:"type:text;comment:MixFile索引文件URL"`
	ShareCode   string `json:"share_code" gorm:"type:text;comment:MixFile分享码"`
	MixFileMode bool   `json:"mixfile_mode" gorm:"type:boolean;default:false;comment:是否为MixFile模式"`

	// 直接播放链接字段
	PlayURL       string `json:"play_url" gorm:"type:text;comment:主要播放链接"`
	DoodStreamURL string `json:"doodstream_url" gorm:"type:text;comment:DoodStream播放链接"`
	StreamTapeURL string `json:"streamtape_url" gorm:"type:text;comment:StreamTape播放链接"`
	StreamHGURL   string `json:"streamhg_url" gorm:"type:text;comment:StreamHG播放链接"`
	VOEURL        string `json:"voe_url" gorm:"type:text;comment:VOE播放链接(已弃用,由独立脚本处理)"`

	// 关联关系
	User *User `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
}

// TableName 指定表名
func (DownloadTask) TableName() string {
	return "download_tasks"
}

// IsActive 检查任务是否处于活跃状态
func (t *DownloadTask) IsActive() bool {
	return t.Status == TaskStatusRunning || t.Status == TaskStatusPending ||
		t.Status == TaskStatusProcessing || t.Status == TaskStatusUploading
}

// IsCompleted 检查任务是否已完成
func (t *DownloadTask) IsCompleted() bool {
	return t.Status == TaskStatusCompleted
}

// IsReady 检查任务是否已就绪（播放列表可用）
func (t *DownloadTask) IsReady() bool {
	return t.Status == TaskStatusReady
}

// IsProcessing 检查任务是否正在处理
func (t *DownloadTask) IsProcessing() bool {
	return t.Status == TaskStatusProcessing || t.Status == TaskStatusUploading
}

// IsFailed 检查任务是否失败
func (t *DownloadTask) IsFailed() bool {
	return t.Status == TaskStatusFailed
}

// CanRetry 检查任务是否可以重试
func (t *DownloadTask) CanRetry() bool {
	return t.IsFailed() && t.RetryCount < t.MaxRetries
}

// GetProgressPercentage 获取进度百分比字符串
func (t *DownloadTask) GetProgressPercentage() string {
	return fmt.Sprintf("%.2f%%", t.Progress)
}

// GetFormattedSize 获取格式化的文件大小
func (t *DownloadTask) GetFormattedSize() string {
	return formatBytes(t.TotalSize)
}

// GetFormattedDownloadedSize 获取格式化的已下载大小
func (t *DownloadTask) GetFormattedDownloadedSize() string {
	return formatBytes(t.DownloadedSize)
}

// GetFormattedSpeed 获取格式化的下载速度
func (t *DownloadTask) GetFormattedSpeed() string {
	return formatBytes(t.DownloadSpeed) + "/s"
}

// GetProcessingProgressPercentage 获取处理进度百分比字符串
func (t *DownloadTask) GetProcessingProgressPercentage() string {
	return fmt.Sprintf("%.2f%%", t.ProcessingProgress)
}

// GetUploadProgress 获取上传进度百分比
func (t *DownloadTask) GetUploadProgress() float64 {
	if t.ChunkCount == 0 {
		return 0.0
	}
	return float64(t.UploadedChunks) / float64(t.ChunkCount) * 100.0
}

// GetUploadProgressPercentage 获取上传进度百分比字符串
func (t *DownloadTask) GetUploadProgressPercentage() string {
	return fmt.Sprintf("%.2f%%", t.GetUploadProgress())
}

// IsDownloadCompleted 检查下载是否完成
func (t *DownloadTask) IsDownloadCompleted() bool {
	return t.Status == TaskStatusCompleted || t.Status == TaskStatusProcessing ||
		t.Status == TaskStatusUploading || t.Status == TaskStatusReady
}

// CanStartProcessing 检查是否可以开始文件处理
func (t *DownloadTask) CanStartProcessing() bool {
	return t.Status == TaskStatusCompleted && t.ProcessingStatus == ""
}

// GetActualFiles 获取实际下载的文件列表
func (t *DownloadTask) GetActualFiles() []ActualFileInfo {
	if t.ActualFiles == nil {
		return []ActualFileInfo{}
	}
	return []ActualFileInfo(t.ActualFiles)
}

// GetSelectedFiles 获取选中下载的文件列表
func (t *DownloadTask) GetSelectedFiles() []ActualFileInfo {
	var selectedFiles []ActualFileInfo
	for _, file := range t.GetActualFiles() {
		if file.Selected {
			selectedFiles = append(selectedFiles, file)
		}
	}
	return selectedFiles
}

// GetPrimaryFile 获取主要文件（第一个选中的文件）
func (t *DownloadTask) GetPrimaryFile() *ActualFileInfo {
	selectedFiles := t.GetSelectedFiles()
	if len(selectedFiles) > 0 {
		return &selectedFiles[0]
	}
	return nil
}

// HasActualFiles 检查是否有实际文件信息
func (t *DownloadTask) HasActualFiles() bool {
	return len(t.GetActualFiles()) > 0
}

// SetActualFiles 设置实际文件列表
func (t *DownloadTask) SetActualFiles(files []ActualFileInfo) {
	t.ActualFiles = ActualFiles(files)
}

// formatBytes 格式化字节数
func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// BeforeCreate GORM钩子：创建前
func (t *DownloadTask) BeforeCreate(tx *gorm.DB) error {
	// 设置默认值
	if t.Status == "" {
		t.Status = TaskStatusPending
	}
	if t.Priority == 0 {
		t.Priority = TaskPriorityNormal
	}
	if t.MaxRetries == 0 {
		t.MaxRetries = 3
	}
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (t *DownloadTask) BeforeUpdate(tx *gorm.DB) error {
	now := time.Now()

	// 如果状态变为运行中，设置开始时间
	if t.Status == TaskStatusRunning && t.StartedAt == nil {
		t.StartedAt = &now
	}

	// 如果状态变为已完成，设置完成时间
	if t.Status == TaskStatusCompleted && t.CompletedAt == nil {
		t.CompletedAt = &now
		t.Progress = 100.0
	}

	// 如果状态变为处理中，设置处理开始时间
	if t.Status == TaskStatusProcessing && t.ProcessingStartedAt == nil {
		t.ProcessingStartedAt = &now
		t.ProcessingStatus = "chunking"
	}

	// 如果状态变为就绪，设置处理完成时间
	if t.Status == TaskStatusReady && t.ProcessingCompletedAt == nil {
		t.ProcessingCompletedAt = &now
		t.ProcessingProgress = 100.0
		t.ProcessingStatus = "completed"
	}

	return nil
}
