package model

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// JAVScrapingStatus JAV数据采集状态枚举
type JAVScrapingStatus string

const (
	JAVScrapingStatusPending            JAVScrapingStatus = "pending"             // 等待采集
	JAVScrapingStatusRunning            JAVScrapingStatus = "running"             // 采集中
	JAVScrapingStatusInProgress         JAVScrapingStatus = "in_progress"         // 采集中(别名)
	JAVScrapingStatusCompleted          JAVScrapingStatus = "completed"           // 采集完成
	JAVScrapingStatusFailed             JAVScrapingStatus = "failed"              // 采集失败
	JAVScrapingStatusPartial            JAVScrapingStatus = "partial"             // 部分采集
	JAVScrapingStatusPartiallyCompleted JAVScrapingStatus = "partially_completed" // 部分完成
)

// JAVScrapingSource JAV数据来源枚举
type JAVScrapingSource string

const (
	JAVScrapingSourceJavBus    JAVScrapingSource = "javbus"    // JavBus
	JAVScrapingSourceJavinizer JAVScrapingSource = "javinizer" // Javinizer
	JAVScrapingSourceJavSP     JAVScrapingSource = "javsp"     // JavSP
	JAVScrapingSourceMixed     JAVScrapingSource = "mixed"     // 混合数据源
	JAVScrapingSourceMerged    JAVScrapingSource = "merged"    // 融合数据源
)

// JAVMovie JAV影片模型
type JAVMovie struct {
	ID                uint              `json:"id" gorm:"primaryKey;autoIncrement"`
	Code              string            `json:"code" gorm:"type:varchar(50);uniqueIndex;not null;comment:影片番号"`
	Title             string            `json:"title" gorm:"type:varchar(500);comment:影片标题(日文)"`
	TitleEn           string            `json:"title_en" gorm:"type:varchar(500);comment:影片标题(英文)"`
	ReleaseDate       *time.Time        `json:"release_date" gorm:"comment:发行日期"`
	Duration          int               `json:"duration" gorm:"type:int;comment:影片时长(分钟)"`
	Studio            string            `json:"studio" gorm:"type:varchar(200);comment:制作公司"`
	Series            string            `json:"series" gorm:"type:varchar(200);comment:系列名称"`
	Director          string            `json:"director" gorm:"type:varchar(200);comment:导演"`
	CoverURL          string            `json:"cover_url" gorm:"type:text;comment:封面图片URL"`
	PosterURL         string            `json:"poster_url" gorm:"type:text;comment:海报图片URL"`
	Plot              string            `json:"plot" gorm:"type:text;comment:剧情简介(日文)"`
	PlotEn            string            `json:"plot_en" gorm:"type:text;comment:剧情简介(英文)"`
	Rating            float64           `json:"rating" gorm:"type:decimal(3,1);comment:评分(1-10)"`
	SelectedMagnetURL string            `json:"selected_magnet_url" gorm:"type:text;comment:筛选出的最优磁力链接"`
	StreamTapeURL     string            `json:"streamtape_url" gorm:"column:streamtape_url;type:text;comment:StreamTape播放链接"`
	StreamHGURL       string            `json:"streamhg_url" gorm:"column:streamhg_url;type:text;comment:StreamHG播放链接"`
	VOEURL            string            `json:"voe_url" gorm:"column:voe_url;type:text;comment:VOE播放链接(已弃用,由独立脚本处理)"`
	DownloadTaskID    *uint             `json:"download_task_id" gorm:"type:int;comment:关联的下载任务ID"`
	ScrapingStatus    JAVScrapingStatus `json:"scraping_status" gorm:"type:varchar(20);default:'pending';comment:数据采集状态"`
	ScrapingSource    JAVScrapingSource `json:"scraping_source" gorm:"type:varchar(50);comment:数据来源"`
	LastScrapedAt     *time.Time        `json:"last_scraped_at" gorm:"comment:最后采集时间"`
	CreatedAt         time.Time         `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt         time.Time         `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt         gorm.DeletedAt    `json:"deleted_at" gorm:"index;comment:删除时间"`

	// 关联关系
	Actors       []JAVActor    `json:"actors,omitempty" gorm:"many2many:jav_movie_actors;"`
	Magnets      []JAVMagnet   `json:"magnets,omitempty" gorm:"foreignKey:MovieID"`
	Genres       []JAVGenre    `json:"genres,omitempty" gorm:"many2many:jav_movie_genres;"`
	DownloadTask *DownloadTask `json:"download_task,omitempty" gorm:"foreignKey:DownloadTaskID;references:ID"`
}

// TableName 指定表名
func (JAVMovie) TableName() string {
	return "jav_movies"
}

// IsScrapingCompleted 检查数据采集是否完成
func (m *JAVMovie) IsScrapingCompleted() bool {
	return m.ScrapingStatus == JAVScrapingStatusCompleted
}

// IsScrapingFailed 检查数据采集是否失败
func (m *JAVMovie) IsScrapingFailed() bool {
	return m.ScrapingStatus == JAVScrapingStatusFailed
}

// IsScrapingPending 检查是否等待采集
func (m *JAVMovie) IsScrapingPending() bool {
	return m.ScrapingStatus == JAVScrapingStatusPending
}

// IsScrapingRunning 检查是否正在采集
func (m *JAVMovie) IsScrapingRunning() bool {
	return m.ScrapingStatus == JAVScrapingStatusRunning
}

// HasStreamingLinks 检查是否有播放链接
func (m *JAVMovie) HasStreamingLinks() bool {
	return m.StreamTapeURL != "" || m.StreamHGURL != ""
}

// HasMagnetLink 检查是否有磁力链接
func (m *JAVMovie) HasMagnetLink() bool {
	return m.SelectedMagnetURL != ""
}

// IsDownloaded 检查是否已下载
func (m *JAVMovie) IsDownloaded() bool {
	return m.DownloadTaskID != nil && m.DownloadTask != nil && m.DownloadTask.IsCompleted()
}

// GetPrimaryStreamingURL 获取主要播放链接
func (m *JAVMovie) GetPrimaryStreamingURL() string {
	if m.StreamTapeURL != "" {
		return m.StreamTapeURL
	}
	return m.StreamHGURL
}

// GetFormattedDuration 获取格式化的时长
func (m *JAVMovie) GetFormattedDuration() string {
	if m.Duration <= 0 {
		return "未知"
	}
	hours := m.Duration / 60
	minutes := m.Duration % 60
	if hours > 0 {
		return fmt.Sprintf("%d小时%d分钟", hours, minutes)
	}
	return fmt.Sprintf("%d分钟", minutes)
}

// GetFormattedReleaseDate 获取格式化的发行日期
func (m *JAVMovie) GetFormattedReleaseDate() string {
	if m.ReleaseDate == nil {
		return "未知"
	}
	return m.ReleaseDate.Format("2006-01-02")
}

// BeforeCreate GORM钩子：创建前
func (m *JAVMovie) BeforeCreate(tx *gorm.DB) error {
	// 设置默认值
	if m.ScrapingStatus == "" {
		m.ScrapingStatus = JAVScrapingStatusPending
	}
	return nil
}

// JAVMovieProfile JAV影片资料（用于API响应）
type JAVMovieProfile struct {
	ID                uint              `json:"id"`
	Code              string            `json:"code"`
	Title             string            `json:"title"`
	TitleEn           string            `json:"title_en"`
	ReleaseDate       *time.Time        `json:"release_date"`
	Duration          int               `json:"duration"`
	Studio            string            `json:"studio"`
	Series            string            `json:"series"`
	Director          string            `json:"director"`
	CoverURL          string            `json:"cover_url"`
	PosterURL         string            `json:"poster_url"`
	Plot              string            `json:"plot"`
	PlotEn            string            `json:"plot_en"`
	Rating            float64           `json:"rating"`
	StreamTapeURL     string            `json:"streamtape_url"`
	StreamHGURL       string            `json:"streamhg_url"`
	ScrapingStatus    JAVScrapingStatus `json:"scraping_status"`
	ScrapingSource    JAVScrapingSource `json:"scraping_source"`
	LastScrapedAt     *time.Time        `json:"last_scraped_at"`
	CreatedAt         time.Time         `json:"created_at"`
	UpdatedAt         time.Time         `json:"updated_at"`
	Actors            []JAVActorProfile `json:"actors,omitempty"`
	Genres            []JAVGenreProfile `json:"genres,omitempty"`
	HasStreamingLinks bool              `json:"has_streaming_links"`
	HasMagnetLink     bool              `json:"has_magnet_link"`
	IsDownloaded      bool              `json:"is_downloaded"`
}

// ToProfile 转换为影片资料
func (m *JAVMovie) ToProfile() *JAVMovieProfile {
	profile := &JAVMovieProfile{
		ID:                m.ID,
		Code:              m.Code,
		Title:             m.Title,
		TitleEn:           m.TitleEn,
		ReleaseDate:       m.ReleaseDate,
		Duration:          m.Duration,
		Studio:            m.Studio,
		Series:            m.Series,
		Director:          m.Director,
		CoverURL:          m.CoverURL,
		PosterURL:         m.PosterURL,
		Plot:              m.Plot,
		PlotEn:            m.PlotEn,
		Rating:            m.Rating,
		StreamTapeURL:     m.StreamTapeURL,
		StreamHGURL:       m.StreamHGURL,
		ScrapingStatus:    m.ScrapingStatus,
		ScrapingSource:    m.ScrapingSource,
		LastScrapedAt:     m.LastScrapedAt,
		CreatedAt:         m.CreatedAt,
		UpdatedAt:         m.UpdatedAt,
		HasStreamingLinks: m.HasStreamingLinks(),
		HasMagnetLink:     m.HasMagnetLink(),
		IsDownloaded:      m.IsDownloaded(),
	}

	// 注意：演员和分类信息的转换将在服务层处理，避免循环引用
	// 这里暂时不处理关联数据的转换

	return profile
}

// CreateJAVMovieRequest 创建JAV影片请求
type CreateJAVMovieRequest struct {
	Code        string  `json:"code" binding:"required,min=3,max=50"`
	Title       string  `json:"title"`
	TitleEn     string  `json:"title_en"`
	ReleaseDate string  `json:"release_date"` // 格式: YYYY-MM-DD
	Duration    int     `json:"duration"`
	Studio      string  `json:"studio"`
	Series      string  `json:"series"`
	Director    string  `json:"director"`
	CoverURL    string  `json:"cover_url"`
	PosterURL   string  `json:"poster_url"`
	Plot        string  `json:"plot"`
	PlotEn      string  `json:"plot_en"`
	Rating      float64 `json:"rating"`
	ActorIDs    []uint  `json:"actor_ids"`
	GenreIDs    []uint  `json:"genre_ids"`
}

// UpdateJAVMovieRequest 更新JAV影片请求
type UpdateJAVMovieRequest struct {
	Title             string            `json:"title"`
	TitleEn           string            `json:"title_en"`
	ReleaseDate       string            `json:"release_date"` // 格式: YYYY-MM-DD
	Duration          int               `json:"duration"`
	Studio            string            `json:"studio"`
	Series            string            `json:"series"`
	Director          string            `json:"director"`
	CoverURL          string            `json:"cover_url"`
	PosterURL         string            `json:"poster_url"`
	Plot              string            `json:"plot"`
	PlotEn            string            `json:"plot_en"`
	Rating            float64           `json:"rating"`
	SelectedMagnetURL string            `json:"selected_magnet_url"`
	StreamTapeURL     string            `json:"streamtape_url"`
	StreamHGURL       string            `json:"streamhg_url"`
	ScrapingStatus    JAVScrapingStatus `json:"scraping_status"`
	ScrapingSource    JAVScrapingSource `json:"scraping_source"`
	ActorIDs          []uint            `json:"actor_ids"`
	GenreIDs          []uint            `json:"genre_ids"`
}

// JAVMovieSearchRequest JAV影片搜索请求
type JAVMovieSearchRequest struct {
	Keyword         string            `json:"keyword" form:"keyword"`
	Studio          string            `json:"studio" form:"studio"`
	Series          string            `json:"series" form:"series"`
	ActorName       string            `json:"actor_name" form:"actor_name"`
	Genre           string            `json:"genre" form:"genre"`
	ScrapingStatus  JAVScrapingStatus `json:"scraping_status" form:"scraping_status"`
	HasStreaming    *bool             `json:"has_streaming" form:"has_streaming"`
	HasMagnet       *bool             `json:"has_magnet" form:"has_magnet"`
	IsDownloaded    *bool             `json:"is_downloaded" form:"is_downloaded"`
	ReleaseDateFrom string            `json:"release_date_from" form:"release_date_from"` // 格式: YYYY-MM-DD
	ReleaseDateTo   string            `json:"release_date_to" form:"release_date_to"`     // 格式: YYYY-MM-DD
	Page            int               `json:"page" form:"page"`
	PageSize        int               `json:"page_size" form:"page_size"`
	SortBy          string            `json:"sort_by" form:"sort_by"`       // release_date, created_at, rating, title
	SortOrder       string            `json:"sort_order" form:"sort_order"` // asc, desc
}

// JAVMovieListResponse JAV影片列表响应
type JAVMovieListResponse struct {
	Movies     []JAVMovieProfile `json:"movies"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
	TotalPages int               `json:"total_pages"`
}
