package model

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// JAVActorActiveStatus JAV演员活跃状态枚举
type JAVActorActiveStatus string

const (
	JAVActorActiveStatusActive   JAVActorActiveStatus = "active"   // 活跃
	JAVActorActiveStatusInactive JAVActorActiveStatus = "inactive" // 非活跃
	JAVActorActiveStatusRetired  JAVActorActiveStatus = "retired"  // 已退役
	JAVActorActiveStatusUnknown  JAVActorActiveStatus = "unknown"  // 未知状态
)

// JAVActor JAV演员模型
type JAVActor struct {
	ID           uint                 `json:"id" gorm:"primaryKey;autoIncrement"`
	Name         string               `json:"name" gorm:"type:varchar(200);not null;comment:演员姓名(日文)"`
	NameEn       string               `json:"name_en" gorm:"type:varchar(200);comment:演员姓名(英文)"`
	NameJp       string               `json:"name_jp" gorm:"type:varchar(200);comment:演员姓名(日文假名)"`
	AvatarURL    string               `json:"avatar_url" gorm:"type:text;comment:头像图片URL"`
	BirthDate    *time.Time           `json:"birth_date" gorm:"comment:出生日期"`
	Height       int                  `json:"height" gorm:"type:int;comment:身高(cm)"`
	Bust         int                  `json:"bust" gorm:"type:int;comment:胸围(cm)"`
	Waist        int                  `json:"waist" gorm:"type:int;comment:腰围(cm)"`
	Hip          int                  `json:"hip" gorm:"type:int;comment:臀围(cm)"`
	BloodType    string               `json:"blood_type" gorm:"type:varchar(5);comment:血型"`
	Hobby        string               `json:"hobby" gorm:"type:text;comment:兴趣爱好"`
	DebutDate    *time.Time           `json:"debut_date" gorm:"comment:出道日期"`
	ActiveStatus JAVActorActiveStatus `json:"active_status" gorm:"type:varchar(20);default:'active';comment:活跃状态"`
	CreatedAt    time.Time            `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt    time.Time            `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt    gorm.DeletedAt       `json:"deleted_at" gorm:"index;comment:删除时间"`

	// 关联关系
	Movies []JAVMovie `json:"movies,omitempty" gorm:"many2many:jav_movie_actors;"`
}

// TableName 指定表名
func (JAVActor) TableName() string {
	return "jav_actors"
}

// IsActive 检查演员是否活跃
func (a *JAVActor) IsActive() bool {
	return a.ActiveStatus == JAVActorActiveStatusActive
}

// IsRetired 检查演员是否已退役
func (a *JAVActor) IsRetired() bool {
	return a.ActiveStatus == JAVActorActiveStatusRetired
}

// GetAge 获取年龄
func (a *JAVActor) GetAge() int {
	if a.BirthDate == nil {
		return 0
	}
	now := time.Now()
	age := now.Year() - a.BirthDate.Year()
	if now.YearDay() < a.BirthDate.YearDay() {
		age--
	}
	return age
}

// GetCareerYears 获取从业年数
func (a *JAVActor) GetCareerYears() int {
	if a.DebutDate == nil {
		return 0
	}
	now := time.Now()
	years := now.Year() - a.DebutDate.Year()
	if now.YearDay() < a.DebutDate.YearDay() {
		years--
	}
	if years < 0 {
		return 0
	}
	return years
}

// GetFormattedBirthDate 获取格式化的出生日期
func (a *JAVActor) GetFormattedBirthDate() string {
	if a.BirthDate == nil {
		return "未知"
	}
	return a.BirthDate.Format("2006-01-02")
}

// GetFormattedDebutDate 获取格式化的出道日期
func (a *JAVActor) GetFormattedDebutDate() string {
	if a.DebutDate == nil {
		return "未知"
	}
	return a.DebutDate.Format("2006-01-02")
}

// GetFormattedMeasurements 获取格式化的三围
func (a *JAVActor) GetFormattedMeasurements() string {
	if a.Bust == 0 && a.Waist == 0 && a.Hip == 0 {
		return "未知"
	}
	return fmt.Sprintf("B%d-W%d-H%d", a.Bust, a.Waist, a.Hip)
}

// GetDisplayName 获取显示名称（优先英文，其次日文）
func (a *JAVActor) GetDisplayName() string {
	if a.NameEn != "" {
		return a.NameEn
	}
	return a.Name
}

// BeforeCreate GORM钩子：创建前
func (a *JAVActor) BeforeCreate(tx *gorm.DB) error {
	// 设置默认值
	if a.ActiveStatus == "" {
		a.ActiveStatus = JAVActorActiveStatusActive
	}
	return nil
}

// JAVActorProfile JAV演员资料（用于API响应）
type JAVActorProfile struct {
	ID                    uint                 `json:"id"`
	Name                  string               `json:"name"`
	NameEn                string               `json:"name_en"`
	NameJp                string               `json:"name_jp"`
	AvatarURL             string               `json:"avatar_url"`
	BirthDate             *time.Time           `json:"birth_date"`
	Height                int                  `json:"height"`
	Bust                  int                  `json:"bust"`
	Waist                 int                  `json:"waist"`
	Hip                   int                  `json:"hip"`
	BloodType             string               `json:"blood_type"`
	Hobby                 string               `json:"hobby"`
	DebutDate             *time.Time           `json:"debut_date"`
	ActiveStatus          JAVActorActiveStatus `json:"active_status"`
	CreatedAt             time.Time            `json:"created_at"`
	UpdatedAt             time.Time            `json:"updated_at"`
	Age                   int                  `json:"age"`
	CareerYears           int                  `json:"career_years"`
	FormattedMeasurements string               `json:"formatted_measurements"`
	DisplayName           string               `json:"display_name"`
	MovieCount            int                  `json:"movie_count,omitempty"`
}

// ToProfile 转换为演员资料
func (a *JAVActor) ToProfile() *JAVActorProfile {
	return &JAVActorProfile{
		ID:                    a.ID,
		Name:                  a.Name,
		NameEn:                a.NameEn,
		NameJp:                a.NameJp,
		AvatarURL:             a.AvatarURL,
		BirthDate:             a.BirthDate,
		Height:                a.Height,
		Bust:                  a.Bust,
		Waist:                 a.Waist,
		Hip:                   a.Hip,
		BloodType:             a.BloodType,
		Hobby:                 a.Hobby,
		DebutDate:             a.DebutDate,
		ActiveStatus:          a.ActiveStatus,
		CreatedAt:             a.CreatedAt,
		UpdatedAt:             a.UpdatedAt,
		Age:                   a.GetAge(),
		CareerYears:           a.GetCareerYears(),
		FormattedMeasurements: a.GetFormattedMeasurements(),
		DisplayName:           a.GetDisplayName(),
		MovieCount:            len(a.Movies),
	}
}

// CreateJAVActorRequest 创建JAV演员请求
type CreateJAVActorRequest struct {
	Name         string                `json:"name" binding:"required,min=1,max=200"`
	NameEn       string                `json:"name_en"`
	NameJp       string                `json:"name_jp"`
	AvatarURL    string                `json:"avatar_url"`
	BirthDate    string                `json:"birth_date"`    // 格式: YYYY-MM-DD
	Height       int                   `json:"height"`
	Bust         int                   `json:"bust"`
	Waist        int                   `json:"waist"`
	Hip          int                   `json:"hip"`
	BloodType    string                `json:"blood_type"`
	Hobby        string                `json:"hobby"`
	DebutDate    string                `json:"debut_date"`    // 格式: YYYY-MM-DD
	ActiveStatus JAVActorActiveStatus  `json:"active_status"`
}

// UpdateJAVActorRequest 更新JAV演员请求
type UpdateJAVActorRequest struct {
	Name         string                `json:"name"`
	NameEn       string                `json:"name_en"`
	NameJp       string                `json:"name_jp"`
	AvatarURL    string                `json:"avatar_url"`
	BirthDate    string                `json:"birth_date"`    // 格式: YYYY-MM-DD
	Height       int                   `json:"height"`
	Bust         int                   `json:"bust"`
	Waist        int                   `json:"waist"`
	Hip          int                   `json:"hip"`
	BloodType    string                `json:"blood_type"`
	Hobby        string                `json:"hobby"`
	DebutDate    string                `json:"debut_date"`    // 格式: YYYY-MM-DD
	ActiveStatus JAVActorActiveStatus  `json:"active_status"`
}

// JAVActorSearchRequest JAV演员搜索请求
type JAVActorSearchRequest struct {
	Keyword      string                `json:"keyword" form:"keyword"`
	ActiveStatus JAVActorActiveStatus  `json:"active_status" form:"active_status"`
	MinAge       int                   `json:"min_age" form:"min_age"`
	MaxAge       int                   `json:"max_age" form:"max_age"`
	MinHeight    int                   `json:"min_height" form:"min_height"`
	MaxHeight    int                   `json:"max_height" form:"max_height"`
	BloodType    string                `json:"blood_type" form:"blood_type"`
	Page         int                   `json:"page" form:"page"`
	PageSize     int                   `json:"page_size" form:"page_size"`
	SortBy       string                `json:"sort_by" form:"sort_by"` // name, debut_date, birth_date, created_at
	SortOrder    string                `json:"sort_order" form:"sort_order"` // asc, desc
}

// JAVActorListResponse JAV演员列表响应
type JAVActorListResponse struct {
	Actors     []JAVActorProfile `json:"actors"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
	TotalPages int               `json:"total_pages"`
}