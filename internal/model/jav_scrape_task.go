package model

import (
	"time"

	"gorm.io/gorm"
)

// JAVScrapeTask JAV采集任务模型
type JAVScrapeTask struct {
	ID              uint                `json:"id" gorm:"primaryKey;autoIncrement"`
	MovieCodes      []string            `json:"movie_codes" gorm:"type:jsonb;comment:要采集的影片番号列表"`
	Sources         []string            `json:"sources" gorm:"type:jsonb;comment:数据源列表"`
	Status          JAVScrapingStatus   `json:"status" gorm:"type:varchar(20);default:'pending';comment:任务状态"`
	Priority        int                 `json:"priority" gorm:"type:int;default:2;comment:任务优先级(1-5)"`
	Progress        float64             `json:"progress" gorm:"type:decimal(5,2);default:0;comment:完成进度(0-100)"`
	TotalMovies     int                 `json:"total_movies" gorm:"type:int;comment:总影片数"`
	CompletedMovies int                 `json:"completed_movies" gorm:"type:int;default:0;comment:已完成影片数"`
	FailedMovies    int                 `json:"failed_movies" gorm:"type:int;default:0;comment:失败影片数"`
	AutoMerge       bool                `json:"auto_merge" gorm:"type:boolean;default:true;comment:是否自动融合数据"`
	AutoRetry       bool                `json:"auto_retry" gorm:"type:boolean;default:true;comment:是否自动重试"`
	MaxRetries      int                 `json:"max_retries" gorm:"type:int;default:3;comment:最大重试次数"`
	RetryCount      int                 `json:"retry_count" gorm:"type:int;default:0;comment:当前重试次数"`
	Error           string              `json:"error" gorm:"type:text;comment:错误信息"`
	StartedAt       *time.Time          `json:"started_at" gorm:"comment:开始时间"`
	CompletedAt     *time.Time          `json:"completed_at" gorm:"comment:完成时间"`
	CreatedAt       time.Time           `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt       time.Time           `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt       gorm.DeletedAt      `json:"deleted_at" gorm:"index;comment:删除时间"`
}

// TableName 指定表名
func (JAVScrapeTask) TableName() string {
	return "jav_scrape_tasks"
}

// IsCompleted 检查任务是否完成
func (t *JAVScrapeTask) IsCompleted() bool {
	return t.Status == JAVScrapingStatusCompleted
}

// IsFailed 检查任务是否失败
func (t *JAVScrapeTask) IsFailed() bool {
	return t.Status == JAVScrapingStatusFailed
}

// IsInProgress 检查任务是否进行中
func (t *JAVScrapeTask) IsInProgress() bool {
	return t.Status == JAVScrapingStatusInProgress
}

// CanRetry 检查是否可以重试
func (t *JAVScrapeTask) CanRetry() bool {
	return t.AutoRetry && t.RetryCount < t.MaxRetries && t.IsFailed()
}

// UpdateProgress 更新进度
func (t *JAVScrapeTask) UpdateProgress() {
	if t.TotalMovies > 0 {
		t.Progress = float64(t.CompletedMovies) / float64(t.TotalMovies) * 100
	}
}

// MarkAsStarted 标记为开始
func (t *JAVScrapeTask) MarkAsStarted() {
	t.Status = JAVScrapingStatusInProgress
	now := time.Now()
	t.StartedAt = &now
	t.UpdatedAt = now
}

// MarkAsCompleted 标记为完成
func (t *JAVScrapeTask) MarkAsCompleted() {
	t.Status = JAVScrapingStatusCompleted
	now := time.Now()
	t.CompletedAt = &now
	t.UpdatedAt = now
	t.UpdateProgress()
}

// MarkAsFailed 标记为失败
func (t *JAVScrapeTask) MarkAsFailed(err error) {
	t.Status = JAVScrapingStatusFailed
	if err != nil {
		t.Error = err.Error()
	}
	t.UpdatedAt = time.Now()
	t.UpdateProgress()
}

// IncrementRetry 增加重试次数
func (t *JAVScrapeTask) IncrementRetry() {
	t.RetryCount++
	t.UpdatedAt = time.Now()
}

// AddCompletedMovie 添加完成的影片
func (t *JAVScrapeTask) AddCompletedMovie() {
	t.CompletedMovies++
	t.UpdateProgress()
	t.UpdatedAt = time.Now()
}

// AddFailedMovie 添加失败的影片
func (t *JAVScrapeTask) AddFailedMovie() {
	t.FailedMovies++
	t.UpdateProgress()
	t.UpdatedAt = time.Now()
}