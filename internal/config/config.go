package config

import (
	"fmt"
	"strings"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	App            AppConfig            `mapstructure:"app"`
	Server         ServerConfig         `mapstructure:"server"`
	Database       DatabaseConfig       `mapstructure:"database"`
	Redis          RedisConfig          `mapstructure:"redis"`
	Aria2          Aria2Config          `mapstructure:"aria2"`
	Log            LogConfig            `mapstructure:"log"`
	JWT            JWTConfig            `mapstructure:"jwt"`
	Scheduler      SchedulerConfig      `mapstructure:"scheduler"`
	FileProcessing FileProcessingConfig `mapstructure:"file_processing"`
	Telegram       TelegramConfig       `mapstructure:"telegram"`
	JAV            JAVConfig            `mapstructure:"jav"`
}

// AppConfig 应用基础配置
type AppConfig struct {
	Name    string `mapstructure:"name"`
	Version string `mapstructure:"version"`
	Env     string `mapstructure:"env"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	Mode         string `mapstructure:"mode"`
	ReadTimeout  int    `mapstructure:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	User         string `mapstructure:"user"`
	Password     string `mapstructure:"password"`
	DBName       string `mapstructure:"dbname"`
	SSLMode      string `mapstructure:"sslmode"`
	MaxOpenConns int    `mapstructure:"max_open_conns"`
	MaxIdleConns int    `mapstructure:"max_idle_conns"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
	PoolSize int    `mapstructure:"pool_size"`
}

// Aria2Config aria2配置
type Aria2Config struct {
	Host   string `mapstructure:"host"`
	Port   int    `mapstructure:"port"`
	Secret string `mapstructure:"secret"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
	Output string `mapstructure:"output"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret     string `mapstructure:"secret"`
	ExpireTime int    `mapstructure:"expire_time"`
}

// SchedulerConfig 调度器配置
type SchedulerConfig struct {
	Enabled  bool   `mapstructure:"enabled"`
	Timezone string `mapstructure:"timezone"`
}

// FileProcessingConfig 文件处理配置
type FileProcessingConfig struct {
	Enabled              bool             `mapstructure:"enabled"`                // 是否启用文件处理
	ChunkSizeMB          int              `mapstructure:"chunk_size_mb"`          // 分片大小(MB)
	MaxConcurrentUploads int              `mapstructure:"max_concurrent_uploads"` // 最大并发上传数
	EncryptionAlgorithm  string           `mapstructure:"encryption_algorithm"`   // 加密算法
	EncryptionEnabled    bool             `mapstructure:"encryption_enabled"`     // 是否启用加密
	KeepOriginalFiles    bool             `mapstructure:"keep_original_files"`    // 是否保留原始文件
	WorkDir              string           `mapstructure:"work_dir"`               // 工作目录
	RetryAttempts        int              `mapstructure:"retry_attempts"`         // 重试次数
	AutoStartProcessing  bool             `mapstructure:"auto_start_processing"`  // 下载完成后自动开始处理
	CleanupAfterDays     int              `mapstructure:"cleanup_after_days"`     // 多少天后清理文件
	UploadProvider       string           `mapstructure:"upload_provider"`        // 上传服务提供商 (imgbb, streamtape)
	ImgBB                ImgBBConfig      `mapstructure:"imgbb"`                  // ImgBB配置
	Imgur                ImgurConfig      `mapstructure:"imgur"`                  // Imgur配置
	StreamTape           StreamTapeConfig `mapstructure:"streamtape"`             // StreamTape配置
	StreamHG             StreamHGConfig   `mapstructure:"streamhg"`               // StreamHG配置
	AutoUpload           AutoUploadConfig `mapstructure:"auto_upload"`            // 自动上传配置
	Playlist             PlaylistConfig   `mapstructure:"playlist"`               // 播放列表配置
	MixFile              MixFileConfig    `mapstructure:"mixfile"`                // MixFile配置
}

// ImgBBConfig ImgBB图床配置
type ImgBBConfig struct {
	APIKey     string `mapstructure:"api_key"`     // API密钥
	BaseURL    string `mapstructure:"base_url"`    // API基础URL
	Timeout    int    `mapstructure:"timeout"`     // 请求超时时间(秒)
	MaxRetries int    `mapstructure:"max_retries"` // 最大重试次数
}

// ImgurConfig Imgur图床配置
type ImgurConfig struct {
	ClientID     string `mapstructure:"client_id"`     // 客户端ID
	ClientSecret string `mapstructure:"client_secret"` // 客户端密钥
	RedirectURI  string `mapstructure:"redirect_uri"`  // 回调URL
	BaseURL      string `mapstructure:"base_url"`      // API基础URL
	Timeout      int    `mapstructure:"timeout"`       // 请求超时时间(秒)
	MaxRetries   int    `mapstructure:"max_retries"`   // 最大重试次数
}

// StreamTapeConfig StreamTape配置
type StreamTapeConfig struct {
	APILogin   string `mapstructure:"api_login"`   // API登录用户名
	APIKey     string `mapstructure:"api_key"`     // API密钥/密码
	BaseURL    string `mapstructure:"base_url"`    // API基础URL
	Timeout    int    `mapstructure:"timeout"`     // 请求超时时间(秒)
	MaxRetries int    `mapstructure:"max_retries"` // 最大重试次数
}

// StreamHGConfig StreamHG配置
type StreamHGConfig struct {
	APIKey     string `mapstructure:"api_key"`     // API密钥
	BaseURL    string `mapstructure:"base_url"`    // API基础URL
	Timeout    int    `mapstructure:"timeout"`     // 请求超时时间(秒)
	MaxRetries int    `mapstructure:"max_retries"` // 最大重试次数
}

// VOE配置已移除，改为使用独立的远程上传脚本

// PlaylistConfig 播放列表配置
type PlaylistConfig struct {
	Version        int    `mapstructure:"version"`         // HLS版本
	TargetDuration int    `mapstructure:"target_duration"` // 目标时长(秒)
	MediaSequence  int    `mapstructure:"media_sequence"`  // 媒体序列号
	AllowCache     bool   `mapstructure:"allow_cache"`     // 是否允许缓存
	PlaylistType   string `mapstructure:"playlist_type"`   // 播放列表类型
}

// MixFileConfig MixFile配置
type MixFileConfig struct {
	Enabled                bool   `mapstructure:"enabled"`                  // 是否启用MixFile功能
	EnableSteganography    bool   `mapstructure:"enable_steganography"`     // 是否启用隐写术
	EnableIndexCompression bool   `mapstructure:"enable_index_compression"` // 是否启用索引压缩
	EnableIndexEncryption  bool   `mapstructure:"enable_index_encryption"`  // 是否启用索引加密
	ShareCodePrefix        string `mapstructure:"share_code_prefix"`        // 分享码前缀
	MaxShareCodeLength     int    `mapstructure:"max_share_code_length"`    // 分享码最大长度
	IndexUploadRetries     int    `mapstructure:"index_upload_retries"`     // 索引上传重试次数
}

// AutoUploadConfig 自动上传配置
type AutoUploadConfig struct {
	Enabled              bool  `mapstructure:"enabled"`                // 是否启用自动上传
	ScanInterval         int   `mapstructure:"scan_interval"`          // 扫描间隔(分钟)
	MaxConcurrentUploads int   `mapstructure:"max_concurrent_uploads"` // 最大并发上传数
	MinFileSize          int64 `mapstructure:"min_file_size"`          // 最小文件大小(字节)
	SkipExistingFiles    bool  `mapstructure:"skip_existing_files"`    // 跳过已存在的文件
	DeleteAfterUpload    bool  `mapstructure:"delete_after_upload"`    // 上传后删除原文件
	UploadDelaySeconds   int   `mapstructure:"upload_delay_seconds"`   // 上传间隔延迟(秒)
	MaxRetriesPerFile    int   `mapstructure:"max_retries_per_file"`   // 每个文件最大重试次数
}

// Load 加载配置
func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("/etc/magnet-downloader")

	// 设置默认值
	setDefaults()

	// 环境变量支持
	viper.SetEnvPrefix("MD")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
		// 配置文件不存在时使用默认配置
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	return &config, nil
}

// Reload 重新加载配置
func Reload() (*Config, error) {
	// 重新读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to reload config file: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal reloaded config: %w", err)
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("reloaded config validation failed: %w", err)
	}

	return &config, nil
}

// WatchConfig 监听配置文件变化并自动重载
func WatchConfig(callback func(*Config)) {
	viper.WatchConfig()
	viper.OnConfigChange(func(e fsnotify.Event) {
		fmt.Printf("Config file changed: %s\n", e.Name)

		// 重新加载配置
		config, err := Reload()
		if err != nil {
			fmt.Printf("Failed to reload config: %v\n", err)
			return
		}

		// 调用回调函数
		if callback != nil {
			callback(config)
		}
	})
}

// setDefaults 设置默认配置值
func setDefaults() {
	// App默认配置
	viper.SetDefault("app.name", "magnet-downloader")
	viper.SetDefault("app.version", "1.0.0")
	viper.SetDefault("app.env", "development")

	// Server默认配置
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.mode", "debug")
	viper.SetDefault("server.read_timeout", 60)
	viper.SetDefault("server.write_timeout", 60)

	// Database默认配置
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.user", "postgres")
	viper.SetDefault("database.password", "password")
	viper.SetDefault("database.dbname", "magnet_downloader")
	viper.SetDefault("database.sslmode", "disable")
	viper.SetDefault("database.max_open_conns", 100)
	viper.SetDefault("database.max_idle_conns", 10)

	// Redis默认配置
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 0)
	viper.SetDefault("redis.pool_size", 10)

	// Aria2默认配置
	viper.SetDefault("aria2.host", "localhost")
	viper.SetDefault("aria2.port", 6800)
	viper.SetDefault("aria2.secret", "")

	// Log默认配置
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")
	viper.SetDefault("log.output", "stdout")

	// JWT默认配置
	viper.SetDefault("jwt.secret", "your-secret-key")
	viper.SetDefault("jwt.expire_time", 3600)

	// Scheduler默认配置
	viper.SetDefault("scheduler.enabled", true)
	viper.SetDefault("scheduler.timezone", "UTC")

	// FileProcessing默认配置
	viper.SetDefault("file_processing.enabled", true)
	viper.SetDefault("file_processing.chunk_size_mb", 1)
	viper.SetDefault("file_processing.max_concurrent_uploads", 3)
	viper.SetDefault("file_processing.encryption_algorithm", "aes-gcm-256")
	viper.SetDefault("file_processing.encryption_enabled", true)
	viper.SetDefault("file_processing.keep_original_files", false)
	viper.SetDefault("file_processing.work_dir", "/tmp/fileprocessor")
	viper.SetDefault("file_processing.retry_attempts", 3)
	viper.SetDefault("file_processing.auto_start_processing", true)
	viper.SetDefault("file_processing.cleanup_after_days", 30)
	viper.SetDefault("file_processing.upload_provider", "imgbb")

	// 本地图床默认配置 (Telegraph-Image Express)
	viper.SetDefault("file_processing.imgbb.api_key", "")
	viper.SetDefault("file_processing.imgbb.base_url", "http://localhost:3000")
	viper.SetDefault("file_processing.imgbb.timeout", 30)
	viper.SetDefault("file_processing.imgbb.max_retries", 3)

	// Imgur默认配置
	viper.SetDefault("file_processing.imgur.client_id", "")
	viper.SetDefault("file_processing.imgur.client_secret", "")
	viper.SetDefault("file_processing.imgur.redirect_uri", "http://localhost:8080/api/imgur/callback")
	viper.SetDefault("file_processing.imgur.base_url", "https://api.imgur.com/3")
	viper.SetDefault("file_processing.imgur.timeout", 30)
	viper.SetDefault("file_processing.imgur.max_retries", 3)

	// DoodStream默认配置
	viper.SetDefault("file_processing.doodstream.api_key", "520970w0nqtrdi6r1w6a3u")
	viper.SetDefault("file_processing.doodstream.base_url", "https://doodapi.co")
	viper.SetDefault("file_processing.doodstream.timeout", 300)
	viper.SetDefault("file_processing.doodstream.max_retries", 3)

	// VOE配置已移除，改为使用独立的远程上传脚本

	// Playlist默认配置
	viper.SetDefault("file_processing.playlist.version", 3)
	viper.SetDefault("file_processing.playlist.target_duration", 10)
	viper.SetDefault("file_processing.playlist.media_sequence", 0)
	viper.SetDefault("file_processing.playlist.allow_cache", true)
	viper.SetDefault("file_processing.playlist.playlist_type", "VOD")

	// MixFile默认配置
	viper.SetDefault("file_processing.mixfile.enabled", false)
	viper.SetDefault("file_processing.mixfile.enable_steganography", true)
	viper.SetDefault("file_processing.mixfile.enable_index_compression", true)
	viper.SetDefault("file_processing.mixfile.enable_index_encryption", true)
	viper.SetDefault("file_processing.mixfile.share_code_prefix", "mf://")
	viper.SetDefault("file_processing.mixfile.max_share_code_length", 2048)
	viper.SetDefault("file_processing.mixfile.index_upload_retries", 3)

	// 自动上传默认配置
	viper.SetDefault("file_processing.auto_upload.enabled", true)
	viper.SetDefault("file_processing.auto_upload.scan_interval", 5)          // 5分钟扫描一次，更频繁
	viper.SetDefault("file_processing.auto_upload.max_concurrent_uploads", 3) // 最大3个并发上传
	viper.SetDefault("file_processing.auto_upload.min_file_size", 10485760)   // 10MB最小文件大小
	viper.SetDefault("file_processing.auto_upload.skip_existing_files", true) // 跳过已存在文件
	viper.SetDefault("file_processing.auto_upload.delete_after_upload", true) // 上传后删除原文件夹

	// Telegram通知默认配置
	setTelegramDefaults()

	// JAV功能默认配置
	setJAVDefaults()
}

// setJAVDefaults 设置JAV功能默认配置
func setTelegramDefaults() {
	// Telegram主配置
	viper.SetDefault("telegram.enabled", false)
	viper.SetDefault("telegram.bot_token", "")
	viper.SetDefault("telegram.chat_id", "")
	viper.SetDefault("telegram.timeout", "30s")
	viper.SetDefault("telegram.max_retries", 3)

	// 通知配置
	viper.SetDefault("telegram.notifications.scraping_progress", true)
	viper.SetDefault("telegram.notifications.scraping_completed", true)
	viper.SetDefault("telegram.notifications.scraping_failed", true)
	viper.SetDefault("telegram.notifications.upload_progress", true)
	viper.SetDefault("telegram.notifications.upload_completed", true)
	viper.SetDefault("telegram.notifications.upload_failed", true)
	viper.SetDefault("telegram.notifications.system_error", true)
}

func setJAVDefaults() {
	// JAV主配置
	viper.SetDefault("jav.enabled", false)

	// 数据采集配置
	viper.SetDefault("jav.scraping.enabled", true)
	viper.SetDefault("jav.scraping.timeout", 30)
	viper.SetDefault("jav.scraping.rate_limit", 2)
	viper.SetDefault("jav.scraping.max_retries", 3)
	viper.SetDefault("jav.scraping.auto_merge", true)
	viper.SetDefault("jav.scraping.min_confidence", 0.7)
	viper.SetDefault("jav.scraping.batch_size", 10)
	viper.SetDefault("jav.scraping.concurrent_limit", 5)
	viper.SetDefault("jav.scraping.user_agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	viper.SetDefault("jav.scraping.proxy_enabled", false)
	viper.SetDefault("jav.scraping.proxy_url", "")

	// 数据源默认配置
	// JavBus
	viper.SetDefault("jav.scraping.sources.javbus.enabled", true)
	viper.SetDefault("jav.scraping.sources.javbus.base_url", "https://www.javbus.com")
	viper.SetDefault("jav.scraping.sources.javbus.timeout", 30)
	viper.SetDefault("jav.scraping.sources.javbus.max_retries", 3)
	viper.SetDefault("jav.scraping.sources.javbus.priority", 9)
	viper.SetDefault("jav.scraping.sources.javbus.weight", 0.4)
	viper.SetDefault("jav.scraping.sources.javbus.rate_limit", 2)

	// Javinizer
	viper.SetDefault("jav.scraping.sources.javinizer.enabled", true)
	viper.SetDefault("jav.scraping.sources.javinizer.base_url", "https://www.r18.com")
	viper.SetDefault("jav.scraping.sources.javinizer.timeout", 30)
	viper.SetDefault("jav.scraping.sources.javinizer.max_retries", 3)
	viper.SetDefault("jav.scraping.sources.javinizer.priority", 8)
	viper.SetDefault("jav.scraping.sources.javinizer.weight", 0.3)
	viper.SetDefault("jav.scraping.sources.javinizer.rate_limit", 3)

	// JavSP
	viper.SetDefault("jav.scraping.sources.javsp.enabled", true)
	viper.SetDefault("jav.scraping.sources.javsp.base_url", "https://javdb.com")
	viper.SetDefault("jav.scraping.sources.javsp.timeout", 30)
	viper.SetDefault("jav.scraping.sources.javsp.max_retries", 3)
	viper.SetDefault("jav.scraping.sources.javsp.priority", 7)
	viper.SetDefault("jav.scraping.sources.javsp.weight", 0.2)
	viper.SetDefault("jav.scraping.sources.javsp.rate_limit", 3)

	// JavDB
	viper.SetDefault("jav.scraping.sources.javdb.enabled", false)
	viper.SetDefault("jav.scraping.sources.javdb.base_url", "https://javdb.com")
	viper.SetDefault("jav.scraping.sources.javdb.timeout", 30)
	viper.SetDefault("jav.scraping.sources.javdb.max_retries", 3)
	viper.SetDefault("jav.scraping.sources.javdb.priority", 6)
	viper.SetDefault("jav.scraping.sources.javdb.weight", 0.1)
	viper.SetDefault("jav.scraping.sources.javdb.rate_limit", 5)

	// JavLibrary
	viper.SetDefault("jav.scraping.sources.javlibrary.enabled", false)
	viper.SetDefault("jav.scraping.sources.javlibrary.base_url", "https://www.javlibrary.com")
	viper.SetDefault("jav.scraping.sources.javlibrary.timeout", 30)
	viper.SetDefault("jav.scraping.sources.javlibrary.max_retries", 3)
	viper.SetDefault("jav.scraping.sources.javlibrary.priority", 5)
	viper.SetDefault("jav.scraping.sources.javlibrary.weight", 0.1)
	viper.SetDefault("jav.scraping.sources.javlibrary.rate_limit", 5)

	// 下载配置
	viper.SetDefault("jav.download.enabled", true)
	viper.SetDefault("jav.download.default_path", "/downloads/jav")
	viper.SetDefault("jav.download.create_folder", true)
	viper.SetDefault("jav.download.folder_template", "{studio}/{code} - {title}")
	viper.SetDefault("jav.download.file_template", "{code} - {title}.{ext}")
	viper.SetDefault("jav.download.prefer_quality", "1080p")
	viper.SetDefault("jav.download.prefer_subtitle", true)
	viper.SetDefault("jav.download.subtitle_lang", "chinese")
	viper.SetDefault("jav.download.auto_start", false)
	viper.SetDefault("jav.download.max_concurrent", 3)
	viper.SetDefault("jav.download.speed_limit", 0)
	viper.SetDefault("jav.download.auto_upload", false)
	viper.SetDefault("jav.download.upload_platform", "streamtape")
	viper.SetDefault("jav.download.clean_after_upload", false)

	// 存储配置
	viper.SetDefault("jav.storage.cover_path", "/storage/jav/covers")
	viper.SetDefault("jav.storage.poster_path", "/storage/jav/posters")
	viper.SetDefault("jav.storage.actor_avatar_path", "/storage/jav/actors")
	viper.SetDefault("jav.storage.max_cover_size", 5242880)   // 5MB
	viper.SetDefault("jav.storage.max_poster_size", 10485760) // 10MB
	viper.SetDefault("jav.storage.image_quality", 85)
	viper.SetDefault("jav.storage.image_format", "jpg")
	viper.SetDefault("jav.storage.enable_webp", true)
	viper.SetDefault("jav.storage.enable_thumbnail", true)
	viper.SetDefault("jav.storage.thumbnail_size", 300)

	// 处理配置
	viper.SetDefault("jav.processing.enable_metadata", true)
	viper.SetDefault("jav.processing.enable_thumbnail", true)
	viper.SetDefault("jav.processing.enable_preview", false)
	viper.SetDefault("jav.processing.thumbnail_count", 10)
	viper.SetDefault("jav.processing.thumbnail_width", 320)
	viper.SetDefault("jav.processing.thumbnail_height", 180)
	viper.SetDefault("jav.processing.preview_duration", 30)
	viper.SetDefault("jav.processing.preview_quality", "medium")
	viper.SetDefault("jav.processing.ffmpeg_path", "/usr/bin/ffmpeg")
	viper.SetDefault("jav.processing.ffprobe_path", "/usr/bin/ffprobe")
	viper.SetDefault("jav.processing.temp_path", "/tmp/jav_processing")
	viper.SetDefault("jav.processing.clean_temp_files", true)

	// API配置
	viper.SetDefault("jav.api.enable_public_api", true)
	viper.SetDefault("jav.api.enable_search", true)
	viper.SetDefault("jav.api.enable_download", true)
	viper.SetDefault("jav.api.enable_scraping", false)
	viper.SetDefault("jav.api.max_search_results", 100)
	viper.SetDefault("jav.api.max_page_size", 50)
	viper.SetDefault("jav.api.cache_enabled", true)
	viper.SetDefault("jav.api.cache_ttl", 3600)
	viper.SetDefault("jav.api.rate_limit_enabled", true)
	viper.SetDefault("jav.api.rate_limit_rpm", 1000)
	viper.SetDefault("jav.api.require_auth", false)
	viper.SetDefault("jav.api.admin_only", false)
}

// Validate 验证配置
func (c *Config) Validate() error {
	// 验证应用配置
	if c.App.Name == "" {
		return fmt.Errorf("app.name is required")
	}

	// 验证服务器配置
	if c.Server.Port <= 0 || c.Server.Port > 65535 {
		return fmt.Errorf("server.port must be between 1 and 65535")
	}

	// 验证数据库配置
	if c.Database.Host == "" {
		return fmt.Errorf("database.host is required")
	}
	if c.Database.Port <= 0 || c.Database.Port > 65535 {
		return fmt.Errorf("database.port must be between 1 and 65535")
	}
	if c.Database.DBName == "" {
		return fmt.Errorf("database.dbname is required")
	}

	// 验证Redis配置
	if c.Redis.Host == "" {
		return fmt.Errorf("redis.host is required")
	}
	if c.Redis.Port <= 0 || c.Redis.Port > 65535 {
		return fmt.Errorf("redis.port must be between 1 and 65535")
	}

	// 验证Aria2配置
	if c.Aria2.Host == "" {
		return fmt.Errorf("aria2.host is required")
	}
	if c.Aria2.Port <= 0 || c.Aria2.Port > 65535 {
		return fmt.Errorf("aria2.port must be between 1 and 65535")
	}

	// 验证JWT配置
	if c.JWT.Secret == "" || c.JWT.Secret == "your-secret-key" || c.JWT.Secret == "your-secret-key-here" {
		return fmt.Errorf("jwt.secret must be set to a secure value")
	}
	if c.JWT.ExpireTime <= 0 {
		return fmt.Errorf("jwt.expire_time must be positive")
	}

	// 验证文件处理配置
	if err := c.validateFileProcessing(); err != nil {
		return fmt.Errorf("file_processing validation failed: %w", err)
	}

	// 验证JAV配置
	if err := c.JAV.Validate(); err != nil {
		return fmt.Errorf("jav validation failed: %w", err)
	}

	return nil
}

// validateFileProcessing 验证文件处理配置
func (c *Config) validateFileProcessing() error {
	fp := &c.FileProcessing

	// 验证分片大小
	if fp.ChunkSizeMB <= 0 || fp.ChunkSizeMB > 100 {
		return fmt.Errorf("chunk_size_mb must be between 1 and 100")
	}

	// 验证并发数
	if fp.MaxConcurrentUploads <= 0 || fp.MaxConcurrentUploads > 10 {
		return fmt.Errorf("max_concurrent_uploads must be between 1 and 10")
	}

	// 验证加密算法
	validAlgorithms := []string{"aes-gcm-256", "aes-gcm-128"}
	algorithmValid := false
	for _, alg := range validAlgorithms {
		if fp.EncryptionAlgorithm == alg {
			algorithmValid = true
			break
		}
	}
	if !algorithmValid {
		return fmt.Errorf("encryption_algorithm must be one of: %v", validAlgorithms)
	}

	// 验证工作目录
	if fp.WorkDir == "" {
		return fmt.Errorf("work_dir is required")
	}

	// 验证重试次数
	if fp.RetryAttempts < 0 || fp.RetryAttempts > 10 {
		return fmt.Errorf("retry_attempts must be between 0 and 10")
	}

	// 验证清理天数
	if fp.CleanupAfterDays < 1 || fp.CleanupAfterDays > 365 {
		return fmt.Errorf("cleanup_after_days must be between 1 and 365")
	}

	// 验证图床配置
	// 本地图床(localhost:3000)不需要API密钥，其他图床需要
	if fp.Enabled && fp.ImgBB.APIKey == "" && !strings.Contains(fp.ImgBB.BaseURL, "localhost:3000") {
		return fmt.Errorf("imgbb.api_key is required when file processing is enabled (except for local image host)")
	}

	if fp.ImgBB.Timeout <= 0 || fp.ImgBB.Timeout > 300 {
		return fmt.Errorf("imgbb.timeout must be between 1 and 300 seconds")
	}

	if fp.ImgBB.MaxRetries < 0 || fp.ImgBB.MaxRetries > 10 {
		return fmt.Errorf("imgbb.max_retries must be between 0 and 10")
	}

	// 验证播放列表配置
	if fp.Playlist.Version < 1 || fp.Playlist.Version > 7 {
		return fmt.Errorf("playlist.version must be between 1 and 7")
	}

	if fp.Playlist.TargetDuration <= 0 || fp.Playlist.TargetDuration > 3600 {
		return fmt.Errorf("playlist.target_duration must be between 1 and 3600 seconds")
	}

	validPlaylistTypes := []string{"VOD", "EVENT"}
	playlistTypeValid := false
	for _, pt := range validPlaylistTypes {
		if fp.Playlist.PlaylistType == pt {
			playlistTypeValid = true
			break
		}
	}
	if !playlistTypeValid {
		return fmt.Errorf("playlist.playlist_type must be one of: %v", validPlaylistTypes)
	}

	// 验证上传提供商
	validProviders := []string{"imgbb", "streamtape", "streamhg", "dual", "triple"}
	providerValid := false
	for _, provider := range validProviders {
		if fp.UploadProvider == provider {
			providerValid = true
			break
		}
	}
	if !providerValid {
		return fmt.Errorf("upload_provider must be one of: %v", validProviders)
	}

	// 验证StreamTape配置
	if fp.UploadProvider == "streamtape" {
		if fp.StreamTape.APILogin == "" {
			return fmt.Errorf("streamtape.api_login is required when upload_provider is streamtape")
		}

		if fp.StreamTape.APIKey == "" {
			return fmt.Errorf("streamtape.api_key is required when upload_provider is streamtape")
		}

		if fp.StreamTape.BaseURL == "" {
			return fmt.Errorf("streamtape.base_url is required")
		}

		if fp.StreamTape.Timeout <= 0 || fp.StreamTape.Timeout > 600 {
			return fmt.Errorf("streamtape.timeout must be between 1 and 600 seconds")
		}

		if fp.StreamTape.MaxRetries < 0 || fp.StreamTape.MaxRetries > 10 {
			return fmt.Errorf("streamtape.max_retries must be between 0 and 10")
		}
	}

	// VOE配置验证已移除，改为使用独立的远程上传脚本

	return nil
}

// TelegramConfig Telegram配置
type TelegramConfig struct {
	Enabled       bool                       `mapstructure:"enabled"`       // 是否启用Telegram通知
	BotToken      string                     `mapstructure:"bot_token"`     // Bot API Token
	ChatID        string                     `mapstructure:"chat_id"`       // 目标聊天ID
	Timeout       string                     `mapstructure:"timeout"`       // 请求超时时间
	MaxRetries    int                        `mapstructure:"max_retries"`   // 最大重试次数
	Notifications TelegramNotificationConfig `mapstructure:"notifications"` // 通知配置
}

// TelegramNotificationConfig Telegram通知配置
type TelegramNotificationConfig struct {
	ScrapingProgress  bool `mapstructure:"scraping_progress"`  // 采集进度通知
	ScrapingCompleted bool `mapstructure:"scraping_completed"` // 采集完成通知
	ScrapingFailed    bool `mapstructure:"scraping_failed"`    // 采集失败通知
	UploadProgress    bool `mapstructure:"upload_progress"`    // 上传进度通知
	UploadCompleted   bool `mapstructure:"upload_completed"`   // 上传完成通知
	UploadFailed      bool `mapstructure:"upload_failed"`      // 上传失败通知
	SystemError       bool `mapstructure:"system_error"`       // 系统错误通知
}

// JAVConfig JAV功能配置
type JAVConfig struct {
	Enabled          bool                      `mapstructure:"enabled"`           // 是否启用JAV功能
	ExternalServices JAVExternalServicesConfig `mapstructure:"external_services"` // 外部服务配置
	Scraping         JAVScrapingConfig         `mapstructure:"scraping"`          // 数据采集配置
	Download         JAVDownloadConfig         `mapstructure:"download"`          // 下载配置
	Storage          JAVStorageConfig          `mapstructure:"storage"`           // 存储配置
	Processing       JAVProcessConfig          `mapstructure:"processing"`        // 处理配置
	API              JAVAPIConfig              `mapstructure:"api"`               // API配置
}

// JAVExternalServicesConfig JAV外部服务配置
type JAVExternalServicesConfig struct {
	JavBusAPI        JAVExternalServiceConfig `mapstructure:"javbus_api"`        // JavBus API服务
	JavSPWrapper     JAVExternalServiceConfig `mapstructure:"javsp_wrapper"`     // JavSP包装器服务
	JavinizerWrapper JAVExternalServiceConfig `mapstructure:"javinizer_wrapper"` // Javinizer包装器服务
}

// JAVExternalServiceConfig 单个外部服务配置
type JAVExternalServiceConfig struct {
	Enabled    bool   `mapstructure:"enabled"`     // 是否启用
	BaseURL    string `mapstructure:"base_url"`    // 服务基础URL
	Timeout    string `mapstructure:"timeout"`     // 超时时间
	MaxRetries int    `mapstructure:"max_retries"` // 最大重试次数
	RateLimit  string `mapstructure:"rate_limit"`  // 速率限制
}

// JAVScrapingConfig JAV数据采集配置
type JAVScrapingConfig struct {
	Enabled         bool                    `mapstructure:"enabled"`          // 是否启用数据采集
	Sources         JAVScrapingSourceConfig `mapstructure:"sources"`          // 数据源配置
	Timeout         int                     `mapstructure:"timeout"`          // 请求超时时间(秒)
	RateLimit       int                     `mapstructure:"rate_limit"`       // 速率限制(秒)
	MaxRetries      int                     `mapstructure:"max_retries"`      // 最大重试次数
	AutoMerge       bool                    `mapstructure:"auto_merge"`       // 是否自动融合数据
	MinConfidence   float64                 `mapstructure:"min_confidence"`   // 最小可信度
	BatchSize       int                     `mapstructure:"batch_size"`       // 批量处理大小
	ConcurrentLimit int                     `mapstructure:"concurrent_limit"` // 并发限制
	UserAgent       string                  `mapstructure:"user_agent"`       // 用户代理
	ProxyEnabled    bool                    `mapstructure:"proxy_enabled"`    // 是否启用代理
	ProxyURL        string                  `mapstructure:"proxy_url"`        // 代理URL
}

// JAVScrapingSourceConfig JAV数据源配置
type JAVScrapingSourceConfig struct {
	JavBus     JAVSourceConfig `mapstructure:"javbus"`     // JavBus配置
	Javinizer  JAVSourceConfig `mapstructure:"javinizer"`  // Javinizer配置
	JavSP      JAVSourceConfig `mapstructure:"javsp"`      // JavSP配置
	JavDB      JAVSourceConfig `mapstructure:"javdb"`      // JavDB配置
	JavLibrary JAVSourceConfig `mapstructure:"javlibrary"` // JavLibrary配置
}

// JAVSourceConfig 单个JAV数据源配置
type JAVSourceConfig struct {
	Enabled    bool              `mapstructure:"enabled"`     // 是否启用
	BaseURL    string            `mapstructure:"base_url"`    // 基础URL
	Timeout    int               `mapstructure:"timeout"`     // 超时时间(秒)
	MaxRetries int               `mapstructure:"max_retries"` // 最大重试次数
	Priority   int               `mapstructure:"priority"`    // 优先级(1-10)
	Weight     float64           `mapstructure:"weight"`      // 权重(0.0-1.0)
	APIKey     string            `mapstructure:"api_key"`     // API密钥(如果需要)
	RateLimit  int               `mapstructure:"rate_limit"`  // 速率限制(秒)
	UserAgent  string            `mapstructure:"user_agent"`  // 用户代理
	Headers    map[string]string `mapstructure:"headers"`     // 自定义请求头
}

// JAVDownloadConfig JAV下载配置
type JAVDownloadConfig struct {
	Enabled          bool   `mapstructure:"enabled"`            // 是否启用JAV下载
	DefaultPath      string `mapstructure:"default_path"`       // 默认下载路径
	CreateFolder     bool   `mapstructure:"create_folder"`      // 是否为每部影片创建文件夹
	FolderTemplate   string `mapstructure:"folder_template"`    // 文件夹命名模板
	FileTemplate     string `mapstructure:"file_template"`      // 文件命名模板
	PreferQuality    string `mapstructure:"prefer_quality"`     // 首选清晰度
	PreferSubtitle   bool   `mapstructure:"prefer_subtitle"`    // 是否优先选择有字幕的
	SubtitleLang     string `mapstructure:"subtitle_lang"`      // 首选字幕语言
	AutoStart        bool   `mapstructure:"auto_start"`         // 是否自动开始下载
	MaxConcurrent    int    `mapstructure:"max_concurrent"`     // 最大并发下载数
	SpeedLimit       int64  `mapstructure:"speed_limit"`        // 速度限制(KB/s, 0为不限制)
	AutoUpload       bool   `mapstructure:"auto_upload"`        // 是否自动上传
	UploadPlatform   string `mapstructure:"upload_platform"`    // 上传平台
	CleanAfterUpload bool   `mapstructure:"clean_after_upload"` // 上传后是否清理本地文件
}

// JAVStorageConfig JAV存储配置
type JAVStorageConfig struct {
	CoverPath       string `mapstructure:"cover_path"`        // 封面存储路径
	PosterPath      string `mapstructure:"poster_path"`       // 海报存储路径
	ActorAvatarPath string `mapstructure:"actor_avatar_path"` // 演员头像存储路径
	MaxCoverSize    int64  `mapstructure:"max_cover_size"`    // 最大封面文件大小(字节)
	MaxPosterSize   int64  `mapstructure:"max_poster_size"`   // 最大海报文件大小(字节)
	ImageQuality    int    `mapstructure:"image_quality"`     // 图片质量(1-100)
	ImageFormat     string `mapstructure:"image_format"`      // 图片格式(jpg, png, webp)
	EnableWebP      bool   `mapstructure:"enable_webp"`       // 是否启用WebP格式
	EnableThumbnail bool   `mapstructure:"enable_thumbnail"`  // 是否生成缩略图
	ThumbnailSize   int    `mapstructure:"thumbnail_size"`    // 缩略图大小(像素)
}

// JAVProcessConfig JAV处理配置
type JAVProcessConfig struct {
	EnableMetadata  bool   `mapstructure:"enable_metadata"`  // 是否启用元数据处理
	EnableThumbnail bool   `mapstructure:"enable_thumbnail"` // 是否生成缩略图
	EnablePreview   bool   `mapstructure:"enable_preview"`   // 是否生成预览
	ThumbnailCount  int    `mapstructure:"thumbnail_count"`  // 缩略图数量
	ThumbnailWidth  int    `mapstructure:"thumbnail_width"`  // 缩略图宽度
	ThumbnailHeight int    `mapstructure:"thumbnail_height"` // 缩略图高度
	PreviewDuration int    `mapstructure:"preview_duration"` // 预览时长(秒)
	PreviewQuality  string `mapstructure:"preview_quality"`  // 预览质量
	FFmpegPath      string `mapstructure:"ffmpeg_path"`      // FFmpeg路径
	FFprobePath     string `mapstructure:"ffprobe_path"`     // FFprobe路径
	TempPath        string `mapstructure:"temp_path"`        // 临时文件路径
	CleanTempFiles  bool   `mapstructure:"clean_temp_files"` // 是否清理临时文件
}

// JAVAPIConfig JAV API配置
type JAVAPIConfig struct {
	EnablePublicAPI  bool     `mapstructure:"enable_public_api"`  // 是否启用公开API
	EnableSearch     bool     `mapstructure:"enable_search"`      // 是否启用搜索功能
	EnableDownload   bool     `mapstructure:"enable_download"`    // 是否启用下载功能
	EnableScraping   bool     `mapstructure:"enable_scraping"`    // 是否启用采集功能
	MaxSearchResults int      `mapstructure:"max_search_results"` // 最大搜索结果数
	MaxPageSize      int      `mapstructure:"max_page_size"`      // 最大分页大小
	CacheEnabled     bool     `mapstructure:"cache_enabled"`      // 是否启用缓存
	CacheTTL         int      `mapstructure:"cache_ttl"`          // 缓存TTL(秒)
	RateLimitEnabled bool     `mapstructure:"rate_limit_enabled"` // 是否启用速率限制
	RateLimitRPM     int      `mapstructure:"rate_limit_rpm"`     // 每分钟请求限制
	AllowedOrigins   []string `mapstructure:"allowed_origins"`    // 允许的跨域来源
	RequireAuth      bool     `mapstructure:"require_auth"`       // 是否需要认证
	AdminOnly        bool     `mapstructure:"admin_only"`         // 是否仅管理员可用
}

// Validate 验证JAV配置
func (j *JAVConfig) Validate() error {
	if !j.Enabled {
		return nil // 如果未启用，跳过验证
	}

	// 验证数据采集配置
	if err := j.Scraping.Validate(); err != nil {
		return fmt.Errorf("scraping config: %w", err)
	}

	// 验证下载配置
	if err := j.Download.Validate(); err != nil {
		return fmt.Errorf("download config: %w", err)
	}

	// 验证存储配置
	if err := j.Storage.Validate(); err != nil {
		return fmt.Errorf("storage config: %w", err)
	}

	// 验证处理配置
	if err := j.Processing.Validate(); err != nil {
		return fmt.Errorf("processing config: %w", err)
	}

	// 验证API配置
	if err := j.API.Validate(); err != nil {
		return fmt.Errorf("api config: %w", err)
	}

	return nil
}

// Validate 验证数据采集配置
func (s *JAVScrapingConfig) Validate() error {
	if !s.Enabled {
		return nil
	}

	if s.Timeout < 5 || s.Timeout > 300 {
		return fmt.Errorf("timeout must be between 5 and 300 seconds")
	}

	if s.RateLimit < 0 || s.RateLimit > 60 {
		return fmt.Errorf("rate_limit must be between 0 and 60 seconds")
	}

	if s.MaxRetries < 0 || s.MaxRetries > 10 {
		return fmt.Errorf("max_retries must be between 0 and 10")
	}

	if s.MinConfidence < 0.0 || s.MinConfidence > 1.0 {
		return fmt.Errorf("min_confidence must be between 0.0 and 1.0")
	}

	if s.BatchSize < 1 || s.BatchSize > 100 {
		return fmt.Errorf("batch_size must be between 1 and 100")
	}

	if s.ConcurrentLimit < 1 || s.ConcurrentLimit > 20 {
		return fmt.Errorf("concurrent_limit must be between 1 and 20")
	}

	// 验证数据源配置
	return s.Sources.Validate()
}

// Validate 验证数据源配置
func (s *JAVScrapingSourceConfig) Validate() error {
	sources := []JAVSourceConfig{s.JavBus, s.Javinizer, s.JavSP, s.JavDB, s.JavLibrary}

	for i, source := range sources {
		if err := source.Validate(); err != nil {
			sourceNames := []string{"javbus", "javinizer", "javsp", "javdb", "javlibrary"}
			return fmt.Errorf("%s: %w", sourceNames[i], err)
		}
	}

	return nil
}

// Validate 验证单个数据源配置
func (s *JAVSourceConfig) Validate() error {
	if !s.Enabled {
		return nil
	}

	if s.BaseURL == "" {
		return fmt.Errorf("base_url is required when enabled")
	}

	if s.Timeout < 5 || s.Timeout > 300 {
		return fmt.Errorf("timeout must be between 5 and 300 seconds")
	}

	if s.MaxRetries < 0 || s.MaxRetries > 10 {
		return fmt.Errorf("max_retries must be between 0 and 10")
	}

	if s.Priority < 1 || s.Priority > 10 {
		return fmt.Errorf("priority must be between 1 and 10")
	}

	if s.Weight < 0.0 || s.Weight > 1.0 {
		return fmt.Errorf("weight must be between 0.0 and 1.0")
	}

	if s.RateLimit < 0 || s.RateLimit > 60 {
		return fmt.Errorf("rate_limit must be between 0 and 60 seconds")
	}

	return nil
}

// Validate 验证下载配置
func (d *JAVDownloadConfig) Validate() error {
	if !d.Enabled {
		return nil
	}

	if d.DefaultPath == "" {
		return fmt.Errorf("default_path is required when enabled")
	}

	if d.MaxConcurrent < 1 || d.MaxConcurrent > 20 {
		return fmt.Errorf("max_concurrent must be between 1 and 20")
	}

	if d.SpeedLimit < 0 {
		return fmt.Errorf("speed_limit must be non-negative")
	}

	validQualities := []string{"720p", "1080p", "4K", "auto"}
	if d.PreferQuality != "" {
		found := false
		for _, q := range validQualities {
			if d.PreferQuality == q {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("prefer_quality must be one of: %v", validQualities)
		}
	}

	validSubtitleLangs := []string{"chinese", "english", "japanese", "auto", "none"}
	if d.SubtitleLang != "" {
		found := false
		for _, lang := range validSubtitleLangs {
			if d.SubtitleLang == lang {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("subtitle_lang must be one of: %v", validSubtitleLangs)
		}
	}

	return nil
}

// Validate 验证存储配置
func (s *JAVStorageConfig) Validate() error {
	if s.MaxCoverSize < 0 {
		return fmt.Errorf("max_cover_size must be non-negative")
	}

	if s.MaxPosterSize < 0 {
		return fmt.Errorf("max_poster_size must be non-negative")
	}

	if s.ImageQuality < 1 || s.ImageQuality > 100 {
		return fmt.Errorf("image_quality must be between 1 and 100")
	}

	validFormats := []string{"jpg", "jpeg", "png", "webp"}
	if s.ImageFormat != "" {
		found := false
		for _, format := range validFormats {
			if s.ImageFormat == format {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("image_format must be one of: %v", validFormats)
		}
	}

	if s.ThumbnailSize < 50 || s.ThumbnailSize > 1000 {
		return fmt.Errorf("thumbnail_size must be between 50 and 1000 pixels")
	}

	return nil
}

// Validate 验证处理配置
func (p *JAVProcessConfig) Validate() error {
	if p.ThumbnailCount < 0 || p.ThumbnailCount > 50 {
		return fmt.Errorf("thumbnail_count must be between 0 and 50")
	}

	if p.ThumbnailWidth < 100 || p.ThumbnailWidth > 1920 {
		return fmt.Errorf("thumbnail_width must be between 100 and 1920 pixels")
	}

	if p.ThumbnailHeight < 100 || p.ThumbnailHeight > 1080 {
		return fmt.Errorf("thumbnail_height must be between 100 and 1080 pixels")
	}

	if p.PreviewDuration < 10 || p.PreviewDuration > 300 {
		return fmt.Errorf("preview_duration must be between 10 and 300 seconds")
	}

	validQualities := []string{"low", "medium", "high"}
	if p.PreviewQuality != "" {
		found := false
		for _, q := range validQualities {
			if p.PreviewQuality == q {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("preview_quality must be one of: %v", validQualities)
		}
	}

	return nil
}

// Validate 验证API配置
func (a *JAVAPIConfig) Validate() error {
	if a.MaxSearchResults < 10 || a.MaxSearchResults > 1000 {
		return fmt.Errorf("max_search_results must be between 10 and 1000")
	}

	if a.MaxPageSize < 10 || a.MaxPageSize > 200 {
		return fmt.Errorf("max_page_size must be between 10 and 200")
	}

	if a.CacheTTL < 60 || a.CacheTTL > 86400 {
		return fmt.Errorf("cache_ttl must be between 60 and 86400 seconds")
	}

	if a.RateLimitRPM < 10 || a.RateLimitRPM > 10000 {
		return fmt.Errorf("rate_limit_rpm must be between 10 and 10000")
	}

	return nil
}
