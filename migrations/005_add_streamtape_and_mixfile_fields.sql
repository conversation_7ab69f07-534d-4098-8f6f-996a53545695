-- 添加StreamTape和MixFile相关字段
-- 支持双重上传和MixFile功能

-- 添加MixFile相关字段
ALTER TABLE download_tasks ADD COLUMN IF NOT EXISTS index_url TEXT;
ALTER TABLE download_tasks ADD COLUMN IF NOT EXISTS share_code TEXT;
ALTER TABLE download_tasks ADD COLUMN IF NOT EXISTS mixfile_mode BOOLEAN DEFAULT FALSE;

-- 添加StreamTape播放链接字段
ALTER TABLE download_tasks ADD COLUMN IF NOT EXISTS doodstream_url TEXT;
ALTER TABLE download_tasks ADD COLUMN IF NOT EXISTS streamtape_url TEXT;

-- 添加字段注释
COMMENT ON COLUMN download_tasks.index_url IS 'MixFile索引文件URL';
COMMENT ON COLUMN download_tasks.share_code IS 'MixFile分享码';
COMMENT ON COLUMN download_tasks.mixfile_mode IS '是否为MixFile模式';
COMMENT ON COLUMN download_tasks.doodstream_url IS 'DoodStream播放链接';
COMMENT ON COLUMN download_tasks.streamtape_url IS 'StreamTape播放链接';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_download_tasks_share_code ON download_tasks(share_code);
CREATE INDEX IF NOT EXISTS idx_download_tasks_doodstream_url ON download_tasks(doodstream_url);
CREATE INDEX IF NOT EXISTS idx_download_tasks_streamtape_url ON download_tasks(streamtape_url);