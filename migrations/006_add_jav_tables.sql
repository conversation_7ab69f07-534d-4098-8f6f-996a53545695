-- 添加JAV影片元数据相关表结构
-- 创建时间: 2025-06-27
-- 支持多源数据采集、磁力筛选、自动下载上传功能

-- JAV影片表
CREATE TABLE IF NOT EXISTS jav_movies (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    title VARCHAR(500),
    title_en VARCHAR(500),
    release_date DATE,
    duration INTEGER,
    studio VARCHAR(200),
    series VARCHAR(200),
    director VA<PERSON><PERSON><PERSON>(200),
    cover_url TEXT,
    poster_url TEXT,
    plot TEXT,
    plot_en TEXT,
    rating DECIMAL(3,1),
    selected_magnet_url TEXT,
    streamtape_url TEXT,
    streamhg_url TEXT,
    download_task_id INTEGER REFERENCES download_tasks(id) ON DELETE SET NULL,
    scraping_status VARCHAR(20) DEFAULT 'pending',
    scraping_source VARCHAR(50),
    last_scraped_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- JAV演员表
CREATE TABLE IF NOT EXISTS jav_actors (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    name_en VARCHAR(200),
    name_jp VARCHAR(200),
    avatar_url TEXT,
    birth_date DATE,
    height INTEGER,
    bust INTEGER,
    waist INTEGER,
    hip INTEGER,
    blood_type VARCHAR(5),
    hobby TEXT,
    debut_date DATE,
    active_status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- JAV磁力链接表
CREATE TABLE IF NOT EXISTS jav_magnets (
    id SERIAL PRIMARY KEY,
    movie_id INTEGER NOT NULL REFERENCES jav_movies(id) ON DELETE CASCADE,
    magnet_url TEXT NOT NULL,
    file_name VARCHAR(500),
    file_size BIGINT,
    quality VARCHAR(20),
    has_subtitle BOOLEAN DEFAULT FALSE,
    subtitle_language VARCHAR(10),
    source VARCHAR(100),
    uploader VARCHAR(100),
    upload_date TIMESTAMP,
    seeders INTEGER DEFAULT 0,
    leechers INTEGER DEFAULT 0,
    score DECIMAL(5,2) DEFAULT 0,
    size_score DECIMAL(5,2) DEFAULT 0,
    subtitle_score DECIMAL(5,2) DEFAULT 0,
    quality_score DECIMAL(5,2) DEFAULT 0,
    source_score DECIMAL(5,2) DEFAULT 0,
    is_selected BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- JAV影片演员关联表
CREATE TABLE IF NOT EXISTS jav_movie_actors (
    id SERIAL PRIMARY KEY,
    movie_id INTEGER NOT NULL REFERENCES jav_movies(id) ON DELETE CASCADE,
    actor_id INTEGER NOT NULL REFERENCES jav_actors(id) ON DELETE CASCADE,
    role_type VARCHAR(20) DEFAULT 'actress',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(movie_id, actor_id)
);

-- JAV分类标签表
CREATE TABLE IF NOT EXISTS jav_genres (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    name_en VARCHAR(100),
    name_jp VARCHAR(100),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- JAV影片分类关联表
CREATE TABLE IF NOT EXISTS jav_movie_genres (
    id SERIAL PRIMARY KEY,
    movie_id INTEGER NOT NULL REFERENCES jav_movies(id) ON DELETE CASCADE,
    genre_id INTEGER NOT NULL REFERENCES jav_genres(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(movie_id, genre_id)
);

-- JAV数据采集日志表
CREATE TABLE IF NOT EXISTS jav_scraping_logs (
    id SERIAL PRIMARY KEY,
    movie_code VARCHAR(50),
    source VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    data_found BOOLEAN DEFAULT FALSE,
    error_message TEXT,
    response_time INTEGER,
    scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能

-- jav_movies表索引
CREATE INDEX IF NOT EXISTS idx_jav_movies_code ON jav_movies(code);
CREATE INDEX IF NOT EXISTS idx_jav_movies_title ON jav_movies(title);
CREATE INDEX IF NOT EXISTS idx_jav_movies_studio ON jav_movies(studio);
CREATE INDEX IF NOT EXISTS idx_jav_movies_series ON jav_movies(series);
CREATE INDEX IF NOT EXISTS idx_jav_movies_release_date ON jav_movies(release_date);
CREATE INDEX IF NOT EXISTS idx_jav_movies_scraping_status ON jav_movies(scraping_status);
CREATE INDEX IF NOT EXISTS idx_jav_movies_download_task_id ON jav_movies(download_task_id);
CREATE INDEX IF NOT EXISTS idx_jav_movies_created_at ON jav_movies(created_at);
CREATE INDEX IF NOT EXISTS idx_jav_movies_deleted_at ON jav_movies(deleted_at);

-- jav_actors表索引
CREATE INDEX IF NOT EXISTS idx_jav_actors_name ON jav_actors(name);
CREATE INDEX IF NOT EXISTS idx_jav_actors_name_en ON jav_actors(name_en);
CREATE INDEX IF NOT EXISTS idx_jav_actors_active_status ON jav_actors(active_status);
CREATE INDEX IF NOT EXISTS idx_jav_actors_deleted_at ON jav_actors(deleted_at);

-- jav_magnets表索引
CREATE INDEX IF NOT EXISTS idx_jav_magnets_movie_id ON jav_magnets(movie_id);
CREATE INDEX IF NOT EXISTS idx_jav_magnets_score ON jav_magnets(score DESC);
CREATE INDEX IF NOT EXISTS idx_jav_magnets_file_size ON jav_magnets(file_size DESC);
CREATE INDEX IF NOT EXISTS idx_jav_magnets_has_subtitle ON jav_magnets(has_subtitle);
CREATE INDEX IF NOT EXISTS idx_jav_magnets_is_selected ON jav_magnets(is_selected);
CREATE INDEX IF NOT EXISTS idx_jav_magnets_quality ON jav_magnets(quality);

-- jav_movie_actors表索引
CREATE INDEX IF NOT EXISTS idx_jav_movie_actors_movie_id ON jav_movie_actors(movie_id);
CREATE INDEX IF NOT EXISTS idx_jav_movie_actors_actor_id ON jav_movie_actors(actor_id);

-- jav_movie_genres表索引
CREATE INDEX IF NOT EXISTS idx_jav_movie_genres_movie_id ON jav_movie_genres(movie_id);
CREATE INDEX IF NOT EXISTS idx_jav_movie_genres_genre_id ON jav_movie_genres(genre_id);

-- jav_scraping_logs表索引
CREATE INDEX IF NOT EXISTS idx_jav_scraping_logs_movie_code ON jav_scraping_logs(movie_code);
CREATE INDEX IF NOT EXISTS idx_jav_scraping_logs_source ON jav_scraping_logs(source);
CREATE INDEX IF NOT EXISTS idx_jav_scraping_logs_status ON jav_scraping_logs(status);
CREATE INDEX IF NOT EXISTS idx_jav_scraping_logs_scraped_at ON jav_scraping_logs(scraped_at);

-- 添加字段注释

-- jav_movies表字段注释
COMMENT ON TABLE jav_movies IS 'JAV影片元数据表';
COMMENT ON COLUMN jav_movies.code IS '影片番号，唯一标识';
COMMENT ON COLUMN jav_movies.title IS '影片标题（日文）';
COMMENT ON COLUMN jav_movies.title_en IS '影片标题（英文）';
COMMENT ON COLUMN jav_movies.release_date IS '发行日期';
COMMENT ON COLUMN jav_movies.duration IS '影片时长（分钟）';
COMMENT ON COLUMN jav_movies.studio IS '制作公司';
COMMENT ON COLUMN jav_movies.series IS '系列名称';
COMMENT ON COLUMN jav_movies.director IS '导演';
COMMENT ON COLUMN jav_movies.cover_url IS '封面图片URL';
COMMENT ON COLUMN jav_movies.poster_url IS '海报图片URL';
COMMENT ON COLUMN jav_movies.plot IS '剧情简介（日文）';
COMMENT ON COLUMN jav_movies.plot_en IS '剧情简介（英文）';
COMMENT ON COLUMN jav_movies.rating IS '评分（1-10）';
COMMENT ON COLUMN jav_movies.selected_magnet_url IS '筛选出的最优磁力链接';
COMMENT ON COLUMN jav_movies.streamtape_url IS 'StreamTape播放链接';
COMMENT ON COLUMN jav_movies.streamhg_url IS 'StreamHG播放链接';
COMMENT ON COLUMN jav_movies.download_task_id IS '关联的下载任务ID';
COMMENT ON COLUMN jav_movies.scraping_status IS '数据采集状态';
COMMENT ON COLUMN jav_movies.scraping_source IS '数据来源';
COMMENT ON COLUMN jav_movies.last_scraped_at IS '最后采集时间';

-- jav_actors表字段注释
COMMENT ON TABLE jav_actors IS 'JAV演员信息表';
COMMENT ON COLUMN jav_actors.name IS '演员姓名（日文）';
COMMENT ON COLUMN jav_actors.name_en IS '演员姓名（英文）';
COMMENT ON COLUMN jav_actors.name_jp IS '演员姓名（日文假名）';
COMMENT ON COLUMN jav_actors.avatar_url IS '头像图片URL';
COMMENT ON COLUMN jav_actors.birth_date IS '出生日期';
COMMENT ON COLUMN jav_actors.height IS '身高（cm）';
COMMENT ON COLUMN jav_actors.bust IS '胸围（cm）';
COMMENT ON COLUMN jav_actors.waist IS '腰围（cm）';
COMMENT ON COLUMN jav_actors.hip IS '臀围（cm）';
COMMENT ON COLUMN jav_actors.blood_type IS '血型';
COMMENT ON COLUMN jav_actors.hobby IS '兴趣爱好';
COMMENT ON COLUMN jav_actors.debut_date IS '出道日期';
COMMENT ON COLUMN jav_actors.active_status IS '活跃状态';

-- jav_magnets表字段注释
COMMENT ON TABLE jav_magnets IS 'JAV磁力链接表';
COMMENT ON COLUMN jav_magnets.movie_id IS '关联的影片ID';
COMMENT ON COLUMN jav_magnets.magnet_url IS '磁力链接URL';
COMMENT ON COLUMN jav_magnets.file_name IS '文件名';
COMMENT ON COLUMN jav_magnets.file_size IS '文件大小（字节）';
COMMENT ON COLUMN jav_magnets.quality IS '视频质量（720p, 1080p, 4K等）';
COMMENT ON COLUMN jav_magnets.has_subtitle IS '是否有字幕';
COMMENT ON COLUMN jav_magnets.subtitle_language IS '字幕语言';
COMMENT ON COLUMN jav_magnets.source IS '磁力来源';
COMMENT ON COLUMN jav_magnets.uploader IS '上传者';
COMMENT ON COLUMN jav_magnets.upload_date IS '上传日期';
COMMENT ON COLUMN jav_magnets.seeders IS '做种数';
COMMENT ON COLUMN jav_magnets.leechers IS '下载数';
COMMENT ON COLUMN jav_magnets.score IS '综合评分';
COMMENT ON COLUMN jav_magnets.size_score IS '文件大小评分';
COMMENT ON COLUMN jav_magnets.subtitle_score IS '字幕评分';
COMMENT ON COLUMN jav_magnets.quality_score IS '清晰度评分';
COMMENT ON COLUMN jav_magnets.source_score IS '来源可靠性评分';
COMMENT ON COLUMN jav_magnets.is_selected IS '是否为选中的磁力链接';

-- jav_movie_actors表字段注释
COMMENT ON TABLE jav_movie_actors IS 'JAV影片演员关联表';
COMMENT ON COLUMN jav_movie_actors.role_type IS '角色类型（actress, actor等）';

-- jav_genres表字段注释
COMMENT ON TABLE jav_genres IS 'JAV分类标签表';
COMMENT ON COLUMN jav_genres.name IS '分类名称（中文）';
COMMENT ON COLUMN jav_genres.name_en IS '分类名称（英文）';
COMMENT ON COLUMN jav_genres.name_jp IS '分类名称（日文）';

-- jav_movie_genres表字段注释
COMMENT ON TABLE jav_movie_genres IS 'JAV影片分类关联表';

-- jav_scraping_logs表字段注释
COMMENT ON TABLE jav_scraping_logs IS 'JAV数据采集日志表';
COMMENT ON COLUMN jav_scraping_logs.movie_code IS '影片番号';
COMMENT ON COLUMN jav_scraping_logs.source IS '数据源（javbus, javinizer, javsp）';
COMMENT ON COLUMN jav_scraping_logs.status IS '采集状态（success, failed, partial）';
COMMENT ON COLUMN jav_scraping_logs.data_found IS '是否找到数据';
COMMENT ON COLUMN jav_scraping_logs.error_message IS '错误信息';
COMMENT ON COLUMN jav_scraping_logs.response_time IS '响应时间（毫秒）';
COMMENT ON COLUMN jav_scraping_logs.scraped_at IS '采集时间';

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为JAV相关表创建更新时间触发器
CREATE TRIGGER update_jav_movies_updated_at BEFORE UPDATE ON jav_movies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_jav_actors_updated_at BEFORE UPDATE ON jav_actors
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_jav_magnets_updated_at BEFORE UPDATE ON jav_magnets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入一些常用的JAV分类标签
INSERT INTO jav_genres (name, name_en, name_jp) VALUES
('巨乳', 'Big Tits', '巨乳'),
('美少女', 'Beautiful Girl', '美少女'),
('熟女', 'Mature Woman', '熟女'),
('制服', 'Uniform', '制服'),
('丝袜', 'Pantyhose', 'パンスト'),
('中出', 'Creampie', '中出し'),
('口交', 'Blowjob', 'フェラ'),
('多人', 'Multiple', '複数'),
('按摩', 'Massage', 'マッサージ'),
('学生', 'Student', '学生'),
('OL', 'Office Lady', 'OL'),
('人妻', 'Married Woman', '人妻'),
('痴女', 'Slut', '痴女'),
('SM', 'SM', 'SM'),
('肛交', 'Anal', 'アナル')
ON CONFLICT (name) DO NOTHING;

-- 创建统计视图
CREATE OR REPLACE VIEW jav_movie_statistics AS
SELECT 
    scraping_status,
    COUNT(*) as count,
    COUNT(CASE WHEN selected_magnet_url IS NOT NULL THEN 1 END) as with_magnet,
    COUNT(CASE WHEN streamtape_url IS NOT NULL OR streamhg_url IS NOT NULL THEN 1 END) as with_streaming,
    AVG(rating) as avg_rating
FROM jav_movies 
WHERE deleted_at IS NULL
GROUP BY scraping_status;

CREATE OR REPLACE VIEW jav_actor_statistics AS
SELECT 
    active_status,
    COUNT(*) as count,
    COUNT(CASE WHEN avatar_url IS NOT NULL THEN 1 END) as with_avatar
FROM jav_actors 
WHERE deleted_at IS NULL
GROUP BY active_status;
