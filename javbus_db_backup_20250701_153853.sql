--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13 (Debian 15.13-0+deb12u1)
-- Dumped by pg_dump version 15.13 (Debian 15.13-0+deb12u1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: ImageType; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public."ImageType" AS ENUM (
    'COVER',
    'SAMPLE',
    'THUMBNAIL',
    'AVATAR'
);


ALTER TYPE public."ImageType" OWNER TO postgres;

--
-- Name: MovieType; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public."MovieType" AS ENUM (
    'NORMAL',
    'UNCENSORED'
);


ALTER TYPE public."MovieType" OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: crawl_tasks; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.crawl_tasks (
    id text NOT NULL,
    type text NOT NULL,
    status text DEFAULT 'pending'::text NOT NULL,
    params jsonb,
    result jsonb,
    error text,
    "startedAt" timestamp(3) without time zone,
    "completedAt" timestamp(3) without time zone,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.crawl_tasks OWNER TO postgres;

--
-- Name: directors; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.directors (
    id text NOT NULL,
    name text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.directors OWNER TO postgres;

--
-- Name: genres; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.genres (
    id text NOT NULL,
    name text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.genres OWNER TO postgres;

--
-- Name: images; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.images (
    id text NOT NULL,
    type public."ImageType" NOT NULL,
    "originalUrl" text NOT NULL,
    "localPath" text,
    width integer,
    height integer,
    "fileSize" integer,
    alt text,
    "movieId" text,
    "starId" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "thumbnailUrl" text,
    "thumbnailPath" text,
    "sampleId" text
);


ALTER TABLE public.images OWNER TO postgres;

--
-- Name: magnets; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.magnets (
    id text NOT NULL,
    "movieId" text NOT NULL,
    link text NOT NULL,
    title text NOT NULL,
    size text,
    "numberSize" bigint,
    "shareDate" timestamp(3) without time zone,
    "isHD" boolean DEFAULT false NOT NULL,
    "hasSubtitle" boolean DEFAULT false NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.magnets OWNER TO postgres;

--
-- Name: movie_genres; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.movie_genres (
    "movieId" text NOT NULL,
    "genreId" text NOT NULL
);


ALTER TABLE public.movie_genres OWNER TO postgres;

--
-- Name: movie_stars; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.movie_stars (
    "movieId" text NOT NULL,
    "starId" text NOT NULL
);


ALTER TABLE public.movie_stars OWNER TO postgres;

--
-- Name: movie_tags; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.movie_tags (
    "movieId" text NOT NULL,
    "tagId" text NOT NULL
);


ALTER TABLE public.movie_tags OWNER TO postgres;

--
-- Name: movies; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.movies (
    id text NOT NULL,
    title text NOT NULL,
    date timestamp(3) without time zone,
    "videoLength" integer,
    type public."MovieType" DEFAULT 'NORMAL'::public."MovieType" NOT NULL,
    gid text,
    uc text,
    "directorId" text,
    "producerId" text,
    "publisherId" text,
    "seriesId" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "imageWidth" integer DEFAULT 0,
    "imageHeight" integer DEFAULT 0
);


ALTER TABLE public.movies OWNER TO postgres;

--
-- Name: producers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.producers (
    id text NOT NULL,
    name text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.producers OWNER TO postgres;

--
-- Name: publishers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.publishers (
    id text NOT NULL,
    name text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.publishers OWNER TO postgres;

--
-- Name: series; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.series (
    id text NOT NULL,
    name text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.series OWNER TO postgres;

--
-- Name: similar_movies; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.similar_movies (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    "movieId" text NOT NULL,
    "similarMovieId" text NOT NULL,
    title text,
    img text,
    "createdAt" timestamp without time zone DEFAULT now() NOT NULL,
    "updatedAt" timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.similar_movies OWNER TO postgres;

--
-- Name: stars; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.stars (
    id text NOT NULL,
    name text NOT NULL,
    birthday timestamp(3) without time zone,
    age integer,
    height integer,
    bust text,
    waistline text,
    hipline text,
    birthplace text,
    hobby text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.stars OWNER TO postgres;

--
-- Name: tags; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.tags (
    id text NOT NULL,
    name text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.tags OWNER TO postgres;

--
-- Data for Name: crawl_tasks; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.crawl_tasks (id, type, status, params, result, error, "startedAt", "completedAt", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: directors; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.directors (id, name, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: genres; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.genres (id, name, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: images; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.images (id, type, "originalUrl", "localPath", width, height, "fileSize", alt, "movieId", "starId", "createdAt", "updatedAt", "thumbnailUrl", "thumbnailPath", "sampleId") FROM stdin;
\.


--
-- Data for Name: magnets; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.magnets (id, "movieId", link, title, size, "numberSize", "shareDate", "isHD", "hasSubtitle", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: movie_genres; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.movie_genres ("movieId", "genreId") FROM stdin;
\.


--
-- Data for Name: movie_stars; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.movie_stars ("movieId", "starId") FROM stdin;
\.


--
-- Data for Name: movie_tags; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.movie_tags ("movieId", "tagId") FROM stdin;
\.


--
-- Data for Name: movies; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.movies (id, title, date, "videoLength", type, gid, uc, "directorId", "producerId", "publisherId", "seriesId", "createdAt", "updatedAt", "imageWidth", "imageHeight") FROM stdin;
\.


--
-- Data for Name: producers; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.producers (id, name, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: publishers; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.publishers (id, name, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: series; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.series (id, name, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: similar_movies; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.similar_movies (id, "movieId", "similarMovieId", title, img, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: stars; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.stars (id, name, birthday, age, height, bust, waistline, hipline, birthplace, hobby, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: tags; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.tags (id, name, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Name: crawl_tasks crawl_tasks_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.crawl_tasks
    ADD CONSTRAINT crawl_tasks_pkey PRIMARY KEY (id);


--
-- Name: directors directors_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.directors
    ADD CONSTRAINT directors_pkey PRIMARY KEY (id);


--
-- Name: genres genres_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.genres
    ADD CONSTRAINT genres_pkey PRIMARY KEY (id);


--
-- Name: images images_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.images
    ADD CONSTRAINT images_pkey PRIMARY KEY (id);


--
-- Name: magnets magnets_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.magnets
    ADD CONSTRAINT magnets_pkey PRIMARY KEY (id);


--
-- Name: movie_genres movie_genres_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movie_genres
    ADD CONSTRAINT movie_genres_pkey PRIMARY KEY ("movieId", "genreId");


--
-- Name: movie_stars movie_stars_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movie_stars
    ADD CONSTRAINT movie_stars_pkey PRIMARY KEY ("movieId", "starId");


--
-- Name: movie_tags movie_tags_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movie_tags
    ADD CONSTRAINT movie_tags_pkey PRIMARY KEY ("movieId", "tagId");


--
-- Name: movies movies_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movies
    ADD CONSTRAINT movies_pkey PRIMARY KEY (id);


--
-- Name: producers producers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.producers
    ADD CONSTRAINT producers_pkey PRIMARY KEY (id);


--
-- Name: publishers publishers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.publishers
    ADD CONSTRAINT publishers_pkey PRIMARY KEY (id);


--
-- Name: series series_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.series
    ADD CONSTRAINT series_pkey PRIMARY KEY (id);


--
-- Name: similar_movies similar_movies_movieId_similarMovieId_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.similar_movies
    ADD CONSTRAINT "similar_movies_movieId_similarMovieId_key" UNIQUE ("movieId", "similarMovieId");


--
-- Name: similar_movies similar_movies_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.similar_movies
    ADD CONSTRAINT similar_movies_pkey PRIMARY KEY (id);


--
-- Name: stars stars_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.stars
    ADD CONSTRAINT stars_pkey PRIMARY KEY (id);


--
-- Name: tags tags_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tags
    ADD CONSTRAINT tags_pkey PRIMARY KEY (id);


--
-- Name: crawl_tasks_createdAt_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "crawl_tasks_createdAt_idx" ON public.crawl_tasks USING btree ("createdAt");


--
-- Name: crawl_tasks_status_type_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX crawl_tasks_status_type_idx ON public.crawl_tasks USING btree (status, type);


--
-- Name: images_movieId_type_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "images_movieId_type_idx" ON public.images USING btree ("movieId", type);


--
-- Name: images_starId_type_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "images_starId_type_idx" ON public.images USING btree ("starId", type);


--
-- Name: magnets_movieId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "magnets_movieId_idx" ON public.magnets USING btree ("movieId");


--
-- Name: tags_name_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX tags_name_key ON public.tags USING btree (name);


--
-- Name: images images_movieId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.images
    ADD CONSTRAINT "images_movieId_fkey" FOREIGN KEY ("movieId") REFERENCES public.movies(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: images images_starId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.images
    ADD CONSTRAINT "images_starId_fkey" FOREIGN KEY ("starId") REFERENCES public.stars(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: magnets magnets_movieId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.magnets
    ADD CONSTRAINT "magnets_movieId_fkey" FOREIGN KEY ("movieId") REFERENCES public.movies(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: movie_genres movie_genres_genreId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movie_genres
    ADD CONSTRAINT "movie_genres_genreId_fkey" FOREIGN KEY ("genreId") REFERENCES public.genres(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: movie_genres movie_genres_movieId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movie_genres
    ADD CONSTRAINT "movie_genres_movieId_fkey" FOREIGN KEY ("movieId") REFERENCES public.movies(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: movie_stars movie_stars_movieId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movie_stars
    ADD CONSTRAINT "movie_stars_movieId_fkey" FOREIGN KEY ("movieId") REFERENCES public.movies(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: movie_stars movie_stars_starId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movie_stars
    ADD CONSTRAINT "movie_stars_starId_fkey" FOREIGN KEY ("starId") REFERENCES public.stars(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: movie_tags movie_tags_movieId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movie_tags
    ADD CONSTRAINT "movie_tags_movieId_fkey" FOREIGN KEY ("movieId") REFERENCES public.movies(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: movie_tags movie_tags_tagId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movie_tags
    ADD CONSTRAINT "movie_tags_tagId_fkey" FOREIGN KEY ("tagId") REFERENCES public.tags(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: movies movies_directorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movies
    ADD CONSTRAINT "movies_directorId_fkey" FOREIGN KEY ("directorId") REFERENCES public.directors(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: movies movies_producerId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movies
    ADD CONSTRAINT "movies_producerId_fkey" FOREIGN KEY ("producerId") REFERENCES public.producers(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: movies movies_publisherId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movies
    ADD CONSTRAINT "movies_publisherId_fkey" FOREIGN KEY ("publisherId") REFERENCES public.publishers(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: movies movies_seriesId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movies
    ADD CONSTRAINT "movies_seriesId_fkey" FOREIGN KEY ("seriesId") REFERENCES public.series(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: pg_database_owner
--

GRANT ALL ON SCHEMA public TO javbus;


--
-- PostgreSQL database dump complete
--

