[Unit]
Description=Aria2 File Filter Service
Documentation=https://github.com/JAVAPI.COM/aria2-file-filter
After=network.target aria2.service docker.service
Wants=aria2.service
Requires=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/www/wwwroot/JAVAPI.COM/scripts
ExecStart=/usr/bin/python3 /www/wwwroot/JAVAPI.COM/scripts/aria2_file_filter.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=aria2-file-filter

# 环境变量
Environment=PYTHONPATH=/www/wwwroot/JAVAPI.COM
Environment=PYTHONUNBUFFERED=1

# 资源限制
LimitNOFILE=65536
MemoryMax=512M

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/www/wwwroot/JAVAPI.COM/logs
ReadWritePaths=/var/run

# 启动延迟（等待aria2服务完全启动）
ExecStartPre=/bin/sleep 30

[Install]
WantedBy=multi-user.target