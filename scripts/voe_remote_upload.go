package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	_ "github.com/lib/pq"
)

// VOE远程上传响应结构
type VOERemoteUploadResponse struct {
	ServerTime string `json:"server_time"`
	Msg        string `json:"msg"`
	Message    string `json:"message"`
	Status     int    `json:"status"`
	Success    bool   `json:"success"`
	Result     struct {
		FileCode string `json:"file_code"`
		QueueID  int    `json:"queueID"`
	} `json:"result"`
}

// 配置结构
type Config struct {
	DatabaseURL string
	VOEAPIKey   string
	VOEBaseURL  string
	BatchSize   int
	DelayMs     int
}

// 数据库记录结构
type DownloadTask struct {
	ID             int    `json:"id"`
	TaskName       string `json:"task_name"`
	StreamTapeURL  string `json:"stream_tape_url"`
	VOEURL         string `json:"voe_url"`
	ProcessingStatus string `json:"processing_status"`
}

func main() {
	// 读取配置
	config := loadConfig()
	
	// 连接数据库
	db, err := sql.Open("postgres", config.DatabaseURL)
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}
	defer db.Close()

	// 测试数据库连接
	if err := db.Ping(); err != nil {
		log.Fatalf("数据库连接测试失败: %v", err)
	}

	log.Println("🚀 VOE远程上传服务启动")
	log.Printf("📊 配置信息: BatchSize=%d, DelayMs=%d", config.BatchSize, config.DelayMs)

	// 获取需要上传的任务
	tasks, err := getTasksForVOEUpload(db)
	if err != nil {
		log.Fatalf("获取任务失败: %v", err)
	}

	if len(tasks) == 0 {
		log.Println("✅ 没有需要上传到VOE的任务")
		return
	}

	log.Printf("📋 找到 %d 个需要上传到VOE的任务", len(tasks))

	// 批量处理任务
	successCount := 0
	failCount := 0

	for i, task := range tasks {
		log.Printf("🔄 处理任务 %d/%d: %s", i+1, len(tasks), task.TaskName)

		// 执行VOE远程上传
		success := uploadToVOE(config, task)
		if success {
			// 更新数据库
			if updateTaskVOEURL(db, task.ID, task.VOEURL) {
				successCount++
				log.Printf("✅ 任务 %s 上传成功", task.TaskName)
			} else {
				failCount++
				log.Printf("❌ 任务 %s 数据库更新失败", task.TaskName)
			}
		} else {
			failCount++
			log.Printf("❌ 任务 %s 上传失败", task.TaskName)
		}

		// 延迟避免API限制
		if i < len(tasks)-1 {
			time.Sleep(time.Duration(config.DelayMs) * time.Millisecond)
		}
	}

	log.Printf("🎉 VOE远程上传完成: 成功 %d, 失败 %d", successCount, failCount)
}

// 加载配置
func loadConfig() *Config {
	config := &Config{
		DatabaseURL: getEnv("DATABASE_URL", "postgres://user:password@localhost/javapi?sslmode=disable"),
		VOEAPIKey:   getEnv("VOE_API_KEY", "dLSU0jXbJtKZIDrD0PXmY3QsIlNqylsuaQ7N9XVm9NHZAg60Uh8uWHcBcwRhKNEM"),
		VOEBaseURL:  getEnv("VOE_BASE_URL", "https://voe.sx"),
		BatchSize:   10,
		DelayMs:     1000, // 1秒延迟，符合VOE API限制
	}

	if config.VOEAPIKey == "" {
		log.Fatal("❌ VOE_API_KEY 环境变量未设置")
	}

	return config
}

// 获取环境变量
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// 获取需要上传到VOE的任务
func getTasksForVOEUpload(db *sql.DB) ([]DownloadTask, error) {
	query := `
		SELECT id, task_name, stream_tape_url, COALESCE(voe_url, '') as voe_url, processing_status
		FROM download_tasks 
		WHERE stream_tape_url IS NOT NULL 
		AND stream_tape_url != '' 
		AND (voe_url IS NULL OR voe_url = '')
		AND status = 'completed'
		ORDER BY completed_at DESC
		LIMIT 100
	`

	rows, err := db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询数据库失败: %v", err)
	}
	defer rows.Close()

	var tasks []DownloadTask
	for rows.Next() {
		var task DownloadTask
		err := rows.Scan(&task.ID, &task.TaskName, &task.StreamTapeURL, &task.VOEURL, &task.ProcessingStatus)
		if err != nil {
			log.Printf("⚠️ 扫描行失败: %v", err)
			continue
		}
		tasks = append(tasks, task)
	}

	return tasks, nil
}

// 上传到VOE
func uploadToVOE(config *Config, task DownloadTask) bool {
	// 验证StreamTape URL
	if !isValidStreamTapeURL(task.StreamTapeURL) {
		log.Printf("⚠️ 无效的StreamTape URL: %s", task.StreamTapeURL)
		return false
	}

	// 调用VOE远程上传API
	apiURL := fmt.Sprintf("%s/api/upload/url", config.VOEBaseURL)
	
	// 准备请求参数
	data := url.Values{}
	data.Set("key", config.VOEAPIKey)
	data.Set("url", task.StreamTapeURL)

	// 发送POST请求
	resp, err := http.PostForm(apiURL, data)
	if err != nil {
		log.Printf("❌ HTTP请求失败: %v", err)
		return false
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("❌ 读取响应失败: %v", err)
		return false
	}

	// 解析响应
	var voeResp VOERemoteUploadResponse
	if err := json.Unmarshal(body, &voeResp); err != nil {
		log.Printf("❌ 解析响应失败: %v, 响应内容: %s", err, string(body))
		return false
	}

	// 检查响应状态
	if !voeResp.Success {
		log.Printf("❌ VOE API返回错误: %s", voeResp.Message)
		return false
	}

	// 构建VOE播放URL
	voeURL := fmt.Sprintf("https://voe.sx/%s", voeResp.Result.FileCode)
	task.VOEURL = voeURL

	log.Printf("✅ VOE远程上传成功: %s -> %s", task.StreamTapeURL, voeURL)
	return true
}

// 验证StreamTape URL
func isValidStreamTapeURL(streamTapeURL string) bool {
	if streamTapeURL == "" {
		return false
	}
	
	// 检查是否包含streamtape.com域名
	return strings.Contains(streamTapeURL, "streamtape.com")
}

// 更新任务的VOE URL
func updateTaskVOEURL(db *sql.DB, taskID int, voeURL string) bool {
	query := `
		UPDATE download_tasks 
		SET voe_url = $1, updated_at = CURRENT_TIMESTAMP 
		WHERE id = $2
	`

	_, err := db.Exec(query, voeURL, taskID)
	if err != nil {
		log.Printf("❌ 更新数据库失败: %v", err)
		return false
	}

	return true
}