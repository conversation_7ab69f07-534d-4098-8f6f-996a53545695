[Unit]
Description=VOE Remote Upload Service
After=network.target postgresql.service

[Service]
Type=oneshot
User=root
WorkingDirectory=/www/wwwroot/JAVAPI.COM/scripts
Environment=DATABASE_URL=postgres://user:password@localhost:5432/javapi?sslmode=disable
Environment=VOE_API_KEY=dLSU0jXbJtKZIDrD0PXmY3QsIlNqylsuaQ7N9XVm9NHZAg60Uh8uWHcBcwRhKNEM
Environment=VOE_BASE_URL=https://voe.sx
ExecStart=/usr/local/go/bin/go run voe_remote_upload.go
StandardOutput=journal
StandardError=journal
SyslogIdentifier=voe-remote-upload

[Install]
WantedBy=multi-user.target