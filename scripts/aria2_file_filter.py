#!/usr/bin/env python3
"""
Aria2 文件过滤器
功能：监控aria2下载任务，自动过滤小于指定大小的文件
作者：JAVAPI.COM
"""

import json
import time
import requests
import logging
from typing import List, Dict, Any
from datetime import datetime

# 配置
ARIA2_RPC_URL = "http://localhost:6800/jsonrpc"
ARIA2_SECRET = "aria2secret"  # aria2 RPC密钥
MIN_FILE_SIZE_MB = 100  # 最小文件大小（MB）
CHECK_INTERVAL = 10  # 检查间隔（秒）
LOG_FILE = "/www/wwwroot/JAVAPI.COM/logs/aria2_filter.log"

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Aria2FileFilter:
    def __init__(self):
        self.session = requests.Session()
        self.session.timeout = 10
        self.processed_tasks = set()  # 已处理的任务GID
        
    def call_aria2(self, method: str, params: List[Any] = None) -> Dict:
        """调用aria2 RPC接口"""
        if params is None:
            params = []
        
        # 如果设置了secret，添加到参数中
        if ARIA2_SECRET:
            params = [f"token:{ARIA2_SECRET}"] + params
        
        payload = {
            "jsonrpc": "2.0",
            "id": "1",
            "method": method,
            "params": params
        }
        
        try:
            response = self.session.post(ARIA2_RPC_URL, json=payload)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Aria2 RPC调用失败: {e}")
            return {"error": str(e)}
    
    def get_active_downloads(self) -> List[Dict]:
        """获取活跃下载任务"""
        result = self.call_aria2("aria2.tellActive")
        if "result" in result:
            return result["result"]
        return []
    
    def get_waiting_downloads(self) -> List[Dict]:
        """获取等待中的下载任务"""
        result = self.call_aria2("aria2.tellWaiting", [0, 100])
        if "result" in result:
            return result["result"]
        return []
    
    def get_task_files(self, gid: str) -> List[Dict]:
        """获取任务的文件列表"""
        result = self.call_aria2("aria2.getFiles", [gid])
        if "result" in result:
            return result["result"]
        return []
    
    def change_file_selection(self, gid: str, file_indices: List[str]) -> bool:
        """更改文件选择（只下载指定的文件）"""
        if not file_indices:
            logger.warning(f"任务 {gid}: 没有符合条件的文件，暂停任务")
            self.pause_task(gid)
            return False
            
        # 构建select-file参数
        select_file = ",".join(file_indices)
        options = {"select-file": select_file}
        
        result = self.call_aria2("aria2.changeOption", [gid, options])
        if "result" in result:
            logger.info(f"任务 {gid}: 已更新文件选择，只下载 {len(file_indices)} 个大文件")
            return True
        else:
            logger.error(f"任务 {gid}: 更新文件选择失败 - {result}")
            return False
    
    def pause_task(self, gid: str) -> bool:
        """暂停任务"""
        result = self.call_aria2("aria2.pause", [gid])
        return "result" in result
    
    def remove_task(self, gid: str) -> bool:
        """移除任务"""
        result = self.call_aria2("aria2.remove", [gid])
        return "result" in result
    
    def format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def filter_task_files(self, task: Dict) -> None:
        """过滤任务文件"""
        gid = task["gid"]
        
        # 跳过已处理的任务
        if gid in self.processed_tasks:
            return
        
        # 只处理BitTorrent任务
        if "bittorrent" not in task:
            return
        
        # 获取任务文件列表
        files = self.get_task_files(gid)
        if not files:
            logger.debug(f"任务 {gid}: 暂无文件信息")
            return
        
        logger.info(f"任务 {gid}: 开始文件过滤，共 {len(files)} 个文件")
        
        # 分析文件大小
        large_files = []  # 大文件索引
        small_files = []  # 小文件信息
        total_size = 0
        large_size = 0
        
        for i, file_info in enumerate(files):
            file_size = int(file_info.get("length", 0))
            file_path = file_info.get("path", "")
            file_name = file_path.split("/")[-1] if file_path else f"文件{i+1}"
            
            total_size += file_size
            size_mb = file_size / (1024 * 1024)
            
            if size_mb >= MIN_FILE_SIZE_MB:
                large_files.append(str(i + 1))  # aria2文件索引从1开始
                large_size += file_size
                logger.info(f"  ✅ 保留: {file_name} ({self.format_size(file_size)})")
            else:
                small_files.append({
                    "name": file_name,
                    "size": file_size,
                    "index": i + 1
                })
                logger.info(f"  ❌ 跳过: {file_name} ({self.format_size(file_size)})")
        
        # 统计信息
        logger.info(f"任务 {gid}: 过滤完成")
        logger.info(f"  总文件: {len(files)} 个 ({self.format_size(total_size)})")
        logger.info(f"  保留: {len(large_files)} 个 ({self.format_size(large_size)})")
        logger.info(f"  跳过: {len(small_files)} 个 ({self.format_size(total_size - large_size)})")
        
        # 更新文件选择
        if large_files:
            success = self.change_file_selection(gid, large_files)
            if success:
                logger.info(f"任务 {gid}: 文件过滤成功，节省空间 {self.format_size(total_size - large_size)}")
        else:
            logger.warning(f"任务 {gid}: 没有符合条件的大文件，已暂停任务")
        
        # 标记为已处理
        self.processed_tasks.add(gid)
    
    def monitor_downloads(self) -> None:
        """监控下载任务"""
        logger.info("开始监控aria2下载任务...")
        logger.info(f"文件大小过滤阈值: {MIN_FILE_SIZE_MB} MB")
        
        while True:
            try:
                # 获取活跃和等待中的任务
                active_tasks = self.get_active_downloads()
                waiting_tasks = self.get_waiting_downloads()
                all_tasks = active_tasks + waiting_tasks
                
                if all_tasks:
                    logger.debug(f"发现 {len(all_tasks)} 个任务 (活跃: {len(active_tasks)}, 等待: {len(waiting_tasks)})")
                
                # 处理每个任务
                for task in all_tasks:
                    try:
                        self.filter_task_files(task)
                    except Exception as e:
                        logger.error(f"处理任务 {task.get('gid', 'unknown')} 失败: {e}")
                
                # 清理已完成的任务记录
                current_gids = {task["gid"] for task in all_tasks}
                self.processed_tasks = self.processed_tasks.intersection(current_gids)
                
            except Exception as e:
                logger.error(f"监控循环出错: {e}")
            
            time.sleep(CHECK_INTERVAL)
    
    def test_connection(self) -> bool:
        """测试aria2连接"""
        result = self.call_aria2("aria2.getVersion")
        if "result" in result:
            version = result["result"]["version"]
            logger.info(f"aria2连接成功，版本: {version}")
            return True
        else:
            logger.error("aria2连接失败")
            return False

def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("Aria2 文件过滤器启动")
    logger.info(f"最小文件大小: {MIN_FILE_SIZE_MB} MB")
    logger.info(f"检查间隔: {CHECK_INTERVAL} 秒")
    logger.info("=" * 50)
    
    filter_service = Aria2FileFilter()
    
    # 测试连接
    if not filter_service.test_connection():
        logger.error("无法连接到aria2，请检查配置")
        return
    
    try:
        filter_service.monitor_downloads()
    except KeyboardInterrupt:
        logger.info("收到停止信号，正在退出...")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")

if __name__ == "__main__":
    main()