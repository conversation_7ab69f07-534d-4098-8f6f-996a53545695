#!/usr/bin/env python3
"""
安全删除aria2中所有等待任务的脚本
"""

import requests
import json
import time

# aria2 配置
ARIA2_URL = "http://localhost:6800/jsonrpc"
ARIA2_TOKEN = "aria2secret"

def make_request(method, params):
    """发送aria2 RPC请求"""
    payload = {
        "jsonrpc": "2.0",
        "method": method,
        "params": [f"token:{ARIA2_TOKEN}"] + params,
        "id": "1"
    }
    
    try:
        response = requests.post(ARIA2_URL, json=payload, timeout=10)
        return response.json()
    except Exception as e:
        print(f"请求失败: {e}")
        return None

def get_global_stat():
    """获取aria2全局状态"""
    result = make_request("aria2.getGlobalStat", [])
    if result and "result" in result:
        return result["result"]
    return None

def get_waiting_tasks(offset=0, num=1000):
    """获取等待任务列表"""
    result = make_request("aria2.tellWaiting", [offset, num])
    if result and "result" in result:
        return result["result"]
    return []

def remove_task(gid):
    """删除指定任务"""
    result = make_request("aria2.remove", [gid])
    return result and "result" in result

def main():
    print("🔍 检查aria2状态...")
    
    # 获取当前状态
    stat = get_global_stat()
    if not stat:
        print("❌ 无法获取aria2状态")
        return
    
    waiting_count = int(stat.get("numWaiting", 0))
    print(f"📊 当前等待任务数: {waiting_count}")
    
    if waiting_count == 0:
        print("✅ 没有等待任务需要删除")
        return
    
    # 确认删除
    print(f"⚠️  即将删除 {waiting_count} 个等待任务")
    confirm = input("确认删除吗? (输入 'YES' 确认): ")
    
    if confirm != "YES":
        print("❌ 操作已取消")
        return
    
    print("🗑️  开始删除等待任务...")
    
    deleted_count = 0
    batch_size = 1000
    
    while True:
        # 获取一批等待任务
        tasks = get_waiting_tasks(0, batch_size)
        if not tasks:
            break
        
        print(f"📦 处理 {len(tasks)} 个任务...")
        
        # 删除这批任务
        for task in tasks:
            gid = task.get("gid")
            if gid:
                if remove_task(gid):
                    deleted_count += 1
                    if deleted_count % 100 == 0:
                        print(f"✅ 已删除 {deleted_count} 个任务")
                else:
                    print(f"❌ 删除任务 {gid} 失败")
        
        # 短暂休息避免过载
        time.sleep(0.1)
    
    print(f"🎉 删除完成! 总共删除了 {deleted_count} 个等待任务")
    
    # 最终状态检查
    final_stat = get_global_stat()
    if final_stat:
        final_waiting = int(final_stat.get("numWaiting", 0))
        print(f"📊 最终等待任务数: {final_waiting}")

if __name__ == "__main__":
    main()