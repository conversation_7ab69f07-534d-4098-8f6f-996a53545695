# VOE远程上传服务

## 概述

VOE远程上传服务是一个独立的Go脚本，用于将StreamTape上传成功的视频链接远程上传到VOE平台。该服务会定期扫描数据库中的StreamTape链接，并使用VOE的远程上传API将这些链接上传到VOE。

## 功能特点

- ✅ **自动化处理**: 定期扫描数据库，自动处理新的StreamTape链接
- ✅ **远程上传**: 使用VOE API远程上传，无需重新下载文件
- ✅ **批量处理**: 支持批量处理多个链接
- ✅ **错误处理**: 完善的错误处理和重试机制
- ✅ **API限制**: 遵守VOE API的速率限制（3-4请求/秒）
- ✅ **日志记录**: 详细的日志记录和状态跟踪
- ✅ **系统服务**: 作为systemd服务运行，支持自动启动

## 文件结构

```
scripts/
├── voe_remote_upload.go          # 主程序文件
├── voe_remote_upload.env         # 环境变量配置
├── voe-remote-upload.service     # systemd服务文件
├── voe-remote-upload.timer       # systemd定时器文件
├── install_voe_service.sh        # 安装脚本
└── VOE_REMOTE_UPLOAD_README.md   # 说明文档
```

## 安装和配置

### 1. 配置环境变量

编辑 `voe_remote_upload.env` 文件：

```bash
# 数据库连接字符串
DATABASE_URL=postgres://user:password@localhost:5432/javapi?sslmode=disable

# VOE API配置
VOE_API_KEY=dLSU0jXbJtKZIDrD0PXmY3QsIlNqylsuaQ7N9XVm9NHZAg60Uh8uWHcBcwRhKNEM
VOE_BASE_URL=https://voe.sx

# 批处理配置
BATCH_SIZE=10
DELAY_MS=1000
```

### 2. 安装系统服务

运行安装脚本：

```bash
cd /www/wwwroot/JAVAPI.COM/scripts
./install_voe_service.sh
```

### 3. 验证安装

检查服务状态：

```bash
# 查看定时器状态
sudo systemctl status voe-remote-upload.timer

# 查看服务日志
sudo journalctl -u voe-remote-upload.service -f
```

## 使用方法

### 自动运行

服务安装后会自动每30分钟运行一次，无需手动干预。

### 手动运行

```bash
# 手动运行一次
sudo systemctl start voe-remote-upload.service

# 或者直接运行Go程序
cd /www/wwwroot/JAVAPI.COM/scripts
go run voe_remote_upload.go
```

### 管理服务

```bash
# 启动定时器
sudo systemctl start voe-remote-upload.timer

# 停止定时器
sudo systemctl stop voe-remote-upload.timer

# 启用开机自启
sudo systemctl enable voe-remote-upload.timer

# 禁用开机自启
sudo systemctl disable voe-remote-upload.timer

# 查看定时器列表
sudo systemctl list-timers | grep voe
```

## 工作原理

1. **数据库扫描**: 脚本会扫描 `download_tasks` 表，查找满足以下条件的记录：
   - `stream_tape_url` 不为空
   - `voe_url` 为空或NULL
   - `status` 为 'completed'

2. **VOE远程上传**: 对于每个找到的StreamTape链接：
   - 调用VOE远程上传API: `POST https://voe.sx/api/upload/url`
   - 传递参数: `key`（API密钥）和 `url`（StreamTape链接）
   - 解析响应获取 `file_code`

3. **数据库更新**: 上传成功后：
   - 构建VOE播放链接: `https://voe.sx/{file_code}`
   - 更新数据库记录的 `voe_url` 字段

4. **速率限制**: 遵守VOE API限制，每次请求间隔1秒

## API文档参考

VOE远程上传API文档: https://voe.sx/api-1-reference-index#upload-remote

### 请求格式

```
POST https://voe.sx/api/upload/url
Content-Type: application/x-www-form-urlencoded

key=YOUR_API_KEY&url=STREAMTAPE_URL
```

### 响应格式

```json
{
  "server_time": "2024-01-01 12:00:00",
  "msg": "success",
  "status": 200,
  "success": true,
  "result": {
    "file_code": "abc123def456",
    "queueID": 12345
  }
}
```

## 日志和监控

### 查看日志

```bash
# 实时查看服务日志
sudo journalctl -u voe-remote-upload.service -f

# 查看最近的日志
sudo journalctl -u voe-remote-upload.service --since "1 hour ago"

# 查看定时器日志
sudo journalctl -u voe-remote-upload.timer -f
```

### 日志内容示例

```
🚀 VOE远程上传服务启动
📊 配置信息: BatchSize=10, DelayMs=1000
📋 找到 5 个需要上传到VOE的任务
🔄 处理任务 1/5: example_video.mp4
✅ VOE远程上传成功: https://streamtape.com/e/abc123 -> https://voe.sx/def456
✅ 任务 example_video.mp4 上传成功
🎉 VOE远程上传完成: 成功 5, 失败 0
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `DATABASE_URL` 配置
   - 确认PostgreSQL服务运行正常
   - 验证数据库用户权限

2. **VOE API错误**
   - 检查 `VOE_API_KEY` 是否正确
   - 确认网络连接正常
   - 检查API速率限制

3. **服务未运行**
   - 检查systemd服务状态
   - 查看服务日志排查错误
   - 确认Go环境正确安装

### 调试模式

临时启用详细日志：

```bash
# 设置环境变量后运行
export LOG_LEVEL=debug
go run voe_remote_upload.go
```

## 性能优化

- **批处理大小**: 默认每次处理10个任务，可根据需要调整
- **请求间隔**: 默认1秒间隔，确保不超过VOE API限制
- **定时器频率**: 默认30分钟运行一次，可根据需要调整

## 安全注意事项

- VOE API密钥应妥善保管，不要泄露
- 数据库连接字符串包含敏感信息，注意文件权限
- 建议定期轮换API密钥

## 更新和维护

### 更新脚本

1. 修改 `voe_remote_upload.go` 文件
2. 重启服务: `sudo systemctl restart voe-remote-upload.timer`

### 卸载服务

```bash
# 停止并禁用服务
sudo systemctl stop voe-remote-upload.timer
sudo systemctl disable voe-remote-upload.timer

# 删除服务文件
sudo rm /etc/systemd/system/voe-remote-upload.service
sudo rm /etc/systemd/system/voe-remote-upload.timer

# 重新加载systemd
sudo systemctl daemon-reload
```