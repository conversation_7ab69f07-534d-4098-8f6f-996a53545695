package main

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// 简化的模型定义
type JAVMovie struct {
	ID             uint      `gorm:"primaryKey"`
	Code           string    `gorm:"uniqueIndex;size:50;not null"`
	Title          string    `gorm:"size:500"`
	TitleEn        string    `gorm:"size:500"`
	Studio         string    `gorm:"size:200"`
	ReleaseDate    *time.Time
	Duration       int
	Rating         float64
	Plot           string    `gorm:"type:text"`
	PlotEn         string    `gorm:"type:text"`
	CoverURL       string    `gorm:"size:1000"`
	PosterURL      string    `gorm:"size:1000"`
	ScrapingStatus string    `gorm:"size:50;default:'pending'"`
	ScrapingSource string    `gorm:"size:50"`
	CreatedAt      time.Time
	UpdatedAt      time.Time
}

type JAVActor struct {
	ID        uint      `gorm:"primaryKey"`
	Name      string    `gorm:"size:200;not null"`
	NameEn    string    `gorm:"size:200"`
	NameJp    string    `gorm:"size:200"`
	AvatarURL string    `gorm:"size:1000"`
	CreatedAt time.Time
	UpdatedAt time.Time
}

type JAVMagnet struct {
	ID               uint      `gorm:"primaryKey"`
	MovieID          uint      `gorm:"not null;index"`
	MagnetURL        string    `gorm:"type:text;not null"`
	FileName         string    `gorm:"size:500"`
	FileSize         int64
	Quality          string    `gorm:"size:20"`
	HasSubtitle      bool
	SubtitleLanguage string    `gorm:"size:20"`
	Source           string    `gorm:"size:50"`
	Uploader         string    `gorm:"size:200"`
	Seeders          int
	Leechers         int
	Score            float64
	CreatedAt        time.Time
	UpdatedAt        time.Time
}

type JAVGenre struct {
	ID        uint      `gorm:"primaryKey"`
	Name      string    `gorm:"size:100;not null"`
	NameEn    string    `gorm:"size:100"`
	CreatedAt time.Time
	UpdatedAt time.Time
}

// 关联表
type JAVMovieActor struct {
	MovieID uint `gorm:"primaryKey"`
	ActorID uint `gorm:"primaryKey"`
}

type JAVMovieGenre struct {
	MovieID uint `gorm:"primaryKey"`
	GenreID uint `gorm:"primaryKey"`
}

// HTTP客户端
type HTTPClient struct {
	client    *http.Client
	userAgent string
	rateLimit time.Duration
	lastReq   time.Time
}

func NewHTTPClient() *HTTPClient {
	return &HTTPClient{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
		userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
		rateLimit: 2 * time.Second,
	}
}

func (c *HTTPClient) Get(url string) ([]byte, error) {
	// 速率限制
	if time.Since(c.lastReq) < c.rateLimit {
		time.Sleep(c.rateLimit - time.Since(c.lastReq))
	}
	c.lastReq = time.Now()

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", c.userAgent)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	resp, err := c.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, resp.Status)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	return body, nil
}

// JavBus爬虫
type JavBusScraper struct {
	client  *HTTPClient
	baseURL string
}

func NewJavBusScraper() *JavBusScraper {
	return &JavBusScraper{
		client:  NewHTTPClient(),
		baseURL: "https://www.javbus.com",
	}
}

// 获取最新影片代码
func (j *JavBusScraper) GetLatestMovieCodes(limit int) ([]string, error) {
	fmt.Printf("🔍 正在获取JavBus最新影片列表...\n")
	
	body, err := j.client.Get(j.baseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch homepage: %w", err)
	}

	// 解析影片代码
	re := regexp.MustCompile(`<a class="movie-box" href="/([A-Z0-9-]+)"`)
	matches := re.FindAllStringSubmatch(string(body), -1)

	var codes []string
	for _, match := range matches {
		if len(match) > 1 {
			codes = append(codes, match[1])
			if len(codes) >= limit {
				break
			}
		}
	}

	fmt.Printf("✅ 找到 %d 个影片代码\n", len(codes))
	return codes, nil
}

// 采集影片详情
func (j *JavBusScraper) ScrapeMovie(code string) (*MovieInfo, error) {
	url := fmt.Sprintf("%s/%s", j.baseURL, code)
	fmt.Printf("  📡 采集: %s", code)
	
	body, err := j.client.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch movie page: %w", err)
	}

	html := string(body)
	movie := &MovieInfo{
		Code:   code,
		Source: "javbus",
	}

	// 解析标题
	if re := regexp.MustCompile(`<h3>([^<]+)</h3>`); re.MatchString(html) {
		matches := re.FindStringSubmatch(html)
		if len(matches) > 1 {
			movie.Title = strings.TrimSpace(matches[1])
		}
	}

	// 解析封面
	if re := regexp.MustCompile(`<a class="bigImage" href="([^"]+)"`); re.MatchString(html) {
		matches := re.FindStringSubmatch(html)
		if len(matches) > 1 {
			movie.CoverURL = matches[1]
		}
	}

	// 解析发布日期
	if re := regexp.MustCompile(`發行日期:</span>\s*([0-9-]+)`); re.MatchString(html) {
		matches := re.FindStringSubmatch(html)
		if len(matches) > 1 {
			if date, err := time.Parse("2006-01-02", matches[1]); err == nil {
				movie.ReleaseDate = &date
			}
		}
	}

	// 解析时长
	if re := regexp.MustCompile(`長度:</span>\s*(\d+)`); re.MatchString(html) {
		matches := re.FindStringSubmatch(html)
		if len(matches) > 1 {
			if duration, err := strconv.Atoi(matches[1]); err == nil {
				movie.Duration = duration
			}
		}
	}

	// 解析制作商
	if re := regexp.MustCompile(`製作商:</span>.*?<a[^>]*>([^<]+)</a>`); re.MatchString(html) {
		matches := re.FindStringSubmatch(html)
		if len(matches) > 1 {
			movie.Studio = strings.TrimSpace(matches[1])
		}
	}

	// 解析演员
	actorRe := regexp.MustCompile(`<a class="avatar-box" title="([^"]+)"`)
	actorMatches := actorRe.FindAllStringSubmatch(html, -1)
	for _, match := range actorMatches {
		if len(match) > 1 {
			movie.Actors = append(movie.Actors, ActorInfo{
				Name: strings.TrimSpace(match[1]),
			})
		}
	}

	// 解析分类
	genreRe := regexp.MustCompile(`<span class="genre"[^>]*><a[^>]*>([^<]+)</a></span>`)
	genreMatches := genreRe.FindAllStringSubmatch(html, -1)
	for _, match := range genreMatches {
		if len(match) > 1 {
			movie.Genres = append(movie.Genres, GenreInfo{
				Name: strings.TrimSpace(match[1]),
			})
		}
	}

	// 尝试获取磁力链接
	magnets, err := j.scrapeMagnets(code)
	if err == nil {
		movie.Magnets = magnets
	}

	return movie, nil
}

// 采集磁力链接
func (j *JavBusScraper) scrapeMagnets(code string) ([]MagnetInfo, error) {
	// 尝试从磁力页面获取
	magnetURL := fmt.Sprintf("%s/%s/magnet", j.baseURL, code)
	
	body, err := j.client.Get(magnetURL)
	if err != nil {
		// 如果磁力页面不存在，返回空列表而不是错误
		return []MagnetInfo{}, nil
	}

	html := string(body)
	var magnets []MagnetInfo

	// 解析磁力链接
	magnetRe := regexp.MustCompile(`<a href="(magnet:[^"]+)"[^>]*>([^<]+)</a>`)
	matches := magnetRe.FindAllStringSubmatch(html, -1)

	for _, match := range matches {
		if len(match) > 2 {
			magnet := MagnetInfo{
				MagnetURL: match[1],
				FileName:  strings.TrimSpace(match[2]),
				Source:    "javbus",
			}

			// 解析文件大小
			if sizeRe := regexp.MustCompile(`(\d+(?:\.\d+)?)\s*(GB|MB)`); sizeRe.MatchString(match[2]) {
				sizeMatches := sizeRe.FindStringSubmatch(match[2])
				if len(sizeMatches) > 2 {
					if size, err := strconv.ParseFloat(sizeMatches[1], 64); err == nil {
						if sizeMatches[2] == "GB" {
							magnet.FileSize = int64(size * 1024 * 1024 * 1024)
						} else {
							magnet.FileSize = int64(size * 1024 * 1024)
						}
					}
				}
			}

			// 判断质量
			fileName := strings.ToLower(magnet.FileName)
			if strings.Contains(fileName, "4k") {
				magnet.Quality = "4K"
			} else if strings.Contains(fileName, "1080p") {
				magnet.Quality = "1080p"
			} else if strings.Contains(fileName, "720p") {
				magnet.Quality = "720p"
			} else {
				magnet.Quality = "other"
			}

			// 判断字幕
			if strings.Contains(fileName, "中文") || strings.Contains(fileName, "字幕") || strings.Contains(fileName, "sub") {
				magnet.HasSubtitle = true
				magnet.SubtitleLanguage = "chinese"
			}

			// 计算评分
			magnet.Score = j.calculateMagnetScore(&magnet)

			magnets = append(magnets, magnet)
		}
	}

	return magnets, nil
}

// 计算磁力链接评分
func (j *JavBusScraper) calculateMagnetScore(magnet *MagnetInfo) float64 {
	score := 50.0

	// 根据清晰度加分
	switch magnet.Quality {
	case "4K":
		score += 30
	case "1080p":
		score += 20
	case "720p":
		score += 10
	}

	// 根据字幕加分
	if magnet.HasSubtitle {
		score += 15
		if magnet.SubtitleLanguage == "chinese" {
			score += 5
		}
	}

	// 根据文件大小合理性加分
	if magnet.FileSize > 0 {
		expectedSize := j.getExpectedFileSize(magnet.Quality)
		if magnet.FileSize >= expectedSize*8/10 && magnet.FileSize <= expectedSize*2 {
			score += 5
		}
	}

	return score
}

func (j *JavBusScraper) getExpectedFileSize(quality string) int64 {
	switch quality {
	case "4K":
		return 8 * 1024 * 1024 * 1024 // 8GB
	case "1080p":
		return 3 * 1024 * 1024 * 1024 // 3GB
	case "720p":
		return 1536 * 1024 * 1024 // 1.5GB
	default:
		return 1024 * 1024 * 1024 // 1GB
	}
}

// 数据结构
type MovieInfo struct {
	Code        string
	Title       string
	TitleEn     string
	Studio      string
	ReleaseDate *time.Time
	Duration    int
	Rating      float64
	Plot        string
	PlotEn      string
	CoverURL    string
	PosterURL   string
	Actors      []ActorInfo
	Genres      []GenreInfo
	Magnets     []MagnetInfo
	Source      string
}

type ActorInfo struct {
	Name   string
	NameEn string
	NameJp string
}

type GenreInfo struct {
	Name   string
	NameEn string
}

type MagnetInfo struct {
	MagnetURL        string
	FileName         string
	FileSize         int64
	Quality          string
	HasSubtitle      bool
	SubtitleLanguage string
	Source           string
	Uploader         string
	Seeders          int
	Leechers         int
	Score            float64
}

// 统计结构
type CollectionStats struct {
	Total       int
	Processed   int
	Success     int
	Failed      int
	NewMovies   int
	NewActors   int
	NewMagnets  int
	StartTime   time.Time
	EndTime     time.Time
}

func main() {
	fmt.Println("=== 真实JAV数据采集程序 ===")
	fmt.Println("正在初始化系统...")

	// 初始化数据库
	db, err := initDatabase()
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 创建爬虫
	scraper := NewJavBusScraper()

	fmt.Println("✅ 系统初始化完成")
	fmt.Println()

	// 开始真实数据采集
	demonstrateRealDataCollection(db, scraper)
}

func initDatabase() (*gorm.DB, error) {
	// 使用SQLite数据库
	db, err := gorm.Open(sqlite.Open("real_jav.db"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	// 自动迁移表结构
	err = db.AutoMigrate(
		&JAVMovie{},
		&JAVActor{},
		&JAVMagnet{},
		&JAVGenre{},
		&JAVMovieActor{},
		&JAVMovieGenre{},
	)
	if err != nil {
		return nil, err
	}

	return db, nil
}

func demonstrateRealDataCollection(db *gorm.DB, scraper *JavBusScraper) {
	fmt.Println("🎬 开始真实JAV数据采集")
	fmt.Println()

	// 获取最新影片代码
	movieCodes, err := scraper.GetLatestMovieCodes(20)
	if err != nil {
		log.Fatalf("获取影片列表失败: %v", err)
	}

	if len(movieCodes) == 0 {
		fmt.Println("❌ 未找到任何影片代码")
		return
	}

	fmt.Printf("📋 准备采集 %d 部真实影片\n", len(movieCodes))
	fmt.Println()

	// 统计信息
	stats := &CollectionStats{
		Total:     len(movieCodes),
		StartTime: time.Now(),
	}

	// 分批处理影片
	batchSize := 5
	for i := 0; i < len(movieCodes); i += batchSize {
		end := i + batchSize
		if end > len(movieCodes) {
			end = len(movieCodes)
		}

		batch := movieCodes[i:end]
		fmt.Printf("📦 处理批次 %d-%d (%d部影片)\n", i+1, end, len(batch))

		processRealBatch(db, scraper, batch, stats)

		fmt.Printf("✅ 批次 %d-%d 处理完成\n", i+1, end)
		fmt.Println()

		// 批次间休息
		if end < len(movieCodes) {
			fmt.Println("⏳ 休息5秒...")
			time.Sleep(5 * time.Second)
		}
	}

	// 显示最终统计
	showFinalStats(db, stats)
}

func processRealBatch(db *gorm.DB, scraper *JavBusScraper, movieCodes []string, stats *CollectionStats) {
	for _, code := range movieCodes {
		// 检查影片是否已存在
		var existingMovie JAVMovie
		result := db.Where("code = ?", code).First(&existingMovie)
		if result.Error == nil {
			fmt.Printf(" ⏭️  已存在，跳过\n")
			stats.Processed++
			continue
		}

		// 采集真实数据
		movieInfo, err := scraper.ScrapeMovie(code)
		if err != nil {
			fmt.Printf(" ❌ 采集失败: %v\n", err)
			stats.Failed++
			stats.Processed++
			continue
		}

		// 保存影片数据
		if err := saveRealMovieData(db, movieInfo, stats); err != nil {
			fmt.Printf(" ❌ 保存失败: %v\n", err)
			stats.Failed++
		} else {
			fmt.Printf(" ✅ 采集成功 (标题: %s)\n", movieInfo.Title)
			stats.Success++
		}

		stats.Processed++

		// 影片间休息
		time.Sleep(3 * time.Second)
	}
}

func saveRealMovieData(db *gorm.DB, movieInfo *MovieInfo, stats *CollectionStats) error {
	// 开始事务
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 保存影片
	movie := &JAVMovie{
		Code:           movieInfo.Code,
		Title:          movieInfo.Title,
		TitleEn:        movieInfo.TitleEn,
		Studio:         movieInfo.Studio,
		ReleaseDate:    movieInfo.ReleaseDate,
		Duration:       movieInfo.Duration,
		Rating:         movieInfo.Rating,
		Plot:           movieInfo.Plot,
		PlotEn:         movieInfo.PlotEn,
		CoverURL:       movieInfo.CoverURL,
		PosterURL:      movieInfo.PosterURL,
		ScrapingStatus: "completed",
		ScrapingSource: movieInfo.Source,
	}

	if err := tx.Create(movie).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create movie: %w", err)
	}
	stats.NewMovies++

	// 保存演员
	for _, actorInfo := range movieInfo.Actors {
		if actorInfo.Name == "" {
			continue
		}

		// 检查演员是否已存在
		var existingActor JAVActor
		result := tx.Where("name = ?", actorInfo.Name).First(&existingActor)
		
		var actor JAVActor
		if result.Error != nil {
			// 创建新演员
			actor = JAVActor{
				Name:   actorInfo.Name,
				NameEn: actorInfo.NameEn,
				NameJp: actorInfo.NameJp,
			}
			if err := tx.Create(&actor).Error; err != nil {
				continue
			}
			stats.NewActors++
		} else {
			actor = existingActor
		}

		// 创建影片-演员关联
		movieActor := JAVMovieActor{
			MovieID: movie.ID,
			ActorID: actor.ID,
		}
		tx.Create(&movieActor)
	}

	// 保存分类
	for _, genreInfo := range movieInfo.Genres {
		if genreInfo.Name == "" {
			continue
		}

		// 检查分类是否已存在
		var existingGenre JAVGenre
		result := tx.Where("name = ?", genreInfo.Name).First(&existingGenre)
		
		var genre JAVGenre
		if result.Error != nil {
			// 创建新分类
			genre = JAVGenre{
				Name:   genreInfo.Name,
				NameEn: genreInfo.NameEn,
			}
			if err := tx.Create(&genre).Error; err != nil {
				continue
			}
		} else {
			genre = existingGenre
		}

		// 创建影片-分类关联
		movieGenre := JAVMovieGenre{
			MovieID: movie.ID,
			GenreID: genre.ID,
		}
		tx.Create(&movieGenre)
	}

	// 保存磁力链接
	for _, magnetInfo := range movieInfo.Magnets {
		if magnetInfo.MagnetURL == "" {
			continue
		}

		magnet := &JAVMagnet{
			MovieID:          movie.ID,
			MagnetURL:        magnetInfo.MagnetURL,
			FileName:         magnetInfo.FileName,
			FileSize:         magnetInfo.FileSize,
			Quality:          magnetInfo.Quality,
			HasSubtitle:      magnetInfo.HasSubtitle,
			SubtitleLanguage: magnetInfo.SubtitleLanguage,
			Source:           magnetInfo.Source,
			Uploader:         magnetInfo.Uploader,
			Seeders:          magnetInfo.Seeders,
			Leechers:         magnetInfo.Leechers,
			Score:            magnetInfo.Score,
		}

		if err := tx.Create(magnet).Error; err != nil {
			continue
		}
		stats.NewMagnets++
	}

	// 提交事务
	return tx.Commit().Error
}

func showFinalStats(db *gorm.DB, stats *CollectionStats) {
	stats.EndTime = time.Now()
	duration := stats.EndTime.Sub(stats.StartTime)

	fmt.Println("📊 真实数据采集完成统计")
	fmt.Println(strings.Repeat("=", 50))
	fmt.Printf("总影片数: %d\n", stats.Total)
	fmt.Printf("已处理: %d\n", stats.Processed)
	fmt.Printf("成功: %d\n", stats.Success)
	fmt.Printf("失败: %d\n", stats.Failed)
	fmt.Printf("新增影片: %d\n", stats.NewMovies)
	fmt.Printf("新增演员: %d\n", stats.NewActors)
	fmt.Printf("新增磁力: %d\n", stats.NewMagnets)
	fmt.Printf("耗时: %v\n", duration)
	if stats.Processed > 0 {
		fmt.Printf("成功率: %.1f%%\n", float64(stats.Success)/float64(stats.Processed)*100)
	}
	fmt.Println()

	// 查询数据库统计
	showDatabaseStats(db)

	// 演示查询功能
	demonstrateRealQueries(db)

	fmt.Println("🎉 真实JAV数据采集完成！")
}

func showDatabaseStats(db *gorm.DB) {
	fmt.Println("💾 数据库统计")
	fmt.Println(strings.Repeat("-", 30))

	var movieCount, actorCount, genreCount, magnetCount int64

	db.Model(&JAVMovie{}).Count(&movieCount)
	db.Model(&JAVActor{}).Count(&actorCount)
	db.Model(&JAVGenre{}).Count(&genreCount)
	db.Model(&JAVMagnet{}).Count(&magnetCount)

	fmt.Printf("影片总数: %d\n", movieCount)
	fmt.Printf("演员总数: %d\n", actorCount)
	fmt.Printf("分类总数: %d\n", genreCount)
	fmt.Printf("磁力总数: %d\n", magnetCount)
	fmt.Println()
}

func demonstrateRealQueries(db *gorm.DB) {
	fmt.Println("🔍 真实数据查询演示")
	fmt.Println(strings.Repeat("-", 30))

	// 1. 查询最新影片
	fmt.Println("📋 最新5部影片:")
	var latestMovies []JAVMovie
	db.Order("created_at DESC").Limit(5).Find(&latestMovies)
	for i, movie := range latestMovies {
		fmt.Printf("  %d. %s - %s\n", i+1, movie.Code, movie.Title)
		if movie.Studio != "" {
			fmt.Printf("     制作商: %s\n", movie.Studio)
		}
		if movie.ReleaseDate != nil {
			fmt.Printf("     发布日期: %s\n", movie.ReleaseDate.Format("2006-01-02"))
		}
	}
	fmt.Println()

	// 2. 查询有磁力链接的影片
	fmt.Println("🧲 有磁力链接的影片:")
	var moviesWithMagnets []struct {
		Code        string
		Title       string
		MagnetCount int64
	}
	
	db.Table("ja_vmovies").
		Select("ja_vmovies.code, ja_vmovies.title, count(ja_vmagnets.id) as magnet_count").
		Joins("LEFT JOIN ja_vmagnets ON ja_vmovies.id = ja_vmagnets.movie_id").
		Group("ja_vmovies.id").
		Having("magnet_count > 0").
		Order("magnet_count DESC").
		Limit(5).
		Find(&moviesWithMagnets)
	
	for i, movie := range moviesWithMagnets {
		fmt.Printf("  %d. %s - %s (%d个磁力链接)\n", i+1, movie.Code, movie.Title, movie.MagnetCount)
	}
	fmt.Println()

	// 3. 查询磁力链接质量分布
	fmt.Println("📊 磁力链接质量分布:")
	var qualityStats []struct {
		Quality string
		Count   int64
	}
	db.Model(&JAVMagnet{}).Select("quality, count(*) as count").Group("quality").Find(&qualityStats)
	for _, stat := range qualityStats {
		fmt.Printf("  %s: %d个\n", stat.Quality, stat.Count)
	}
	fmt.Println()

	// 4. 查询制作商统计
	fmt.Println("🏢 制作商统计 (前5名):")
	var studioStats []struct {
		Studio string
		Count  int64
	}
	db.Model(&JAVMovie{}).
		Select("studio, count(*) as count").
		Where("studio != ''").
		Group("studio").
		Order("count DESC").
		Limit(5).
		Find(&studioStats)
	
	for i, stat := range studioStats {
		fmt.Printf("  %d. %s: %d部影片\n", i+1, stat.Studio, stat.Count)
	}
	fmt.Println()
}

